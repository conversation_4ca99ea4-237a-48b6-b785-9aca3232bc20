---
id: update-addresses-byid
title: "updateAddressesByid"
description: "updateAddressesByid"
sidebar_label: "updateAddressesByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVktz2zYQ/iuYPfVBW647vfDm2MnUnU7ridX24NFhRa5ExCCAAEspKof/PbMARVmR8jjmIhGLbx9YfPiAHhjXEconuKnrQDHCogCPAVtiCjLRg8WWoATMgPsaCtAWSvDIDRQQ6H2nA9VQcuiogFg11CKUPfDOi6O2TGsKUMDKhRY5m369hmFYZHeK/MrVO/GpnGWyLJ/ovdEVsnZ29i46K7aT4G75jiqGAnxwngJrijKr4x0ZYqlqgi6dM4QWhqGQxWgJjObhhd8KTaQCWLMRh0e34hzmtWXNu7v538mZ6QN/XxW9bNVPP39HpYl7oOidjTnX9dWV/NUUq6C9RBXfrqqEe4KOVHVB8y5Rb0kYKED5tBCqSO60xvsaSuh8jUwjbSm+2mlh5nHg85iWuHF1ZnAlFE5MLmGGXs9wD571E+MHkLrCZn8iumCghIbZx3I2M65C07jIZe9d4OGkij8FoHIAKGCDQePS5H6IR27ICjsjZ+O3q6tfpHPFp3lWgWJzUdPmAr2+xP+7QFtaRs0ULy3xSdo72pBxviXL++SfC9ohf2PQR8a1tuuvBTR6Q98Y8SG4uqtkMAVdHGjwKBzOrdqTYeKopIRRcBJnM6AYP97s1eaP/+aJWnIu3h7k5vUHbL1w9+gEiIgNonArl1KN9H4jy1K3GNTNw71sIoWY699cnyxp3ugoOOWD2+iaoiJbe6ctR7VyQXFDqnb8F7FKcQX67/WluufPebRoc98rDLGQX1Uh09oFTfux6ZYqdsupkJf2pXPP2q5jodDWqosUFHbckOVRNlJyNMZtY5qOip3yFESy1XTwoopd1SjMGBVorSPnqRTYuLW2hTI68qHYjKIgFkvb0VpLww+gFy5HKzO5cNX5NFUTozZRLXfHyW3XLikUKhKGqtmHFViLz5Rqa11NplBVIDxkOtezE8CheUlPZG40qcjIXcxbYCsy5kuRUxX7jTyzB0rbynS1zHqMcetCrQJF4phcqUVt1IaCXu03DYYCvIvcYhL88abOqqcm2VPntHE6Rek28wa1lWjpMPejIj4Beg0FTJoIBZSHd8CiAJE9wfX9EiP9E8wwiPl9R0EUfHFQuySctY7yXU+3xyclTbc//PB2fFb8qKA4X+poRLtLomo6GUEBz7Q7eq4Mi6GAhrBON0k/zt/mVBdziXLwP3l0iBhNF8bDzfz2dxGY8bUinIISAm7lIYTbXIDLGy6AZOvBoF13uBZsDpqu1U56fLhxj9UrlXp2iX2fEXP3THYYphWzjGW1w/ARMMN5AQ==
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAddressesByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/addresses/{addressId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAddressesByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"addressId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      