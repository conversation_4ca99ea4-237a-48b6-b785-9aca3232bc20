---
id: forgot-password
title: "forgotPassword"
description: "forgotPassword"
sidebar_label: "forgotPassword"
hide_title: true
hide_table_of_contents: true
api: eJzVVk2T2zYM/SscHFvterMzvejUNOnObCfTeLJuc9jxAZZgiVmKZAjSG1ej/94B6a+1kzbXXGyJfHgACOBRIzhPAaN29r6FGtYudC7OkfnZhRYqiNgx1I/wumlcshGWFQT6nIjjb67dQj1C42wkG+URvTe6yWyzT+ysrHHT04DyJHY6UCt0NKA2Qha3nqAGt/pETYQKfJCAoiYWkwKrRxi0fUe2iz3Urw5GHIO2HVQS9YAR6h1+mirAttUSB5r5CeMaDVMFUUcjBHcvsv1Q8nq7eJ8ZIn2JP34WpyX56ecfPB3hCMTeWS6h3N7cyF9L3ATthRpqeEhNQ8zZI1OTgo5bqB9HWBEGClA/LqdldWZ00fgDxd7JSHjH+QxQ8oQZej3bDcPs7tyIKWwocPaWgoEa+hg917OZcQ2a3nGsR+9CnOA8gHcCUIUAKthg0LgyJU2xKHmuMRk5019ubl7JgVTnftaBuL9qaXOFXl/jPynQM61YR+JrS/HC7VvakHF+IBv3zr9FmjB+J+lDxE7b7v8Ijd7QdzLOg2tTIy8H0uWxug/SzuWo9jUe9/0oLqUyGQL1HlDtHu72XfrHx0XuGBmRD0eF+/0LDl768tD3kJjCr1TWrxs3wFSBtmuXne6bWBJUbzCo1/N7KScFLplsbi+SW/SaBad8cBvdEiuyrXfaRlZrF1TsSbUu/klRZV6B/n17re7jtywGtKUCDQau5Fc1GKlzQdP+3aSV4rQ6BHK6vnLuSduOK4W2VZKwwhR7snGnJdk5GuOeOW+zik55CjL06nClsOLU9AoLRgXqNMeylYmN67StlNEcj8EWFImKKEvPu9WWDJ2ATkxeZGZK4Cr5vNVSRG1YrbYvnds0rChUiglD0+9pBTbgE+XYBteSqVQTCI+evnZmF4Dj4SXflr3dkuKIMXEpgW3ImP9izlHsC/mVGihtG5Na2fU7CVKBmCJn09yuakNBr/dFk04VORsw3wIW80QU6VMnMvaiPQ+TlC9Eb1Bb4ckDPe5k8RHQa6gOXwnVmYrLpSLiJ8hxXCHTX8FMkyx/ThREnpdHzcsKXUFP2Ga5HuGJtlDDm/KlcbWQeARuksR18dUh0nCQ7/n7h4VM++5rRcoKNQR8hir/1gAVuHLm+UKUtREM2i5hJ9jCme+vJMkeDuRMSnKkuy2025MIx7EgFu6J7CTaXxKK8g7TcpqmfwHm41/Y
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"forgotPassword"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/ForgotPassword"}
  context={"endpoint"}
>
  
</MethodEndpoint>



forgotPassword

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["email"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"}},"additionalProperties":false,"title":"ForgotPasswordRequestDTO"}},"text/json":{"schema":{"required":["email"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"}},"additionalProperties":false,"title":"ForgotPasswordRequestDTO"}},"application/*+json":{"schema":{"required":["email"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"}},"additionalProperties":false,"title":"ForgotPasswordRequestDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      