---
id: get-valeters-byid
title: "getValetersByid"
description: "getValetersByid"
sidebar_label: "getValetersByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVUtv5DYM/isCTy3gzaQpevGtry1SFMWiSbeHYA4cm2MLkSWtSDmdGv7vBf2YRzLb7mU8EsmP5MeHBhBsGMon+IiOhBJsC4iYsNODCgbw2BGU0M8K9zUUYD2UEFFaKCDRp2wT1VBKylQAVy11COUAcohqaL1QQwkK2IfUocxX397BOBZH9BhYqlATr+ifMqUDXIFjSdY3MI5b9c0xeCZW+d3trX5q4irZKDYozEOuKmJWX8BU5WTlMGW1I0yUoHzaKlCIlFBN7msooSFZ6OAfDlYTvgR9K+9I2rCYQjFTU8IGo90svPFmODI4al6U+pXhnByU0IpELjcbFyp0bWAphxiSjG/c/6YKZgaAAnpMFnduJkEtZhb2mJ1y/d3t7TcwTlxf+tkn4vZdTf07jPYG/8mJXmjHVohv/JTGpdufqCcXYkdeVuefA80oXwj6INhY3/wfoLM9fSHihxTqXOnhCLo91f5B22mmau2AY2epy7Xj9LwoFMuf92v3/vrX49RP1u/DZG7Fqf57DdX8iMl8/+FeC0OJ55j6uzdhPraWVc/EFHpbExvydQzWC5t9SEZaMnWQ30nMhKuqH+9uzL18zqJDP3NZYeJCf02FQk1IltazyzvDeXcM5Px+F8Kz9Q0XBn1tMlMymKUlL7aaZmNyjs6FF57EbCSYSEnH2hwniA3nqjU465hEjWWZRROwC431hXGW5RTsrEU62cbTy3Jbk6MzpTOTi8zcHLjJcRLVJGgdm93h0rnP3Y5SYZgwVe0Kq2odPtMUWxdqcoWpEuHJ0zXO3iicyMuxnmXLlWFByTyXwFfk3H8hT1GshbxSA2N95XKt0ojMLyHVJhGT8GRKHVpnekp2vxYNxmLarh16bdZl3zYkZt1h5tqSO06F0N+yiQ6tV6RpOIdlwT0BRjttoBkICihPz8S2AN1iqjYMO2T6M7lx1Ot5t+vmqy3r6qqh3KNjKuCZDq9egx5d1jimMV533XXjVwlUwQt5ndiv/lgeqa8NFNcTWy7RH859rgGdshq349m6/+XnRyhAS3TG2Ku9McV6FX4YZo3H8Ex+HI/eRM/qaRz/BQlUrpk=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getValetersByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/valeters/{valeterId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getValetersByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"valeterId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}},{"name":"postcodes","in":"query","schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      