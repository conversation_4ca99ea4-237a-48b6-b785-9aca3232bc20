#!/bin/bash

# Test script for queue worker health server
# This script tests the health server functionality locally

set -e

# Configuration
PORT=${PORT:-8080}
TEST_TIMEOUT=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test the health server PHP script
test_health_server() {
    log_info "Testing health server PHP script..."
    
    # Create the health server script (same as in entrypoint)
    cat > /tmp/test-health-server.php << 'EOF'
<?php
$port = $_SERVER['PORT'] ?? 8080;
$host = '0.0.0.0';

echo "Starting health check server on $host:$port\n";

$socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
if ($socket === false) {
    die("socket_create() failed: " . socket_strerror(socket_last_error()) . "\n");
}

socket_set_option($socket, SOL_SOCKET, SO_REUSEADDR, 1);

if (socket_bind($socket, $host, $port) === false) {
    die("socket_bind() failed: " . socket_strerror(socket_last_error($socket)) . "\n");
}

if (socket_listen($socket, 5) === false) {
    die("socket_listen() failed: " . socket_strerror(socket_last_error($socket)) . "\n");
}

echo "Health server listening on $host:$port\n";

// Handle shutdown gracefully
function shutdown($socket) {
    echo "Shutting down health server...\n";
    socket_close($socket);
    exit(0);
}

// Set up signal handlers
pcntl_signal(SIGTERM, function() use ($socket) { shutdown($socket); });
pcntl_signal(SIGINT, function() use ($socket) { shutdown($socket); });

$request_count = 0;
$max_requests = 5; // Limit for testing

while ($request_count < $max_requests) {
    pcntl_signal_dispatch();
    
    $client = socket_accept($socket);
    if ($client === false) continue;
    
    $request = socket_read($client, 1024);
    echo "Received request " . ($request_count + 1) . "\n";
    
    // Simple health check response
    $response_body = '{"status":"healthy","service":"queue-worker","timestamp":"' . date('c') . '"}';
    $response = "HTTP/1.1 200 OK\r\n";
    $response .= "Content-Type: application/json\r\n";
    $response .= "Content-Length: " . strlen($response_body) . "\r\n";
    $response .= "Connection: close\r\n\r\n";
    $response .= $response_body;
    
    socket_write($client, $response);
    socket_close($client);
    
    $request_count++;
}

echo "Test completed after $request_count requests\n";
shutdown($socket);
EOF

    # Start the health server in background
    log_info "Starting health server on port $PORT..."
    PORT=$PORT php /tmp/test-health-server.php &
    local server_pid=$!
    
    # Wait a moment for server to start
    sleep 2
    
    # Test the health endpoint
    log_info "Testing health endpoint..."
    local success=0
    
    for i in {1..3}; do
        log_info "Test request $i..."
        if curl -s -f "http://localhost:$PORT/" | jq . 2>/dev/null; then
            log_success "Health check request $i successful!"
            success=1
        else
            log_warning "Health check request $i failed"
        fi
        sleep 1
    done
    
    # Clean up
    log_info "Stopping test server..."
    kill $server_pid 2>/dev/null || true
    wait $server_pid 2>/dev/null || true
    rm -f /tmp/test-health-server.php
    
    if [ $success -eq 1 ]; then
        log_success "Health server test completed successfully!"
        return 0
    else
        log_error "Health server test failed!"
        return 1
    fi
}

# Function to test the entrypoint script syntax
test_entrypoint_syntax() {
    log_info "Testing entrypoint script syntax..."
    
    local entrypoint="cloud-run-services/entrypoints/queue-worker-entrypoint"
    
    if [ ! -f "$entrypoint" ]; then
        log_error "Entrypoint script not found: $entrypoint"
        return 1
    fi
    
    # Check if script is executable
    if [ ! -x "$entrypoint" ]; then
        log_warning "Entrypoint script is not executable, fixing..."
        chmod +x "$entrypoint"
    fi
    
    # Basic syntax check
    if bash -n "$entrypoint"; then
        log_success "Entrypoint script syntax is valid!"
    else
        log_error "Entrypoint script has syntax errors!"
        return 1
    fi
    
    # Check for required functions
    if grep -q "create_health_server" "$entrypoint" && \
       grep -q "cleanup" "$entrypoint" && \
       grep -q "php artisan queue:work" "$entrypoint"; then
        log_success "Entrypoint script contains required functions!"
    else
        log_error "Entrypoint script is missing required functions!"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if PHP is available
    if ! command -v php >/dev/null 2>&1; then
        log_error "PHP is not available"
        return 1
    fi
    
    # Check if curl is available
    if ! command -v curl >/dev/null 2>&1; then
        log_error "curl is not available"
        return 1
    fi
    
    # Check if jq is available (optional)
    if ! command -v jq >/dev/null 2>&1; then
        log_warning "jq is not available (JSON formatting will be skipped)"
    fi
    
    # Check if port is available
    if netstat -ln 2>/dev/null | grep -q ":$PORT "; then
        log_error "Port $PORT is already in use"
        return 1
    fi
    
    log_success "Prerequisites check passed!"
}

# Main function
main() {
    case "${1:-all}" in
        "health")
            check_prerequisites && test_health_server
            ;;
        "syntax")
            test_entrypoint_syntax
            ;;
        "all")
            log_info "Running all tests..."
            echo ""
            
            if check_prerequisites; then
                echo ""
                test_entrypoint_syntax
                echo ""
                test_health_server
                echo ""
                log_success "All tests completed!"
            else
                log_error "Prerequisites check failed!"
                exit 1
            fi
            ;;
        "help"|*)
            echo "Usage: $0 <command>"
            echo ""
            echo "Commands:"
            echo "  health    - Test health server functionality"
            echo "  syntax    - Test entrypoint script syntax"
            echo "  all       - Run all tests (default)"
            echo ""
            echo "Environment Variables:"
            echo "  PORT      - Port to test health server on (default: 8080)"
            echo ""
            echo "Examples:"
            echo "  $0 all"
            echo "  $0 health"
            echo "  $0 syntax"
            echo "  PORT=9090 $0 health"
            ;;
    esac
}

# Run main function
main "$@"
