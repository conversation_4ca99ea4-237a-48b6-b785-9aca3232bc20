<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Stripe
    |--------------------------------------------------------------------------
    */
    'stripe' => [
        'model' => App\Models\User::class,
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook' => [
            'secret' => env('STRIPE_WEBHOOK_SECRET'),
            'tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | DVLA API
    |--------------------------------------------------------------------------
    */
    'dvla' => [
        'api_key' => env('DVLA_API_KEY'),
        'api_url' => env('DVLA_API_URL'),
        'mot_history' => [
            'api_key' => env('DVLA_MOT_HISTORY_API_KEY'),
            'client_id' => env('DVLA_MOT_HISTORY_API_CLIENT_ID'),
            'client_secret' => env('DVLA_MOT_HISTORY_API_CLIENT_SECRET'),
            'scope' => env('DVLA_MOT_HISTORY_API_SCOPE_URL'),
            'token_url' => env('DVLA_MOT_HISTORY_API_TOKEN_URL'),
            'tenant_id' => env('DVLA_MOT_HISTORY_API_TENANT_ID'),
            'api_url' => env('DVLA_MOT_HISTORY_API_URL'),
        ],
    ],

];
