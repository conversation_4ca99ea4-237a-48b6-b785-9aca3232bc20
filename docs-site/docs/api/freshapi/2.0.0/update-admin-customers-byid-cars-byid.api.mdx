---
id: update-admin-customers-byid-cars-byid
title: "updateAdminCustomersByidCarsByid"
description: "updateAdminCustomersByidCarsByid"
sidebar_label: "updateAdminCustomersByidCarsByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVk1z4zYM/SscnPqhxGk6veiWTXan6XTazMZtDxkfYBG2uKFILgnZ62r033dAyXIcZ9scOp292CIIPIAg8IgOGNcJyge4bhP7huI1xnSlG+NgUUDAiA0xRVHpwGFDUEI1qt5qKMA4KCEg11BApI+tiaSh5NhSAamqqUEoO+BdEEvjmNYUoYCVjw3yIPrxEvq+OMDjf4u8GMwp8Ruvd2JTecfkWD4xBGsqZOPd7EPyTmQn4H75gSqGAkL0gSIbSrJr0g1ZYolqUl16bwmdHAhQayPAaO+e2K3QJiqADVsxuPcrHmDeOja8u5n/no2ZPvHXFdHTVH33/VcUmphHSsG7NPi6vLiQP02piiYIqti2VUUpZWeJqjYa3uWqXhJGilA+LKRUxHc+462GEtqgkSn3w75B0pud0dIl8g/FMy+vMGiIa6+H2q6kuHONlzDDYGYolrN9h6VZd2i2flZhlkh/9CCniJt9a7bRQgk1c0jlbGZ9hbb2icsu+Mj9SZi/ioIaAKCADUaDSztkTyyG9K2wtdJJP11c/CB5Lp77WUVK9ZmmzRkGc45/t5G2tEyGKZ074hO3N7Qh60NDjvfOvwTaIr8S9J5xbdz63wCt2dArEe+i120liwl0cSiae6n4IVX70pkqWlzCSE+5wgeFYvx4t+emX/6a50KULnp/IKe3n7AJUulH/SKU1wsfrnx2NTbDOzmWusaoru5u5RIppiH+zeXJkea1SaKnQvQboykpcjp44ziplY+Ka1La82/EKuOK6p+X5+qWv2TRoBvyLlVZyK+qkGnto6H92rZLldrlFMhT+dL7R+PWqVDotGoTRYUt1+R4JJnsHK3125S3k2KvAkUheDW1aVKprWqFg46KtDaJh60MbP3auEJZk/gQ7KBFUSSOtqNUS8IPSk9Mjk5mh8BVG/KWJkZjk1rujp27tllSLFQijFW9hxW1Bh8px9Z4TbZQVSQ8eHopZycKh+RlwpG9UaQSI7dpuAJXkbX/hJyj2F/kC3egjKtsq2U3YEpbH7WKlIhTNqUGjVUbima1vzToCwg+cYP5eRjf9IEWVeZFNRGjEkaUCh6+nhft1FX5LQwWjRP03NzdyJoPgMGAPB8yshTTbJKggPJoUJHsZ2EeLxYFCD8KQNctMdEf0fa9iD+2FOVhWBxoMTOsNkm+9fQoPYt1Girgm/fjtPKtguLlM4xCdLvMvraVFRTwSLvjAUvY7P/0nJPTL/oCakKdn8Vu3Lse3JzNBeFgezJBCVdOb9zd1fz6Z+G/cfSSkocSIm5lqsPt4NwP9SgKWdaBRbducS26A2ieEVq58sP4cEyuOdQXj9d1g8bcP5Lr++m0LGs5bd9/BtD+4Ts=
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminCustomersByidCarsByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/customers/{customerId}/cars/{carId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminCustomersByidCarsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}},{"name":"carId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      