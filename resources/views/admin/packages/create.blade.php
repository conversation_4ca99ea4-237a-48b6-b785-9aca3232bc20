<x-app-layout>
    <x-slot name="header">
        Create Package
    </x-slot>

    @if($errors->any())
        <x-slot name="notification">
            <x-notification-simple type="error" title="Validation Error" description="Please check the form for errors."/>
        </x-slot>
    @endif

    <div class="py-4">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <form method="POST" action="{{ route('admin.packages.store') }}">
                        @csrf

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Basic Information -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Basic Information</h3>
                            </div>

                            <!-- Name -->
                            <div>
                                <x-input-label for="name" :value="__('Package Name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" 
                                              :value="old('name')" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <!-- Service Group -->
                            <div>
                                <x-input-label for="service_group_id" :value="__('Service Group')" />
                                <select id="service_group_id" name="service_group_id" required
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm">
                                    <option value="">Select a service group</option>
                                    @foreach($serviceGroups as $group)
                                        <option value="{{ $group->id }}" {{ old('service_group_id') == $group->id ? 'selected' : '' }}>
                                            {{ $group->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('service_group_id')" />
                            </div>

                            <!-- Description -->
                            <div class="lg:col-span-2">
                                <x-input-label for="description" :value="__('Description')" />
                                <textarea id="description" name="description" rows="3"
                                          class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                                          placeholder="Describe the package...">{{ old('description') }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('description')" />
                            </div>

                            <!-- Duration -->
                            <div>
                                <x-input-label for="duration_minutes" :value="__('Duration (minutes)')" />
                                <x-text-input id="duration_minutes" name="duration_minutes" type="number" class="mt-1 block w-full" 
                                              :value="old('duration_minutes')" min="1" max="1440" />
                                <x-input-error class="mt-2" :messages="$errors->get('duration_minutes')" />
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    Optional. Maximum 24 hours (1440 minutes)
                                </p>
                            </div>

                            <!-- Sort Order -->
                            <div>
                                <x-input-label for="sort_order" :value="__('Sort Order')" />
                                <x-text-input id="sort_order" name="sort_order" type="number" class="mt-1 block w-full" 
                                              :value="old('sort_order', 0)" min="0" />
                                <x-input-error class="mt-2" :messages="$errors->get('sort_order')" />
                            </div>

                            <!-- Features Section -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Features</h3>
                            </div>

                            <!-- Included Features -->
                            <div class="lg:col-span-2">
                                <x-input-label for="included_features" :value="__('Included Features')" />
                                <textarea id="included_features" name="included_features" rows="4"
                                          class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                                          placeholder="Enter each feature on a new line...">{{ old('included_features') ? implode("\n", old('included_features')) : '' }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('included_features')" />
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    Enter each feature on a separate line
                                </p>
                            </div>

                            <!-- Feature Description -->
                            <div class="lg:col-span-2">
                                <x-input-label for="feature_description" :value="__('Feature Description')" />
                                <textarea id="feature_description" name="feature_description" rows="3"
                                          class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                                          placeholder="Detailed description of features...">{{ old('feature_description') }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('feature_description')" />
                            </div>

                            <!-- Pricing Section -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Pricing</h3>
                            </div>

                            <!-- Standard Price -->
                            <div>
                                <x-input-label for="standard_price" :value="__('Standard Price')" />
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">£</span>
                                    </div>
                                    <x-text-input id="standard_price" name="standard_price" type="number" step="0.01" 
                                                  class="pl-7 block w-full" :value="old('standard_price')" min="0" max="99999.99" />
                                </div>
                                <x-input-error class="mt-2" :messages="$errors->get('standard_price')" />
                            </div>

                            <!-- Higher Price -->
                            <div>
                                <x-input-label for="higher_price" :value="__('Higher Price')" />
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">£</span>
                                    </div>
                                    <x-text-input id="higher_price" name="higher_price" type="number" step="0.01" 
                                                  class="pl-7 block w-full" :value="old('higher_price')" min="0" max="99999.99" />
                                </div>
                                <x-input-error class="mt-2" :messages="$errors->get('higher_price')" />
                            </div>

                            <!-- Lower Price -->
                            <div>
                                <x-input-label for="lower_price" :value="__('Lower Price')" />
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">£</span>
                                    </div>
                                    <x-text-input id="lower_price" name="lower_price" type="number" step="0.01" 
                                                  class="pl-7 block w-full" :value="old('lower_price')" min="0" max="99999.99" />
                                </div>
                                <x-input-error class="mt-2" :messages="$errors->get('lower_price')" />
                            </div>

                            <!-- Currency -->
                            <div>
                                <x-input-label for="currency" :value="__('Currency')" />
                                <select id="currency" name="currency"
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm">
                                    <option value="GBP" {{ old('currency', 'GBP') === 'GBP' ? 'selected' : '' }}>GBP (£)</option>
                                    <option value="USD" {{ old('currency') === 'USD' ? 'selected' : '' }}>USD ($)</option>
                                    <option value="EUR" {{ old('currency') === 'EUR' ? 'selected' : '' }}>EUR (€)</option>
                                </select>
                                <x-input-error class="mt-2" :messages="$errors->get('currency')" />
                            </div>

                            <!-- Status Section -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Status</h3>
                            </div>

                            <!-- Is Active -->
                            <div class="lg:col-span-2">
                                <div class="flex items-center">
                                    <input id="is_active" name="is_active" type="checkbox" value="1" 
                                           {{ old('is_active', true) ? 'checked' : '' }}
                                           class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 dark:border-gray-600 rounded">
                                    <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                        Active (visible to operators)
                                    </label>
                                </div>
                                <x-input-error class="mt-2" :messages="$errors->get('is_active')" />
                            </div>
                        </div>

                        <div class="mt-8 flex items-center justify-end space-x-3">
                            <a href="{{ route('admin.packages.index') }}"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                Cancel
                            </a>
                            <x-primary-button>
                                Create Package
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
