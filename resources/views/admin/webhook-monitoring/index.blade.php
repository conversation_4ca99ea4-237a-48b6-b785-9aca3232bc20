<x-app-layout>
    <x-slot name="header">
        {{ __('Webhook Monitoring') }}
    </x-slot>

    <div class="py-4">
        <div class="px-4 sm:px-6 lg:px-8">
            <!-- Header with Period Filter -->
            <div class="sm:flex sm:items-center sm:justify-between">
                <div class="sm:flex-auto">
                    <h1 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Webhook Processing Dashboard</h1>
                    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Monitor webhook processing metrics and activity logs.</p>
                </div>
                <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                    <form method="GET" action="{{ route('admin.webhook-monitoring.index') }}">
                        <select name="period" onchange="this.form.submit()" class="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-gray-700 dark:text-gray-100 dark:ring-gray-600">
                            <option value="today" {{ $period === 'today' ? 'selected' : '' }}>Today</option>
                            <option value="week" {{ $period === 'week' ? 'selected' : '' }}>This Week</option>
                            <option value="month" {{ $period === 'month' ? 'selected' : '' }}>This Month</option>
                            <option value="all" {{ $period === 'all' ? 'selected' : '' }}>All Time</option>
                        </select>
                    </form>
                </div>
            </div>

            <!-- Metrics Summary -->
            <div class="mt-8">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100 mb-4">Webhook Processing Metrics</h3>
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                        Total Webhooks
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                        Success Rate
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                                        Avg Processing Time
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                        New vs Existing
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $metrics['total_webhooks'] }}</div>
                                    <div class="text-sm text-blue-600 dark:text-blue-400">webhooks received</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $metrics['success_rate'] }}%</div>
                                    <div class="text-sm text-green-600 dark:text-green-400">completed successfully</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $metrics['average_processing_time'] }}s</div>
                                    <div class="text-sm text-yellow-600 dark:text-yellow-400">average duration</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $metrics['new_customers'] }} / {{ $metrics['existing_customers'] }}</div>
                                    <div class="text-sm text-purple-600 dark:text-purple-400">new / existing customers</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Signature Validation & Processing Summary -->
            <div class="mt-8">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100 mb-4">Signature Validation & Processing Summary</h3>
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                        Success
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                        Failure
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
                                        No Signature
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-indigo-500 rounded-full mr-2"></div>
                                        Total
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $signatureMetrics['success'] }}</div>
                                    <div class="text-sm text-green-600 dark:text-green-400">{{ $signatureMetrics['success_percentage'] }}%</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $signatureMetrics['failure'] }}</div>
                                    <div class="text-sm text-red-600 dark:text-red-400">{{ $signatureMetrics['failure_percentage'] }}%</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $signatureMetrics['na'] }}</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $signatureMetrics['na_percentage'] }}%</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $signatureMetrics['total'] }}</div>
                                    <div class="text-sm text-indigo-600 dark:text-indigo-400">100%</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Activity Table -->
            <div class="mt-8 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <div class="mb-4 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <div class="w-4 h-1 bg-blue-400 mr-2"></div>
                                    <span>Activities are grouped by Webhook ID and show the originating location</span>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Webhook ID
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Location
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Time
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Activity
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Email
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Duration
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                                @forelse ($activities as $activity)
                                    <tr class="even:bg-gray-50 dark:even:bg-gray-900 {{ $activity->webhook_group_id ? 'border-l-4 border-l-blue-400' : '' }}">
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-200">
                                            <div class="flex items-center">
                                                <a href="{{ route('admin.webhook-monitoring.show', $activity->webhook_group_id) }}"
                                                   class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors duration-150"
                                                   title="View complete processing flow for webhook {{ $activity->webhook_group_id }}">
                                                    {{ substr($activity->webhook_group_id, 0, 8) }}...
                                                    <svg class="ml-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                                                    </svg>
                                                </a>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-200">
                                            <div class="flex items-center">
                                                <svg class="h-4 w-4 text-gray-400 dark:text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                                                </svg>
                                                <span class="truncate max-w-32" title="{{ $activity->location_display }}">
                                                    {{ $activity->location_display }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-200">
                                            {{ $activity->formatted_date }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                            <div class="flex items-center">
                                                <svg class="h-5 w-5 text-gray-400 dark:text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="{{ $activity->activity_icon }}" />
                                                </svg>
                                                {{ $activity->status }}
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-200">
                                            {{ $activity->customer_email ?? '-' }}
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm">
                                            <span class="inline-flex rounded-full px-2 text-xs font-semibold leading-5 {{ $activity->status_badge_class }}">
                                                {{ $activity->status }}
                                            </span>
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-200">
                                            @if($activity->processing_time)
                                                {{ number_format($activity->processing_time, 2) }}s
                                            @else
                                                -
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                            No webhook activity found for the selected period.
                                        </td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {{ $activities->links() }}
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
