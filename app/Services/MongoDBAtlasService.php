<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class MongoDBAtlasService
{
    private string $apiKey;
    private string $appId;
    private string $baseUrl;
    private string $clusterName;
    private string $databaseName;

    public function __construct()
    {
        $this->apiKey = config('services.mongodb_atlas.api_key');
        $this->appId = config('services.mongodb_atlas.app_id');
        $this->baseUrl = config('services.mongodb_atlas.base_url');
        $this->clusterName = config('services.mongodb_atlas.cluster_name');
        $this->databaseName = config('services.mongodb_atlas.database_name');
    }

    /**
     * Insert a document into a MongoDB collection
     *
     * @param string $collection
     * @param array $document
     * @return array|null
     * @throws Exception
     */
    public function insertOne(string $collection, array $document): ?array
    {
        $url = "{$this->baseUrl}/{$this->appId}/endpoint/data/v1/action/insertOne";

        $payload = [
            'collection' => $collection,
            'database' => $this->databaseName,
            'dataSource' => $this->clusterName,
            'document' => $document
        ];

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'api-key' => $this->apiKey,
            ])->post($url, $payload);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('MongoDB Atlas API Error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'payload' => $payload
            ]);

            throw new Exception("MongoDB Atlas API error: " . $response->body());
        } catch (Exception $e) {
            Log::error('MongoDB Atlas Service Error', [
                'message' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;
        }
    }

    /**
     * Find documents in a MongoDB collection
     *
     * @param string $collection
     * @param array $filter
     * @param array $options
     * @return array|null
     * @throws Exception
     */
    public function find(string $collection, array $filter = [], array $options = []): ?array
    {
        $url = "{$this->baseUrl}/{$this->appId}/endpoint/data/v1/action/find";

        $payload = [
            'collection' => $collection,
            'database' => $this->databaseName,
            'dataSource' => $this->clusterName,
            'filter' => $filter
        ];

        if (!empty($options)) {
            $payload = array_merge($payload, $options);
        }

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'api-key' => $this->apiKey,
            ])->post($url, $payload);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('MongoDB Atlas API Error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'payload' => $payload
            ]);

            throw new Exception("MongoDB Atlas API error: " . $response->body());
        } catch (Exception $e) {
            Log::error('MongoDB Atlas Service Error', [
                'message' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;
        }
    }

    /**
     * Store an API log entry
     *
     * @param string $identifier
     * @param array $payload
     * @param array $metadata
     * @return array|null
     * @throws Exception
     */
    public function storeLog(string $identifier, array $payload, array $metadata = []): ?array
    {
        $document = [
            'identifier' => $identifier,
            'payload' => $payload,
            'metadata' => $metadata,
            'created_at' => now()->toISOString(),
            'timestamp' => now()->timestamp
        ];

        return $this->insertOne('api_logs', $document);
    }
}
