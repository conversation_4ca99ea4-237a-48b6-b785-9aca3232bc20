<?php

namespace Database\Seeders;

use Config;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        Role::updateOrCreate(['name' => Config::get('constants.roles.super_admin')]);
        $userRole = Role::updateOrCreate(['name' => Config::get('constants.roles.user')]);
        $operatorRole = Role::updateOrCreate(['name' => Config::get('constants.roles.operator')]);
        $carClubSubscriberRole = Role::updateOrCreate(['name' => Config::get('constants.roles.car_club_subscriber')]);
        $freshcarAdminRole = Role::updateOrCreate(['name' => Config::get('constants.roles.freshcar_admin')]);

        $permissions = [
            // user
            'create user',
            'delete user',
            'update user',
            'view user',
            'view users',
            // dashboard
            'view dashboard',
            // logs
            'view logs',
            // roles
            'view roles',
            // features
            'view features',
            'edit features',
            // gohighlevel
            'manage gohighlevel',
            'view gohighlevel',
            'edit gohighlevel',
            // webhook monitoring
            'view webhook monitoring',
            // service groups
            'view service groups',
            'create service groups',
            'update service groups',
            'delete service groups',
            // packages
            'view packages',
            'create packages',
            'update packages',
            'delete packages',
        ];

        foreach ($permissions as $permission) {
            Permission::updateOrCreate(['name' => $permission]);
        }


        $userRole->givePermissionTo('view users');
        $operatorRole->givePermissionTo('view dashboard');
        $freshcarAdminRole->givePermissionTo('view dashboard', 'view users', 'manage gohighlevel', 'view gohighlevel', 'edit gohighlevel');
        $freshcarAdminRole->givePermissionTo('view roles', 'view webhook monitoring');
        $freshcarAdminRole->givePermissionTo('view service groups', 'create service groups', 'update service groups', 'delete service groups');
        $freshcarAdminRole->givePermissionTo('view packages', 'create packages', 'update packages', 'delete packages');
    }
}
