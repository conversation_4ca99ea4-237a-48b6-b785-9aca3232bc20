---
id: update-admin-customerpreferredvaleter-byid
title: "updateAdminCustomerpreferredvaleterByid"
description: "updateAdminCustomerpreferredvaleterByid"
sidebar_label: "updateAdminCustomerpreferredvaleterByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVktzGzcM/iscnPpYW647vezNsZOpO53Wk6jpwaMDtIS0jLkkQ4JS1J397xlw9bAjy/UxF2kJAh9AEviAHhiXCep7uM6JfUfxLtKCYiT9ES0xxSvdGQezCgJG7EQi6j047AhqaE6Y3WqowDioISC3UEGkz9lE0lBzzFRBalrqEOoeeBMEyTimJUWoYOFjhzyKfr2EYZiN5pT4jdcbsWm8Y3IsnxiCNQ2y8W7yKXknsiNwP/9EDUMFIfpAkQ0l2TXphiRc/Uh17r0ldDAMFaDWRoDR3j2yW6BNVAEbtmLwwS94hHnr2PDmZvp3MWb6wt9XRI+v6qefv6PQxDxSCt6l0dflxYX8aUpNNEFQxTY3DaVUnCVqcjS8Kbk4J4wUob6fSaqI73LGWw015KCRqWTxLsXDLldXY66+2RjJ1qfOXm/XEbdej5neSKqXjK9hgsFMUAAmzQmESX+6fgaQU8bVruBytFBDyxxSPZlY36BtfeK6Dz7ycBT/n6KgRgCoYIXR4NyOtysW4/UuMFuptN8uLn6Rd6i+9bOIlNozTaszDOYc/8uR1jRPhimdO+Ijtze0IutDR453zk+BZuRXgn5gXBq3/D9Aa1b0SsS76HVuZLEHnR2S6oNUxHhVu9TaZ7y4hC19lQoYFartx7sdd/3x77QkqlTZ+wN5vf2CXZBKeFJPQomD8OXCF1fbYnknx1LXGNXV3a08IsU0xr+6PDrStDVJ9FSIfmU0JUVOB28cJ7XwUXFLSnv+i1gVXFH9eHmubvmURYduvPcGY6rkVzXItPTR0G5t81ylPN8H8lg+9/7BuGWqFDqtcqKoMHNLjrckVJyjtX6dynZS7FWgKA1A7cs4qZSbVuGooyItTeJxqwBbvzSuUtYkPgQ7alEUiaP1Vqrlwg9Kj0yenMyOgascypYmRmOTmm+eOne5m1OsVCKMTbuDFbUOH6jE1nlNtlJNJDx4eu7OjhQOl1eYSPa2IpUYOafxCVxD1r6EXKLYPeQzb6CMa2zWshswpbWPWkVKxKmYUofGqhVFs9g9GgwVBJ+4w9I+toPAyJeqEKY6xZjqOardl1ZpmMGiceKiVHi/JdN7wGBAeoxMIxWcIlSooH5hJJlVIJwpeH0/x0T/RDsMIv6cKUozmR2osrCuNkm+9b6RfRP6fhCBH95vJ5wfFVTPH2krRLcpjGyzrKCCB9q8PEoNs6GCllCXLtdvDa5H32dTgT0AHg1EQm37JnV3Nb3+XehqO0lJhkINEdcypOF6jMiP6SMKRdaDRbfMuBTdEbS0/CyPc5gGnnJhCfXZM/f9qDH1D+SGYX8FLGs57TB8BeN6yGo=
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminCustomerpreferredvaleterByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/customerpreferredvaleter/{customerPreferredValeterId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminCustomerpreferredvaleterByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerPreferredValeterId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      