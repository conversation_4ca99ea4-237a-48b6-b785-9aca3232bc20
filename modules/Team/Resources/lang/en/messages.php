<?php

return [
    'team_not_empty' => 'Cannot delete: Team ":team_name" has users',
    'team_deleted' => 'Team ":team_name" deleted',
    'team_edited' => 'Team data changed successfully',
    'user_added' => 'User added to team',
    'user_removed' => 'User removed from team',
    'user_not_in_team' => 'Not currently assigned to the specified team',
    'user_not_in_other_teams' => 'Can\'t remove user from the last assigned team',
    'cannot_delete_default_team' => 'Default team cannot be deleted',
    'invalid_team_id' => 'The selected id is invalid.'
];
