<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('gohighlevel_locations', function (Blueprint $table) {
            $table->string('client_id')->nullable()->change();
            $table->string('client_secret')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('gohighlevel_locations', function (Blueprint $table) {
            $table->string('client_id')->nullable(false)->change();
            $table->string('client_secret')->nullable(false)->change();
        });
    }
};