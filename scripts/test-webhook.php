<?php

/**
 * GoHighLevel Webhook Testing Script
 * 
 * This script simulates real GoHighLevel webhook calls for testing
 * the OpportunityStatusUpdateHandler functionality.
 */

require_once __DIR__ . '/../vendor/autoload.php';

class WebhookTester
{
    private string $webhookUrl;
    private string $signingSecret;

    public function __construct(string $webhookUrl, string $signingSecret = 'test-secret')
    {
        $this->webhookUrl = $webhookUrl;
        $this->signingSecret = $signingSecret;
    }

    /**
     * Test webhook with new customer data
     */
    public function testNewCustomer(): void
    {
        echo "🧪 Testing New Customer Webhook...\n";
        
        $payload = [
            'contact_id' => 'test_' . uniqid(),
            'first_name' => 'John',
            'last_name' => 'Doe',
            'full_name' => '<PERSON>',
            'email' => '<EMAIL>', // Use test email
            'tags' => 'urgent repair, test webhook',
            'country' => 'GB',
            'date_created' => date('c'),
            'contact_type' => 'lead',
            'opportunity_name' => 'Test Car Service Booking',
            'status' => 'won', // This triggers the handler
            'lead_value' => 150,
            'opportunity_source' => 'website',
            'source' => 'website',
            'pipleline_stage' => 'booked',
            'pipeline_id' => 'test_pipeline_id',
            'id' => 'test_opportunity_' . uniqid(),
            'pipeline_name' => 'Smart Repair',
            'user' => [
                'firstName' => 'Test',
                'lastName' => 'User',
                'email' => '<EMAIL>'
            ],
            'owner' => 'Test User',
            'location' => [
                'name' => 'Test Location',
                'address' => '123 Test Street',
                'city' => 'London',
                'state' => null,
                'country' => 'GB',
                'postalCode' => 'SW1A 1AA',
                'fullAddress' => '123 Test Street, London SW1A 1AA',
                'id' => 'test_location_id'
            ]
        ];

        $this->sendWebhook($payload, 'New Customer Test');
    }

    /**
     * Test webhook with existing customer data
     */
    public function testExistingCustomer(): void
    {
        echo "🧪 Testing Existing Customer Webhook...\n";
        
        $payload = [
            'contact_id' => 'existing_' . uniqid(),
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>', // Use different test email
            'status' => 'won',
            'opportunity_name' => 'Repeat Service Test',
            'lead_value' => 200,
            'location' => [
                'fullAddress' => '456 Oak Avenue, Manchester M1 1AA'
            ]
        ];

        $this->sendWebhook($payload, 'Existing Customer Test');
    }

    /**
     * Test webhook with non-won status (should be skipped)
     */
    public function testNonWonStatus(): void
    {
        echo "🧪 Testing Non-Won Status (should be skipped)...\n";
        
        $payload = [
            'email' => '<EMAIL>',
            'status' => 'open', // Not 'won' - should be skipped
            'opportunity_name' => 'Pending Opportunity Test'
        ];

        $this->sendWebhook($payload, 'Non-Won Status Test');
    }

    /**
     * Test webhook with missing email (should be skipped)
     */
    public function testMissingEmail(): void
    {
        echo "🧪 Testing Missing Email (should be skipped)...\n";
        
        $payload = [
            'status' => 'won',
            'opportunity_name' => 'No Email Test'
            // Missing email field
        ];

        $this->sendWebhook($payload, 'Missing Email Test');
    }

    /**
     * Test webhook with array format (as shown in examples/customwebhook.json)
     */
    public function testArrayFormat(): void
    {
        echo "🧪 Testing Array Format Webhook...\n";
        
        $payload = [
            [
                'contact_id' => 'array_test_' . uniqid(),
                'first_name' => 'Array',
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'status' => 'won',
                'opportunity_name' => 'Array Format Test',
                'lead_value' => 100
            ],
            [
                'Spatie\\WebhookClient\\WebhookConfig' => [
                    'name' => 'gohighlevel-webhook-default'
                ]
            ]
        ];

        $this->sendWebhook($payload, 'Array Format Test');
    }

    /**
     * Send webhook request
     */
    private function sendWebhook(array $payload, string $testName): void
    {
        $jsonPayload = json_encode($payload);
        $signature = $this->generateSignature($jsonPayload);

        $headers = [
            'Content-Type: application/json',
            'x-wh-signature: ' . $signature,
            'User-Agent: GoHighLevel-Webhook-Test'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->webhookUrl,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonPayload,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false, // For local testing
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            echo "❌ {$testName} - cURL Error: {$error}\n";
            return;
        }

        if ($httpCode === 200) {
            echo "✅ {$testName} - Success (HTTP {$httpCode})\n";
        } else {
            echo "⚠️  {$testName} - HTTP {$httpCode}\n";
            echo "Response: " . substr($response, 0, 200) . "\n";
        }

        echo "📝 Payload sent: " . substr($jsonPayload, 0, 100) . "...\n";
        echo "🔐 Signature: {$signature}\n\n";
    }

    /**
     * Generate webhook signature (simplified for testing)
     */
    private function generateSignature(string $payload): string
    {
        // This is a simplified signature for testing
        // In production, GoHighLevel uses their own signing method
        return hash_hmac('sha256', $payload, $this->signingSecret);
    }

    /**
     * Run all tests
     */
    public function runAllTests(): void
    {
        echo "🚀 Starting GoHighLevel Webhook Tests\n";
        echo "📡 Webhook URL: {$this->webhookUrl}\n\n";

        $this->testNewCustomer();
        sleep(1); // Brief pause between tests

        $this->testExistingCustomer();
        sleep(1);

        $this->testNonWonStatus();
        sleep(1);

        $this->testMissingEmail();
        sleep(1);

        $this->testArrayFormat();

        echo "✨ All webhook tests completed!\n";
        echo "📋 Check your application logs for detailed processing information.\n";
    }
}

// Usage
if ($argc < 2) {
    echo "Usage: php test-webhook.php <webhook-url> [signing-secret]\n";
    echo "Example: php test-webhook.php https://abc123.ngrok.io/high/webhook/opportunity-status-update\n";
    exit(1);
}

$webhookUrl = $argv[1];
$signingSecret = $argv[2] ?? 'test-secret';

$tester = new WebhookTester($webhookUrl, $signingSecret);
$tester->runAllTests();
