<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class CarLookupTest extends TestCase
{
    /**
     * Test successful car lookup by registration number.
     */
    public function test_lookup_car_by_registration_success(): void
    {
        // Mock the DVLA API response
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'registrationNumber' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'colour' => 'Blue',
                'yearOfManufacture' => 2020,
                'fuelType' => 'Petrol',
                'engineCapacity' => 2000,
                'transmission' => 'Manual',
                'motExpiryDate' => '2024-12-31',
                'taxDueDate' => '2024-06-30',
                'co2Emissions' => 150,
            ], 200),
        ]);

        // Mock the MOT History API response (token endpoint)
        Http::fake([
            config('services.dvla.mot_history.token_url') => Http::response([
                'access_token' => 'mock-access-token',
                'token_type' => 'Bearer',
                'expires_in' => 3600,
            ], 200),
        ]);

        // Mock the MOT History API response (data endpoint)
        Http::fake([
            config('services.dvla.mot_history.api_url') . '/*' => Http::response([
                'registration' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'firstUsedDate' => '2020-01-01',
                'fuelType' => 'Petrol',
                'primaryColour' => 'Blue',
                'motTests' => [],
            ], 200),
        ]);

        $response = $this->getJson('/api/v3/car-lookup/registration/AB12CDE');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'registrationNumber',
                    'makeAndModel',
                    'make',
                    'model',
                    'colour',
                    'year',
                    'fuelType',
                    'engineSize',
                    'transmission',
                ],
                'meta' => [
                    'timestamp',
                    'source',
                ],
            ])
            ->assertJson([
                'data' => [
                    'registrationNumber' => 'AB12CDE',
                    'makeAndModel' => 'BMW 3 Series',
                    'make' => 'BMW',
                    'model' => '3 Series',
                ],
                'meta' => [
                    'source' => 'car-lookup-service',
                ],
            ]);
    }

    /**
     * Test car lookup by registration number not found.
     */
    public function test_lookup_car_by_registration_not_found(): void
    {
        // Mock the DVLA API to return 404 for not found registration
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'error' => 'Vehicle not found',
            ], 404),
        ]);

        $response = $this->getJson('/api/v3/car-lookup/registration/NOTFOUND');

        $response->assertStatus(404)
            ->assertJson([
                'message' => 'Car not found for the provided registration number',
            ]);
    }

    /**
     * Test car lookup with invalid registration number.
     */
    public function test_lookup_car_by_registration_invalid_format(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/registration/A');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['registrationNumber']);
    }

    /**
     * Test car lookup with registration number containing invalid characters.
     */
    public function test_lookup_car_by_registration_invalid_characters(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/registration/AB12@CDE');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['registrationNumber']);
    }

    /**
     * Test successful car search by make and model.
     */
    public function test_search_cars_by_make_model_success(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/make-model?query=BMW');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'makeAndModel',
                        'make',
                        'model',
                        'category',
                        'variants',
                    ],
                ],
                'meta' => [
                    'timestamp',
                    'source',
                ],
            ]);

        // Check that BMW cars are returned
        $data = $response->json('data');
        $this->assertNotEmpty($data);

        foreach ($data as $car) {
            $this->assertStringContainsStringIgnoringCase('BMW', $car['make']);
        }
    }

    /**
     * Test car search with no results.
     */
    public function test_search_cars_by_make_model_no_results(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/make-model?query=NONEXISTENTBRAND');

        $response->assertStatus(404)
            ->assertJson([
                'data' => [],
                'message' => 'No cars found matching the search criteria',
            ]);
    }

    /**
     * Test car search with missing query parameter.
     */
    public function test_search_cars_by_make_model_missing_query(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/make-model');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['query']);
    }

    /**
     * Test car search with query too short.
     */
    public function test_search_cars_by_make_model_query_too_short(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/make-model?query=A');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['query']);
    }

    /**
     * Test car search with query containing invalid characters.
     */
    public function test_search_cars_by_make_model_invalid_characters(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/make-model?query=BMW@#$');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['query']);
    }

    /**
     * Test car search with valid special characters.
     */
    public function test_search_cars_by_make_model_valid_special_characters(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/make-model?query=BMW 3');

        $response->assertStatus(200);
    }

    /**
     * Test that endpoints are accessible without authentication.
     */
    public function test_endpoints_are_public(): void
    {
        // Mock the DVLA API response for registration lookup
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'registrationNumber' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'colour' => 'Blue',
                'yearOfManufacture' => 2020,
                'fuelType' => 'Petrol',
                'engineCapacity' => 2000,
                'transmission' => 'Manual',
                'motExpiryDate' => '2024-12-31',
                'taxDueDate' => '2024-06-30',
                'co2Emissions' => 150,
            ], 200),
        ]);

        // Mock the MOT History API response (token endpoint)
        Http::fake([
            config('services.dvla.mot_history.token_url') => Http::response([
                'access_token' => 'mock-access-token',
                'token_type' => 'Bearer',
                'expires_in' => 3600,
            ], 200),
        ]);

        // Mock the MOT History API response (data endpoint)
        Http::fake([
            config('services.dvla.mot_history.api_url') . '/*' => Http::response([
                'registration' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'firstUsedDate' => '2020-01-01',
                'fuelType' => 'Petrol',
                'primaryColour' => 'Blue',
                'motTests' => [],
            ], 200),
        ]);

        // Test registration lookup without auth
        $response = $this->getJson('/api/v3/car-lookup/registration/AB12CDE');
        $response->assertStatus(200);

        // Test make/model search without auth (this uses mock data, no external API)
        $response = $this->getJson('/api/v3/car-lookup/make-model?query=BMW');
        $response->assertStatus(200);
    }

    /**
     * Test registration lookup with spaces in registration number.
     */
    public function test_lookup_car_by_registration_with_spaces(): void
    {
        // Mock the DVLA API response (note: service normalizes input to AB12CDE)
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'registrationNumber' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'colour' => 'Blue',
                'yearOfManufacture' => 2020,
                'fuelType' => 'Petrol',
                'engineCapacity' => 2000,
                'transmission' => 'Manual',
                'motExpiryDate' => '2024-12-31',
                'taxDueDate' => '2024-06-30',
                'co2Emissions' => 150,
            ], 200),
        ]);

        // Mock the MOT History API response (token endpoint)
        Http::fake([
            config('services.dvla.mot_history.token_url') => Http::response([
                'access_token' => 'mock-access-token',
                'token_type' => 'Bearer',
                'expires_in' => 3600,
            ], 200),
        ]);

        // Mock the MOT History API response (data endpoint)
        Http::fake([
            config('services.dvla.mot_history.api_url') . '/*' => Http::response([
                'registration' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'firstUsedDate' => '2020-01-01',
                'fuelType' => 'Petrol',
                'primaryColour' => 'Blue',
                'motTests' => [],
            ], 200),
        ]);

        $response = $this->getJson('/api/v3/car-lookup/registration/AB12 CDE');

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'registrationNumber' => 'AB12CDE',
                    'makeAndModel' => 'BMW 3 Series',
                ],
            ]);
    }

    /**
     * Test case insensitive search.
     */
    public function test_search_cars_case_insensitive(): void
    {
        $response = $this->getJson('/api/v3/car-lookup/make-model?query=bmw');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertNotEmpty($data);
    }
}
