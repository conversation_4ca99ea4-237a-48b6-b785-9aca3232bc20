---
id: search-cars-by-make-model
title: "Search by make and model"
description: "Public endpoint that doesn't require authentication, Searches for cars using the make and the model of the vehicle"
sidebar_label: "Search by make and model"
hide_title: true
hide_table_of_contents: true
api: eJzFVktz2zYQ/iuYzSHJDEUqdpK2vDmOk3HGSTyR2xwiTwKCKxIjEEDxkK1q+N87C1KybOXRW32wDe578eHb3YCx6HiQRp/XUIJH7kR7yp1/tX7Pl/je1Kggg8AbD+UXOOWOXRizjBauM6jRCyctWUMJl7FSUjDUtTVSBxZaHlht0OvHgTn8O0qHjMfQog5SpJgZm6WA6NnCOCa48yx6qRsWWmQdXyLjuh4OlAkzi3RYYSuFQsjAcsc7DOgovQ1o3iGU8HdEt4YMpN47jCnUUAYXMQMvWuw4lBsIa0tWPjipG+j7a1L21miPnuRH0yn9uV/ukDlz6KMKnjkMTuIKa+ajEOj9Iiq1hj6D59Pnh9bUSG0CW5ioa+j7DDyK6GRYQ/nlOoMOQ2voRhoMqczQQgnF6rjgVhaCu4lK11BQkybdeE0e3WrbiugUlNCGYH1ZFMoIrlrjQ7mxxoUeHt7eBSmwwQFksOJO8koN9ZPFUMKCRxWghGfTZ1Po+z57GKfGFSpjO9QhXzj0reAuFyaPy4OIr+9Ut3EP/XErJySUAifPpn/8/uLly6Pnz387nuYYnbE4uUEfjnIXdc6tLQ6CzAJvCE8/C/CrRC+dqaOgw87N9d19zQhGQ58q5A7dHqIoCIxIo/OokI3/vDGu49TOd5+vDqK++3zFTmJojZP/pMfCWuQ1ur0H8ip5YYP7nJ3d8s4qLNnje3blVm8TzBJ1/zjBTeqFSZnKoCi1N9QDRrA8uTwnAKDzQyLH+TSfQga3E2WaZDN0sJBdU2xbl2S51Q0Vx8WycQTsU6OMgxIevUk/kAFX4Qpvw0HA/mH5X69a6UnEWu5ZhajZiC2sWTDMoVVcYOpDbcIHDCx5JIu/jrLEG9KnN7bGwKRm0WPNqnWyEEoS7Li1Pv8613P96BGbxa7jbj3Xu8jWmZWs0e84baCpjusBVMRXGf1mggdsjJO4PatYzbWP1a6kPQGrjFlK3fghy+jRPeDFnJ0HxpUyNz6J/VwHwyy6hXEd21G2J65pGR+UmMNG+jCIkmdlGqkzpqQPu3TnelBD4jqm8WasokaFd1r7NveKU0PqLFoSzXWNgUvlqa/3wuvYVegyNgyUrV9S27F6Iq2MCYecQs31rj8P+zZqfK+B0daDbPw03uWpM95PPjrZSM0+oTfRCWSzlqeqn5x+/DR7unfRPlriOP+f7IZRlRRr03Gp03BDH3w+Rj+5d5lzfaLUHoS+PwrHZ33BHV+hYjOuRYhdzs61ULEecD6wBkvPmBBN375HEeVcf/v2ba5/QANrE91k4IJRETIQRgcuEs2PQ/SqRXYhmzZoSuytM5G47D573tzc5KFFtVWjZ29HGu0zUFKg9rjn9P351YETY1EPnc6Na4rRyBek22cQ0HX+42I2TIE9u0aGNla5MF2Rxig6rutdKnvUVNmJMA6LSpmqoBsrLs5Pzz7MzhKrOR5womQnw+GYJnCQnCU59UESGyyMEwMNofbRIRtHFPOBV1LJsE4IX3BJdM0bzBO9j9NzA+QSypfTDCzNC+ikjoHWmWoNJZxfMl7XDr1nxg1P+/w1u2lR72MG69RheuJ3Ll/80iPlhR2XignTVVInbKRRDtb40HG9d1vjjnPwah8Oq02CD+rwf62B47wNeBsKq7jU1JuEss24O32B1THNHysJ7Lv9CTLY26CuM6AdibQ3m4p7/NOpvqfPwxJJe1UtPS1GNZQLrjz+pBNPPo0b51P2oxSXuN7bUFdcRdJJ+8V2A6N9sN9bCN+eXUHf/wvWWx4N
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Search by make and model"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/car-lookup/make-model"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Public endpoint that doesn't require authentication, Searches for cars using the make and the model of the vehicle

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"query","in":"query","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Search results retrieved successfully"},"404":{"description":"Car not found"}}}
>
  
</StatusCodes>


      