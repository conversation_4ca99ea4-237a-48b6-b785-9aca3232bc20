{"info": {"_postman_id": "f8442748-a2ae-48bf-8eac-054adda1f7f1", "name": "ci", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "API", "item": [{"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Home page", "event": [{"listen": "test", "script": {"id": "8a14a7ff-09ba-455c-9933-f84becd1b803", "exec": ["", "pm.test('Reponse code to be 200', function () {", "    pm.expect(pm.response.code).to.be.equal(200);", "});", "console.log('build test');"], "type": "text/javascript", "packages": {}}}], "id": "d4a587e4-151f-4baa-b9eb-2c34058280ac", "protocolProfileBehavior": {"disabledSystemHeaders": {}, "disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{base_url}}"}, "response": [{"id": "e25b2f64-afdd-43d7-b3bd-03340128b9b7", "name": "Home page", "originalRequest": {"method": "GET", "header": [], "url": "{{base_url}}"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Tue, 02 May 2023 07:58:07 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"this-is-laravel-api\": \"10.5.1\"\n}"}]}], "id": "3281f466-a708-4eac-8908-715766ecf794", "event": [{"listen": "prerequest", "script": {"id": "9fae8ad5-e54c-43c1-b2be-23f9472acf19", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "21eb3632-c03e-4130-9891-63540f840699", "type": "text/javascript", "exec": [""]}}]}, {"name": "User onBoarding", "item": [{"name": "Sign up", "item": [{"name": "sign up api creates user account successfully with valid data", "event": [{"listen": "test", "script": {"id": "4ff481b7-bb36-4407-acf2-9ef05facb0d1", "exec": ["pm.test(\"Response status code is 201\", function () {", "    pm.response.to.have.status(201);", "     pm.environment.set(\"verification_url\", encodeURI(pm.cookies.get(\"verification_url\")));", "});", "", "pm.test(\"Response has all required fields\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.data).to.be.an('object');", "    pm.expect(responseData.data.email).to.exist;", "    pm.expect(responseData.data.created_at).to.exist;", "    pm.expect(responseData.data.id).to.exist;", "    pm.expect(responseData.message).to.exist;", "});", "", "pm.test(\"Response time is less than 200ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "4eb1ae30-5bd0-4b11-8c63-f3fac1ca9b09", "exec": ["pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", "pm.environment.set(\"password\", \"Admin@12465\");\r", "pm.variables.set(\"password_confirmation\", \"Admin@12465\");\r", "pm.variables.set(\"first_name\", \"<PERSON>\");\r", "pm.variables.set(\"last_name\", \"Doe\");"], "type": "text/javascript"}}], "id": "2f3a1298-4043-4b84-a332-9a00a7d8b281", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "33c95a2a-3ee3-44cc-80d1-0c7b339df24d", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "385a9b60-11e5-4882-a3d5-74629ba10339", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api creates user account successfully  for blank last name", "event": [{"listen": "test", "script": {"id": "60f16e77-d203-42a9-b0e9-21a8ae77a9ab", "exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"User registered successfully.\");", "});", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "pm.test(\"datatype first_name is text\", function () {", "        pm.expect(pm.response.json().first_name).toString('first_name')", "    });", "   pm.test(\"datatype last_name is text\", function () {", "        pm.expect(pm.response.json().last_name).toString('last_name')", "    }); ", "       pm.test(\"datatype email is string\", function () {", "        pm.expect(pm.response.json().last_name).toString('email')", "    }); ", "           pm.test(\"datatype password is a string\", function () {", "        pm.expect(pm.response.json().email).toString('password')", "    });", "    ", "    ", "    ", "", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "22f153bf-a206-4080-a83e-b9a95c7ab4f6", "exec": ["pm.variables.set(\"last_name\", \"\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", "pm.environment.set(\"password\", \"Admin@12465\");\r", "pm.environment.set(\"password_confirmation\", \"Admin@12465\");\r", "pm.variables.set(\"last_name\", \"\");\r", ""], "type": "text/javascript"}}], "id": "2cd8729d-0b06-48eb-9731-1e14536eb126", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "ffa37e2e-61ff-4b93-8a8b-9e2917424213", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "f5633d8c-700d-4664-b586-3e447b160e6f", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign up api shows validation for existing email", "event": [{"listen": "test", "script": {"id": "9dfee0dc-b6ea-429f-a6ea-feb4b959b65b", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The email has already been taken.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});"], "type": "text/javascript"}}], "id": "6e916a79-9c1a-4fd9-a436-6304d9ff57f5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "3c6488c9-5b67-4fa9-9c23-564aff9fb63c", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "128187c9-0e16-49d9-bfa0-67c1f94371aa", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign up api shows validation for incorrect email format", "event": [{"listen": "test", "script": {"id": "434712cc-4e7d-408e-87b8-4c3985bc7007", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The email field must be a valid email address.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "d269302f-3498-4c67-b5bc-6df7f189470d", "exec": ["pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "80e6630a-97a2-4e58-abe6-077af738b411", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "f2f15ab7-2bda-44c3-ba68-0d7e522c1c93", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "6efbb9e4-ad85-4831-9e17-420399fe492d", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "sign up api shows validation for blank password", "event": [{"listen": "test", "script": {"id": "058962e2-6f43-4a2e-8955-88abb774b78e", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field is required.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "25475c75-885b-4108-90c0-7307baee374c", "exec": ["pm.variables.set(\"password\", \"\");\r", "\r", ""], "type": "text/javascript"}}], "id": "e65e4f49-2aa7-489d-ad8a-50d115221c61", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "5240e0ae-c6f1-41b8-a3b6-459002a3b79e", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "32ee8122-35bd-4090-ab25-125a5b6f4fa8", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "sign up api shows validation for pwd less than 8 chars", "event": [{"listen": "test", "script": {"id": "1e7db411-5748-495b-83f7-a96f6b958666", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field must be at least 8 characters.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "dda949ec-b299-4847-86f9-94e4bfe77af0", "exec": ["pm.variables.set(\"password\", \"Aa@123\");\r", "pm.variables.set(\"password_confirmation\",\"Aa@123\");\r", "\r", ""], "type": "text/javascript"}}], "id": "e209f686-1e4a-4584-8217-b70b13f30040", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "78d2c84b-64a6-4d11-8f64-c331f967a7cb", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "cd806893-11c8-4891-a1d6-7db4cee011cb", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "sign up api shows validation for password having no capital letters", "event": [{"listen": "test", "script": {"id": "67181d58-09c2-4e24-83c5-686968045ee6", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field must contain at least one uppercase and one lowercase letter.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "2f52fedc-991b-46dd-8e76-5b360a817388", "exec": ["pm.variables.set(\"password\", \"admin@123\");\r", "pm.variables.set(\"password_confirmation\",\"admin@123\");\r", ""], "type": "text/javascript"}}], "id": "1b620830-a1ae-4faf-8ca4-e8333ccad04f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "13d100ee-08cc-4dc1-8bd5-6720d6a9c590", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "310f207b-902e-4753-8126-730fcb94af35", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "sign up api shows validation for password having no lowercase chars", "event": [{"listen": "test", "script": {"id": "01ff8e8f-6ee8-4bf3-ac8f-833bf57ebbab", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field must contain at least one uppercase and one lowercase letter.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "88516bc1-ce8e-45a8-8d7a-f8fc2923579e", "exec": ["pm.variables.set(\"password\", \"ADMIN@123\");\r", "pm.variables.set(\"password_confirmation\", \"ADMIN@123\");\r", ""], "type": "text/javascript"}}], "id": "59c82e05-e23c-46d8-89b2-1a11f227a2ce", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "5a4fc05d-13de-4d5a-b938-ba16b02222d1", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "3750189b-92aa-47ce-a86b-b2c2dafd650f", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "sign up api shows validation for password having no number", "event": [{"listen": "test", "script": {"id": "2ab4f9f9-fc4f-4a0a-a23b-579bce26f163", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field must contain at least one number.\");", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "d34dfd7a-fcc6-4d05-93a4-14e83cac90f7", "exec": ["pm.variables.set(\"password\", \"<PERSON><PERSON><PERSON>@abc\");\r", "pm.variables.set(\"password_confirmation\", \"<PERSON>D<PERSON>@abc\");\r", ""], "type": "text/javascript"}}], "id": "f6b96bcf-4b2f-4ebe-82a5-11bf9ac9caa0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "9ef49daa-b392-45b0-a8a4-f609dee68d28", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "f255e7f1-b70c-4d7d-a68e-9465ae6f43bd", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "sign up api shows validation for password having no special char", "event": [{"listen": "test", "script": {"id": "eb1d67ea-8d3e-47ba-837f-7d4f68c80ae0", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field must contain at least one symbol.\");", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "cc835603-d79a-499d-8b98-8e57b7b5e0fd", "exec": ["pm.variables.set(\"password\", \"Admin1234\");\r", "pm.variables.set(\"password_confirmation\", \"Admin1234\");\r", ""], "type": "text/javascript"}}], "id": "c5559512-a497-4435-be55-35be871c51c5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "9182fb42-714f-49a8-b8a1-37d5215df55c", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "d28ca603-51bf-457c-afd4-dcac18bc02de", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "sign up api shows validation when password does not match confirm password", "event": [{"listen": "test", "script": {"id": "caae2b24-40f4-47a1-aa0a-04ff974aa5cb", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field confirmation does not match.\");", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "4b881da6-3e65-4b2e-8bfa-90f199929bc0", "exec": ["pm.variables.set(\"password\", \"Admin@1234\");\r", "pm.variables.set(\"password_confirmation\", \"Admin@123\");\r", ""], "type": "text/javascript"}}], "id": "46adfa5f-c7b7-4d0c-ab93-beb69fe373c1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "6f662213-2aae-4c56-958b-0c1764972fcf", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "44a277b7-788c-4039-a2d0-fe87c0d162b9", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for blank confirm password", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field confirmation does not match.\");", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "", "", "    ", "    ", "    ", "", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password_confirmation\", \"\");\r", ""], "type": "text/javascript"}}], "id": "b13e2393-bd1f-4798-8c0d-f49f1df95e84", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "d8590ba7-423f-4fb9-a607-08f19d017d47", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "4e3f3d3c-e374-40fb-a54a-6e4f95395234", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for password with more than 2  consecutive identical chars", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password must not contain more than 2 consecutive identical characters.\");", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "", "", "    ", "    ", "    ", "", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password\", \"Admin@333\");\r", "pm.variables.set(\"password_confirmation\",\"Admin@333\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "c3401c85-cd27-4370-8a8c-978849d4059c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "7ae6d19b-3f5a-44c8-bc25-92663e78533a", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "e2a65e93-d0c7-4e95-bd3f-49b80b5f6368", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for password with more than 4 increasing numbers", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.json().errors.password[0]).to.include(\"The password must not contain more than 4 sequential increasing/decreasing characters.\");", "});   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password\", \"Coffee@12345\");\r", "pm.variables.set(\"password_confirmation\",\"Coffee@12345\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "c0db8955-b636-4cd3-aba8-084084f22ad6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "19a0cd8e-a555-41f0-a63d-448687f37cc3", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "95b79818-a0f2-4023-a036-79c8acae0cfb", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for password with 3more than 4  decreasing  numbers", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test('Status code is 422', function () {", "    pm.response.to.have.status(422);", "})", "", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.json().errors.password[0]).to.include(\"The password must not contain more than 4 sequential increasing/decreasing characters.\");", "});", "", "pm.test('Content-Type is present', function () {", "    pm.response.to.have.header('Content-Type');", "})", "", "pm.test('Response time is less than 600ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "})", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password\", \"Coffee@76543\");\r", "pm.variables.set(\"password_confirmation\",\"Coffee@76543\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "51c0e1d5-b3b0-4781-9b4a-fa5e4fd32610", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "745d2b4c-831b-43d7-bd10-d0cee1726181", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "af0e46e5-542a-4fca-8760-cecaee6389ca", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for password with more than 4 increasing letters", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.json().errors.password[0]).to.include( \"The password must not contain more than 4 sequential increasing/decreasing characters.\");", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "", "", "    ", "    ", "    ", "", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password\", \"abcdefg@W147\");\r", "pm.variables.set(\"password_confirmation\",\"abcdefg@W147\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "3f526132-c187-476e-ac65-0f434564456e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "a1e31312-4518-4d2b-bcf0-2aa855d65ba6", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "35e7cd1f-23b9-4f1a-a8b4-c8fffd72f346", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for password with more than 4 decreasing letters", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.json().errors.password[0]).to.include( \"The password must not contain more than 4 sequential increasing/decreasing characters.\");", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "", "", "    ", "    ", "    ", "", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password\", \"utsrq@9675Q\");\r", "pm.variables.set(\"password_confirmation\",\"utsrq@9675Q\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "0abc7ed3-879c-459b-84db-a6ffd8ab37d9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "09e6cbe1-c26a-4c70-ad57-533cbb92ec54", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "983200b4-18fb-4a86-9801-ac38b8572b78", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for password with more than 2 sequential increasing chars on the standard keyboard", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.json().errors.password[0]).to.include(\"The password must not contain more than 4 sequential characters on the keyboard.\");", "    ", " });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "", "", "    ", "    ", "    ", "", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password\", \"qwerty@A834\");\r", "pm.variables.set(\"password_confirmation\",\"qwerty@A834\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "da84495c-5a6a-4ead-b900-fe969b1d7f37", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "03610c9a-6290-4080-9a13-683acd049cf9", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "9fe28743-f841-4459-a3d4-4c46cc3b5f80", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign Up Api shows validation for password with more than 2 sequential  decreasing chars(char and letters) on the standard keyboard", "event": [{"listen": "test", "script": {"id": "c0cd89e0-83ee-4648-9485-700edce7d329", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password must not contain more than 4 sequential characters on the keyboard.\");", "     });   ", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "", "", "    ", "    ", "    ", "", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "990e5e57-b150-4232-a44c-c90299e10c88", "exec": ["pm.variables.set(\"password\", \"][Poiuyt1\");\r", "pm.variables.set(\"password_confirmation\",\"][Poiuyt1\");\r", "pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.environment.set(\"email\", email);\r", ""], "type": "text/javascript"}}], "id": "0997cbf9-edd9-498e-bc81-0618f2b188fd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "36d55df9-d074-4270-b031-366dfb3116eb", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "62f177b6-a50e-4459-a0df-801cda02fadf", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Sign up Api shows validation for blank email", "event": [{"listen": "test", "script": {"id": "46e46850-2a32-4e56-928b-ede89d494b04", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The email field is required when invitation key is not present.\");", "});", "    pm.test(\"Content-Type is present\", function () {", "        pm.response.to.have.header(\"Content-Type\");", "    });", "    pm.test(\"Response time is less than 600ms\", function () {", "        pm.expect(pm.response.responseTime).to.be.below(600);", "    });", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "770b3356-8f56-405f-bc5b-fdfa6d5e39aa", "exec": ["pm.variables.set(\"email\", \"\");"], "type": "text/javascript"}}], "id": "dd8e49fc-fb34-4165-83d3-26a8294f81a9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "7d162021-cf42-492a-a998-b35a7926ef8d", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "bc8299f9-bd61-4440-b3c1-a1375026a67e", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Verify e-mail Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test('Reponse code to be 200', function () {", "    pm.expect(pm.response.code).to.be.equal(200);", "});", "", ""], "type": "text/javascript", "id": "90a4e53a-e3b2-4a56-a379-fffc54b83fa0"}}], "id": "092c1bbb-494e-4e0b-903d-56d9bb7d08f1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{verification_url}}"}, "response": []}], "id": "790e9dcf-7119-4551-a4c8-2eeff9436941"}, {"name": "User Login", "item": [{"name": "sign up api creates user account successfully with valid data", "event": [{"listen": "test", "script": {"id": "f0f02b9e-2c38-40f9-b965-410283029b3e", "exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"User registered successfully.\");", "});", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "pm.test(\"datatype first_name is text\", function () {", "        pm.expect(pm.response.json().first_name).toString('first_name')", "    });", "   pm.test(\"datatype last_name is text\", function () {", "        pm.expect(pm.response.json().last_name).toString('last_name')", "    }); ", "       pm.test(\"datatype email is string\", function () {", "        pm.expect(pm.response.json().last_name).toString('email')", "    }); ", "           pm.test(\"datatype password is a string\", function () {", "        pm.expect(pm.response.json().email).toString('password')", "    });", "    pm.test('Reponse code to be 201', function () {", "    pm.expect(pm.response.code).to.be.equal(201);", "    pm.environment.set(\"verification_url\", encodeURI(pm.cookies.get(\"verification_url\")));", "});", "    "], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "f9421e23-236b-46b8-9fda-fc73e34271b9", "exec": ["pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99999)));\r", "var uid = pm.environment.get(\"UID\");\r", "var email = 'user_' + uid + '@yopmail.com';\r", "pm.variables.set(\"email\", email);\r", "pm.variables.set(\"password\", \"Admin@12465\");\r", "pm.variables.set(\"password_confirmation\", \"Admin@12465\");\r", "pm.variables.set(\"first_name\", \"Platform\");\r", "pm.variables.set(\"last_name\", \"user\");"], "type": "text/javascript"}}], "id": "a27f70bf-7c20-4302-b4ad-baec5590e2c5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "{{first_name}}", "type": "text"}, {"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "{{password_confirmation}}", "type": "text"}, {"key": "last_name", "value": "{{last_name}}", "type": "text"}]}, "url": "{{base_url}}/register", "description": "Except the last name, other fields are mandatory. Besides it, User must add a password having:\n- One lowercase character\n- One uppercase character\n- One number\n- One special character\n- 8 characters minimum\n- User must confirm their password\n- Passwords must match"}, "response": [{"id": "5a665725-ad6a-4c09-bf3b-72a59a77a301", "name": "Validation failure", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Fage", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 20 Apr 2023 02:27:04 GMT"}, {"key": "Server", "value": "Apache/2.4.56 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.4"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://*********:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"The email field is required. (and 1 more error)\",\n    \"errors\": {\n        \"email\": [\n            \"The email field is required.\"\n        ],\n        \"password\": [\n            \"The password field is required.\"\n        ]\n    }\n}"}, {"id": "1004ecd8-c9dd-4c43-952b-74d1442ac7cb", "name": "Successful Registration", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "Platform", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}, {"key": "password_confirmation", "value": "User@12345", "type": "text"}, {"key": "last_name", "value": "User", "type": "text"}]}, "url": "{{API_URL}}/register"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 08:05:42 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"first_name\": \"Platform\",\n        \"last_name\": \"User\",\n        \"email\": \"<EMAIL>\",\n        \"updated_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"created_at\": \"2023-04-22T08:05:42.000000Z\",\n        \"id\": 3\n    },\n    \"message\": \"User registered successfully.\"\n}"}]}, {"name": "Verify e-mail", "event": [{"listen": "test", "script": {"exec": ["pm.test('Reponse code to be 200', function () {", "    pm.expect(pm.response.code).to.be.equal(200);", "});", "", ""], "type": "text/javascript", "id": "90a4e53a-e3b2-4a56-a379-fffc54b83fa0"}}], "id": "ee333dd6-8a78-41bc-a3dd-ce4e03c1be83", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{verification_url}}"}, "response": []}, {"name": "User Login  -successful login", "event": [{"listen": "test", "script": {"id": "a276ed75-7a1f-4c87-b51e-5ded61e53e44", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"User logged in successfully.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "jsonData = JSON.parse(responseBody);", "pm.test(\"Token set\", function () {", "    pm.environment.set(\"token\", jsonData.data.token);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "d2ebf584-e76c-40bc-808c-0de61814ff09", "exec": ["\r", ""], "type": "text/javascript"}}], "id": "585c1219-d2ac-41e9-ac62-7b41db11e0ec", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "d7b03981-ee4f-4246-a398-6cf3adf16955", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}, {"name": "User Login  - incorrect email, does not exist in db", "event": [{"listen": "test", "script": {"id": "d69a5d4c-cbfc-48e3-aad2-35a2fe41dc15", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"These credentials do not match our records.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "46d6e457-f9ef-42fb-979a-59d087c545b2", "exec": ["pm.variables.set(\"email\", \"<EMAIL>\");"], "type": "text/javascript"}}], "id": "4ba89dfa-b843-4bc1-8980-a0d7b883cef6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "e09bc44f-68a0-41b8-86fd-2341ae58b54d", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}, {"name": "User Login  - incorrect password", "event": [{"listen": "test", "script": {"id": "208d55d6-6c37-4ad1-85da-85b394771e6d", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"These credentials do not match our records.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "4399cc70-33aa-4cf6-9356-ee1e29253bac", "exec": ["pm.variables.set(\"password\", \"Test@1234\");"], "type": "text/javascript"}}], "id": "d67da267-9b0f-4cf8-a1ff-d996d8cc1583", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "b5edbf43-a999-4f97-8d31-df34c5b6d56b", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}, {"name": "User Login  - incorrect email format", "event": [{"listen": "test", "script": {"id": "deb13836-7fd5-4c47-9d9c-0dee8869675f", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"These credentials do not match our records.\");", "});", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "pm.test(\"Verify the response message is 'These credentials do not match our records.'\", function () {", "    const responseData = pm.response.json();", "    pm.expect(responseData.message).to.eql(\"These credentials do not match our records.\");", "});", "", "pm.test(\"Email field in errors object is non-empty\", function () {", "    const responseData = pm.response.json();", "    pm.expect(responseData.errors.email[0]).to.have.lengthOf.at.least(1, \"Email field should not be empty\");", "});", "pm.test(\"Content-Type is application/json\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "de85f968-1ae1-4a51-aa28-2ffa0239b404", "exec": ["pm.variables.set(\"email\", \"test@yopmail\");"], "type": "text/javascript"}}], "id": "2429e4e1-e5ea-4133-971b-fbe5dab5e43b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "b7add18a-e4a3-4d6a-893c-72aed0e0d81a", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}, {"name": "User Login  - blank email", "event": [{"listen": "test", "script": {"id": "a922ba0b-d709-4d3e-8b6c-d858723dca19", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The email field is required.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "2880bcc7-7441-4334-9e9e-39abf9b04639", "exec": ["pm.variables.set(\"email\", \"\");"], "type": "text/javascript"}}], "id": "900e9550-d4a4-42dd-a08b-5b3c26fe72ce", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "fe9dc7ca-7491-45b2-876c-e236ed37480e", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}, {"name": "User Login  - blank password", "event": [{"listen": "test", "script": {"id": "dfb4ce7e-5d6a-40a6-8f81-f7e8e5a8d1c5", "exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"The password field is required.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "71149cbc-4bdd-4483-870c-02f39c0036e1", "exec": ["pm.variables.set(\"password\", \"\");"], "type": "text/javascript"}}], "id": "90ff0d58-4941-4d55-8cb4-91fc8a45284d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "3a861017-bbce-4e50-b6c8-b702b1b3e5bc", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}], "id": "5448ac9a-6ef1-4828-b3fc-a374413b99cf"}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Team", "item": [{"name": "User Login  -successful login", "event": [{"listen": "test", "script": {"id": "a276ed75-7a1f-4c87-b51e-5ded61e53e44", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"User logged in successfully.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "jsonData = JSON.parse(responseBody);", "pm.test(\"Token set\", function () {", "    pm.environment.set(\"token\", jsonData.data.token);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"id": "d2ebf584-e76c-40bc-808c-0de61814ff09", "exec": ["pm.variables.set(\"email\", \"<EMAIL>\");\r", "pm.variables.set(\"password\", \"Password@123\");"], "type": "text/javascript", "packages": {}}}], "id": "482d1242-4ef3-48fb-927a-04cb6adf610d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "fb0b2e63-cbc9-447f-a103-c382e531a9d4", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}, {"name": "List Teams", "event": [{"listen": "test", "script": {"id": "d4d79adb-63b1-4eaf-a4f9-9b0ce0c9b43f", "exec": ["// Fixed syntax\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response has a JSON content type\", function () {\r", "    pm.response.to.have.header('Content-Type', 'application/json');\r", "});\r", "pm.test(\"Response body contains the 'data' array\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.exist.and.to.be.an('array');\r", "});\r", "pm.test(\"Name field is a non-empty string\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData.data).to.be.an('array');\r", "    responseData.data.forEach(function(team) {\r", "        pm.expect(team.name).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Name should not be empty\");\r", "    });\r", "      });\r", "pm.test(\"Deleted_at field is either null or in a valid date-time format\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData.data).to.be.an('array');\r", "    responseData.data.forEach(function(team) {\r", "        pm.expect(team.deleted_at).to.satisfy(function (value) {\r", "            return value === null || (new Date(value) !== \"Invalid Date\");\r", "});\r", "    });\r", "    pm.test(\"Response message is an empty string\", function () {\r", "  const responseData = pm.response.json();\r", "  \r", "  pm.expect(responseData.message).to.equal(\"\");\r", "});\r", "});"], "type": "text/javascript", "packages": {}}}], "id": "3260501c-ae7a-4e8f-9094-1f5cd09a8965", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "GET", "header": [], "url": "{{base_url}}/mod/team/teams"}, "response": []}, {"name": "Show Team - invalid <PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "d4d79adb-63b1-4eaf-a4f9-9b0ce0c9b43f", "exec": ["pm.test('Status code is 422', function () {\r", "    pm.response.to.have.status(422);\r", "})\r", "\r", "pm.test('Content-Type is present', function () {\r", "    pm.response.to.have.header('Content-Type');\r", "})\r", "\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The selected id is invalid.\");\r", "    });\r", "\r", "\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "id": "726aff4f-1acf-4b41-a422-fb7cab711df8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "GET", "header": [], "url": "{{base_url}}/mod/team/teams/1400"}, "response": []}, {"name": "Create Team - inValid Team name", "event": [{"listen": "test", "script": {"id": "aedea90a-7b1e-451c-b4b3-5de2992a23c2", "exec": ["pm.test('Status code is 422', function () {\r", "    pm.response.to.have.status(422);\r", "})\r", "\r", "pm.test('Content-Type is present', function () {\r", "    pm.response.to.have.header('Content-Type');\r", "})\r", "\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The name field must not be greater than 50 characters.\");\r", "    });"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"id": "5a9dd25f-1f31-4c87-8312-52ef569eee5b", "exec": ["pm.environment.set(\"team<PERSON><PERSON>\", \"Team Lazy and not working ever in their lives and should be asked to just go enjoy themselves\");"], "type": "text/javascript", "packages": {}}}], "id": "3a5ba1a9-a53a-4f5b-a0d1-ddfa495278d9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "{{team<PERSON>ame}}", "type": "text"}]}, "url": "{{base_url}}/mod/team/teams"}, "response": []}, {"name": "Create Team -  Team Name is missing", "event": [{"listen": "test", "script": {"id": "aedea90a-7b1e-451c-b4b3-5de2992a23c2", "exec": ["pm.test('Status code is 422', function () {\r", "    pm.response.to.have.status(422);\r", "})\r", "\r", "pm.test('Content-Type is present', function () {\r", "    pm.response.to.have.header('Content-Type');\r", "})\r", "\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The name field is required.\");\r", "    });"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"id": "e30c7ef6-45e4-47be-a9ee-23144f45cfe8", "exec": ["pm.environment.set(\"teamName\", \"\");"], "type": "text/javascript", "packages": {}}}], "id": "5c3f19fe-cc63-4337-978b-76b29a07252f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "{{team<PERSON>ame}}", "type": "text"}]}, "url": "{{base_url}}/mod/team/teams"}, "response": []}, {"name": "Create Team - Valid team name", "event": [{"listen": "prerequest", "script": {"id": "8e219b0e-48e1-42d6-aa7e-007dbc64049a", "exec": ["pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99)));\r", "var uid = pm.environment.get(\"UID\");\r", "var teamName = 'Team_' + uid ;\r", "pm.environment.set(\"teamName\", teamName);\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"id": "5047df80-66c6-4588-af4e-f364d20b9670", "exec": ["pm.test('Status code is 200', function () {\r", "    pm.response.to.have.status(200);\r", "})\r", "\r", "pm.test('Content-Type is present', function () {\r", "    pm.response.to.have.header('Content-Type');\r", "})\r", "\r", "pm.test('datatype id is a number', function () {\r", "    pm.expect(pm.response.json().data.id).to.be.a('number');\r", "})\r", "\r", "pm.test('id field is not null or empty', function () {\r", "    const responseData = pm.response.json().data;\r", "    pm.expect(responseData.id).to.exist.and.to.not.be.null;\r", "\r", "});\r", "pm.test('name field is not null or empty', function () {\r", "    const responseData = pm.response.json().data;\r", "    pm.expect(responseData.name).to.exist.and.to.not.be.null;\r", "    pm.expect(responseData.name).to.be.a('string').and.to.have.lengthOf.at.least(1, 'name should not be empty');\r", "});\r", "// Stores the teamId in an environment or global variable\r", "var teamId = pm.response.json().data.id;\r", "pm.globals.set(\"teamId\", teamId);"], "type": "text/javascript", "packages": {}}}], "id": "ae809ee3-6d12-4c0f-8dd3-21b2f6820054", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "{{team<PERSON>ame}}", "type": "text"}]}, "url": "{{base_url}}/mod/team/teams"}, "response": []}, {"name": "Edit Team -valid  team name Co<PERSON>", "event": [{"listen": "prerequest", "script": {"id": "8961ddb2-fd95-4791-832f-b081d51c17cb", "exec": ["pm.environment.set(\"UID\", Math.floor(Math.random() * Math.floor(99)));\r", "var uid = pm.environment.get(\"UID\");\r", "var teamNameNew = 'Team_' + uid +'New';\r", "pm.environment.set(\"teamNameNew\", teamNameNew);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"id": "fd4904cd-8675-4514-b08b-1ad70378d5fc", "exec": ["pm.test('Status code is 200', function () {\r", "    pm.response.to.have.status(200);\r", "})\r", "\r", "pm.test('Content-Type is present', function () {\r", "    pm.response.to.have.header('Content-Type');\r", "})\r", "\r", "pm.test('datatype id is a number', function () {\r", "    pm.expect(pm.response.json().data.id).to.be.a('number');\r", "})\r", "\r", "pm.test('id field is not null or empty', function () {\r", "    const responseData = pm.response.json().data;\r", "    pm.expect(responseData.id).to.exist.and.to.not.be.null;\r", "\r", "});\r", "pm.test('name field is not null or empty', function () {\r", "    const responseData = pm.response.json().data;\r", "    pm.expect(responseData.name).to.exist.and.to.not.be.null;\r", "    pm.expect(responseData.name).to.be.a('string').and.to.have.lengthOf.at.least(1, 'name should not be empty');\r", "});"], "type": "text/javascript", "packages": {}}}], "id": "8d7dfe58-a0ee-405d-88cc-43c7b764d296", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"{{teamNameNew}}\"\r\n}"}, "url": "{{base_url}}/mod/team/teams/{{teamId}}"}, "response": []}, {"name": "Delete Team - valid I<PERSON> Co<PERSON>", "event": [{"listen": "test", "script": {"id": "208e96d0-d135-4e9f-9fb8-4be23ffa3f75", "exec": ["pm.test('Status code is 200', function () {\r", "    pm.response.to.have.status(200);\r", "})\r", "\r", "pm.test('Content-Type is present', function () {\r", "    pm.response.to.have.header('Content-Type');\r", "})\r", "\r", "pm.test(\"Response data is null\", function () {\r", "  const responseData = pm.response.json();\r", "  \r", "  pm.expect(responseData.data).to.be.null;\r", "});\r", "\r", "pm.test(\"Response has the required fields - data and message\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData).to.have.property('data');\r", "    pm.expect(responseData).to.have.property('message');\r", "});\r", "\r", "pm.test(\"Message field is a non-empty string\", function () {\r", "  const responseData = pm.response.json();\r", "  \r", "  pm.expect(responseData.message).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Message should not be empty\");\r", "});\r", "\r", "\r", "pm.test(\"Content-Type is application/json\", function () {\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");\r", "});\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "id": "4417fc8e-6d06-4713-9fdc-cfcf4d93843e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "DELETE", "header": [], "url": "{{base_url}}/mod/team/teams/{{teamId}}"}, "response": []}], "id": "0a474fe0-1f04-40b1-aff9-05f477466129"}], "id": "176abda2-e74c-455f-8fc7-b55d6f6042f4"}, {"name": "Invite user", "item": [{"name": "User Login  -successful login", "event": [{"listen": "test", "script": {"id": "a276ed75-7a1f-4c87-b51e-5ded61e53e44", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"User logged in successfully.\");", "    });", "pm.test(\"Content-Type is present\", function () {", "    pm.response.to.have.header(\"Content-Type\");", "});", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "jsonData = JSON.parse(responseBody);", "pm.test(\"Token set\", function () {", "pm.collectionVariables.set(\"user_token\", jsonData.data.token);", "});", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "d2ebf584-e76c-40bc-808c-0de61814ff09", "exec": ["pm.variables.set(\"email\", \"<EMAIL>\");\r", "pm.variables.set(\"password\", \"Password@123\");\r", "\r", "\r", ""], "type": "text/javascript"}}], "id": "e0320846-2f3b-47b5-a00f-ef01c047d580", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}]}, "url": "{{base_url}}/login", "description": "The api expects a valid registered user's email and correct password provided during the registration!"}, "response": [{"id": "95359409-d399-43b1-8b69-4670afe73b86", "name": "User Login", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "User@12345", "type": "text"}]}, "url": "{{API_URL}}/login"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:09:55 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"1|B3aQPO7ErI4AvlF3uiXCZJKxbrVydKzKTxFrIIng\"\n    },\n    \"message\": \"User logged in successfully.\"\n}"}]}, {"name": "Invite user -Admin", "event": [{"listen": "test", "script": {"id": "61c0d1cd-9058-40db-b518-0e93b11643a8", "exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"Invitation created for\");\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", "\r", "       pm.test(\"datatype email is string\", function () {\r", "        pm.expect(pm.response.json().last_name).toString('email')\r", "    }); \r", ""], "type": "text/javascript"}}], "id": "8db540c7-c0e5-4c50-b3a9-97e215550d52", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{$randomEmail}}", "type": "text"}, {"key": "role", "value": "super_admin", "type": "text"}]}, "url": {"raw": "{{base_url}}/invitations?email={{$randomEmail}}&role=admin", "host": ["{{base_url}}"], "path": ["invitations"], "query": [{"key": "email", "value": "{{$randomEmail}}"}, {"key": "role", "value": "admin"}]}}, "response": []}, {"name": "Invite user - Member", "event": [{"listen": "test", "script": {"id": "61c0d1cd-9058-40db-b518-0e93b11643a8", "exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"Invitation created \");\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", "\r", "       pm.test(\"datatype email is string\", function () {\r", "        pm.expect(pm.response.json().last_name).toString('email')\r", "    }); "], "type": "text/javascript"}}], "id": "b02b5875-d772-4887-b812-bf5a88f04670", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{$randomEmail}}", "type": "text"}, {"key": "role", "value": "user", "type": "text"}]}, "url": {"raw": "{{base_url}}/invitations?email={{$randomEmail}}&role=user", "host": ["{{base_url}}"], "path": ["invitations"], "query": [{"key": "email", "value": "{{$randomEmail}}"}, {"key": "role", "value": "user"}]}}, "response": []}, {"name": "Invite user - Throws error when email is missing", "event": [{"listen": "test", "script": {"id": "61c0d1cd-9058-40db-b518-0e93b11643a8", "exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", "\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The email field is required.\");\r", "    });\r", ""], "type": "text/javascript"}}], "id": "0a05f830-2bf2-4d33-aa95-3f20b1d090f9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "", "type": "text"}, {"key": "role", "value": "user", "type": "text"}]}, "url": "{{base_url}}/invitations"}, "response": []}, {"name": "Invite user - Throws error when email is  invalid", "event": [{"listen": "test", "script": {"id": "61c0d1cd-9058-40db-b518-0e93b11643a8", "exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The email field must be a valid email address.\");\r", "    });"], "type": "text/javascript"}}], "id": "e6cc4f2a-0c4b-4aa6-96a1-2a532f9147b8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "testuser@yopmail", "type": "text"}, {"key": "role", "value": "user", "type": "text"}]}, "url": "{{base_url}}/invitations"}, "response": []}, {"name": "Invite user - Throws error when role is missing", "event": [{"listen": "test", "script": {"id": "61c0d1cd-9058-40db-b518-0e93b11643a8", "exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The role field is required.\");\r", "    });"], "type": "text/javascript"}}], "id": "272ea977-ae05-4097-aa0f-f70db7f87f29", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{$randomEmail}}", "type": "text"}, {"key": "role", "value": "", "type": "text"}]}, "url": "{{base_url}}/invitations"}, "response": []}, {"name": "Invite user - Throws error when role is  invalid", "event": [{"listen": "test", "script": {"id": "61c0d1cd-9058-40db-b518-0e93b11643a8", "exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The selected role is invalid.\");\r", "    });\r", ""], "type": "text/javascript"}}], "id": "d1670094-5f46-4e1c-aa54-31cea2acc687", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{$randomEmail}}", "type": "text"}, {"key": "role", "value": "<PERSON>", "type": "text"}]}, "url": "{{base_url}}/invitations"}, "response": []}], "id": "777557da-3718-4d6a-b6ce-1813b718b2dd", "auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "event": [{"listen": "prerequest", "script": {"id": "dbcb6f5e-f344-435a-96f4-889b99096175", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "8443649f-95b3-48be-a905-3ec0771e69cd", "type": "text/javascript", "exec": [""]}}]}, {"name": "User tests", "item": [{"name": "Edit user", "item": [{"name": "Users", "event": [{"listen": "test", "script": {"id": "45459a75-2f38-4dc4-9a36-292a3c3f3d7a", "exec": ["pm.test('Reponse code to be 200', function () {", "    pm.expect(pm.response.code).to.be.equal(200);", "});", "", "pm.test(\"Body is present\", function () {", "    ((responseBody) !== undefined && (responseBody.length > 1));", "    jsonData = JSON.parse(responseBody);", "});", "//pm.globals.set('user_id', pm.response.json().data.id);", "", "jsonData = JSON.parse(responseBody);", "", "value =jsonData.data[0].id", "", "console.log(value)", "pm.environment.set(\"user_id\", value);"], "type": "text/javascript"}}], "id": "651659be-2a88-452c-aa65-16df88819764", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"], "query": [{"key": "page", "value": "1", "type": "text", "disabled": true}, {"key": "perPage", "value": "2", "type": "text", "disabled": true}]}}, "response": []}, {"name": "Edit User", "event": [{"listen": "test", "script": {"id": "8b64cdfd-dddd-4075-b84a-cbe6a1d90eb6", "exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response has all required fields\", function () {\r", "    const responseData = pm.response.json().data;\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.email).to.exist;\r", "    pm.expect(responseData.id).to.exist;\r", "    pm.expect(responseData.created_at).to.exist;\r", "    pm.expect(responseData.updated_at).to.exist;\r", "    pm.expect(responseData.first_name).to.exist;\r", "    pm.expect(responseData.last_name).to.exist;\r", "    pm.expect(responseData.initials).to.exist;\r", "    pm.expect(responseData.has_subscribed).to.be.a('boolean');\r", "    pm.expect(responseData.roles).to.be.an('array').that.is.not.empty;\r", "    pm.expect(responseData.onboarded).to.be.a('boolean');\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "    pm.expect(responseData.birth_date).to.satisfy((value) => value === null || !isNaN(Date.parse(value)), \"Birth date should be null or a valid date format\");\r", "});"], "type": "text/javascript"}}], "id": "03c40deb-5e21-4d9e-8ad0-b6e10d933c23", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": "{{base_url}}/users/{{user_id}}"}, "response": []}, {"name": "Update User -when  names are valid and  role is admin", "event": [{"listen": "test", "script": {"id": "772ce517-4e0f-4c88-a143-91a8b6ef6bad", "exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", "\r", "pm.test(\"Response has all required fields\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.be.an('object');\r", "    pm.expect(responseData.data.first_name).to.exist;\r", "    pm.expect(responseData.data.last_name).to.exist;\r", "    pm.expect(responseData.data.role).to.exist;\r", "    \r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "id": "aca8faba-354a-4b79-a98a-84a674d3a8af", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "PATCH", "header": [{"key": "Authorization", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\":\"<PERSON>\",\n    \"last_name\": \"thomson\",\n    \"role\": \"super_admin\"\n}\n\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"], "query": [{"key": "first_name", "value": "fname", "type": "text", "disabled": true}, {"key": "last_name", "value": "fname", "type": "text", "disabled": true}, {"key": "role", "value": "super_admin", "type": "text", "disabled": true}]}}, "response": []}, {"name": "Update User -when first name is blank", "event": [{"listen": "test", "script": {"id": "f606ff83-25ec-4ca5-8635-d9df6938126c", "exec": ["pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The first name field is required.\");\r", "    });\r", "\r", "    pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", ""], "type": "text/javascript"}}], "id": "62ac5c1a-aad4-4522-86d6-5167c9ebc43c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "PATCH", "header": [{"key": "Authorization", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\":\"\",\n    \"last_name\": \"<PERSON>\",\n    \"role\": \"super_admin\"\n}\n\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"], "query": [{"key": "first_name", "value": "fname", "type": "text", "disabled": true}, {"key": "last_name", "value": "fname", "type": "text", "disabled": true}, {"key": "role", "value": "super_admin", "type": "text", "disabled": true}]}}, "response": []}, {"name": "Update User -when role is blank", "event": [{"listen": "test", "script": {"id": "15528449-175d-44e6-a797-64a97c6ee071", "exec": ["pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The role field is required.\");\r", "    });\r", "\r", "    pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", ""], "type": "text/javascript"}}], "id": "83f71cfd-ee60-47ba-ac8b-bcc1d529988a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "PATCH", "header": [{"key": "Authorization", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\":\"<PERSON>\",\n    \"last_name\": \"<PERSON>\",\n    \"role\": \"\"\n}\n\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"], "query": [{"key": "first_name", "value": "fname", "type": "text", "disabled": true}, {"key": "last_name", "value": "fname", "type": "text", "disabled": true}, {"key": "role", "value": "super_admin", "type": "text", "disabled": true}]}}, "response": []}, {"name": "Update User -when last name is blank", "event": [{"listen": "test", "script": {"id": "a81d676a-573b-4e83-98bd-9a88399a4dd2", "exec": ["pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The last name field is required.\");\r", "    });\r", "\r", "    pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", ""], "type": "text/javascript"}}], "id": "5069644e-348d-4b08-9fa6-c4d85bf8518a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "PATCH", "header": [{"key": "Authorization", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\":\"<PERSON>\",\n    \"last_name\": \"\",\n    \"role\": \"super_admin\"\n}\n\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"], "query": [{"key": "first_name", "value": "fname", "type": "text", "disabled": true}, {"key": "last_name", "value": "fname", "type": "text", "disabled": true}, {"key": "role", "value": "super_admin", "type": "text", "disabled": true}]}}, "response": []}, {"name": "Update User -when role is invalid", "event": [{"listen": "test", "script": {"id": "71e58791-6e44-46e7-91e6-3a79e13034f4", "exec": ["pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"The selected role is invalid.\");\r", "    });\r", "\r", "    pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "pm.test(\"Content-Type is present\", function () {\r", "    pm.response.to.have.header(\"Content-Type\");\r", "});\r", "pm.test(\"Response time is less than 600ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(600);\r", "});\r", ""], "type": "text/javascript"}}], "id": "42df5521-ec8b-41bb-af0c-f5dabca79889", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "PATCH", "header": [{"key": "Authorization", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\":\"<PERSON>\",\n    \"last_name\": \"<PERSON>\",\n    \"role\": \"admin\"\n}\n\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"], "query": [{"key": "first_name", "value": "fname", "type": "text", "disabled": true}, {"key": "last_name", "value": "fname", "type": "text", "disabled": true}, {"key": "role", "value": "super_admin", "type": "text", "disabled": true}]}}, "response": []}, {"name": "Update User -when  names are valid and  role is user", "event": [{"listen": "test", "script": {"id": "1733d193-18db-4104-b25f-0aff77834f92", "exec": ["pm.test(\"Response status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "    \r", "});\r", "\r", "pm.test(\"Response has all required fields\", function () {\r", "    const responseData = pm.response.json();\r", "\r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData.data).to.be.an('object');\r", "    pm.expect(responseData.data.first_name).to.exist;\r", "    pm.expect(responseData.data.last_name).to.exist;\r", "    pm.expect(responseData.data.role).to.exist;\r", "    //pm.expect(responseData.created_at).to.exist;\r", "    //pm.expect(responseData.updated_at).to.exist;\r", "   // pm.expect(responseData.first_name).to.exist;\r", "   // pm.expect(responseData.last_name).to.exist;\r", "   // pm.expect(responseData.birth_date).to.exist;\r", "   // pm.expect(responseData.initials).to.exist;\r", "   // pm.expect(responseData.has_subscribed).to.exist;\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "id": "0ed236c1-ef8f-4e02-9ba1-2c4de40ccebc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "PATCH", "header": [{"key": "Authorization", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\":\"<PERSON>\",\n    \"last_name\": \"<PERSON>\",\n    \"role\": \"user\"\n}\n\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"], "query": [{"key": "first_name", "value": "fname", "type": "text", "disabled": true}, {"key": "last_name", "value": "fname", "type": "text", "disabled": true}, {"key": "role", "value": "super_admin", "type": "text", "disabled": true}]}}, "response": []}], "id": "ea5391cf-f594-46cb-8681-bb56da89f737"}], "id": "6e024bc3-6425-4bb4-a30e-fd4904fed3d4"}], "id": "b92b8244-f5f8-4aec-88e6-22a36216d89c"}, {"name": "User off-boarding", "item": [{"name": "User <PERSON>", "item": [{"name": "User Lo<PERSON>ut- successful", "event": [{"listen": "test", "script": {"id": "fbdbad30-f6c9-403d-9abb-878e4efa2be2", "exec": ["", "", "pm.test(\"Unset user token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(\"User logged out successfully.\");", "    pm.expect(pm.response.code).to.eql(200);", "    pm.environment.set(\"token\", \"\");", "});", "", ""], "type": "text/javascript"}}], "id": "993cf1a3-6039-40e4-84ed-b5e747fe0668", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{token}}"}}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "Admin@12345", "type": "text", "disabled": true}]}, "url": "{{base_url}}/logout", "description": "This post request must contain <PERSON><PERSON> token in the header and the api will remove that token from the database. The requests without the token will throw unathenticated error."}, "response": []}, {"name": "User Logout -  unauthenticated", "event": [{"listen": "test", "script": {"id": "df9a74da-b726-4270-9215-c81487c7dc40", "exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"Unauthenticated.\");", "    });", "pm.test(\"Response time is less than 600ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(600);", "});", "", ""], "type": "text/javascript"}}], "id": "19bc3067-46d9-4a95-9c6b-73d500c04e38", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{email}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "password_confirmation", "value": "Admin@12345", "type": "text", "disabled": true}]}, "url": "{{base_url}}/logout", "description": "This post request must contain <PERSON><PERSON> token in the header and the api will remove that token from the database. The requests without the token will throw unathenticated error."}, "response": [{"id": "152c7f0b-d5bb-4e3f-9fdd-8cff5d6fe590", "name": "Unauthenticated error", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Admin@12345", "type": "text"}, {"key": "password_confirmation", "value": "Admin@12345", "type": "text", "disabled": true}]}, "url": "{{API_URL}}/logout"}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:15:04 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"message\": \"Una<PERSON><PERSON><PERSON><PERSON>.\"\n}"}, {"id": "128f158b-c667-4873-8159-1d94ec863cab", "name": "Successful Logout", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Admin@12345", "type": "text"}, {"key": "password_confirmation", "value": "Admin@12345", "type": "text", "disabled": true}]}, "url": "{{API_URL}}/logout"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Connection", "value": "keep-alive"}, {"key": "Date", "value": "Sat, 22 Apr 2023 09:20:31 GMT"}, {"key": "Server", "value": "Apache"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-Ratelimit-Limit", "value": "60"}, {"key": "X-Ratelimit-Remaining", "value": "59"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Authorization"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Via", "value": "1.1 vegur"}], "cookie": [], "responseTime": null, "body": "{\n    \"data\": {\n        \"token\": \"\"\n    },\n    \"message\": \"User logged out successfully.\"\n}"}]}], "id": "90050229-c892-4ead-a945-6b1ed7e92676"}, {"name": "Forgot Password - with valid email", "event": [{"listen": "prerequest", "script": {"id": "84a8db25-1bd4-428e-b534-7c00c8231f44", "exec": ["pm.variables.set(\"user_email\", \"<EMAIL>\");"], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "18755099-e8db-4414-8967-9fea60feb8d9", "exec": ["pm.test('Reponse code to be 200', function () {", "    pm.expect(pm.response.code).to.be.equal(200);", "});", "pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"Reset password link sent. Please check your email.\");", "    });", "", "//pm.environment.unset('user_token');"], "type": "text/javascript"}}], "id": "a8af72d8-eed9-408e-9f09-c74afab74950", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{user_email}}", "type": "text"}]}, "url": "{{base_url}}/forgot-password"}, "response": []}, {"name": "Forgot Password  with invalid email", "event": [{"listen": "prerequest", "script": {"id": "84a8db25-1bd4-428e-b534-7c00c8231f44", "exec": ["pm.variables.set(\"user_email\", \"<EMAIL>\");"], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "18755099-e8db-4414-8967-9fea60feb8d9", "exec": ["pm.test('Reponse code to be 200', function () {", "    pm.expect(pm.response.code).to.be.equal(200);", "});", "", "//pm.environment.unset('user_token');"], "type": "text/javascript"}}], "id": "618407d5-0e0b-4f5b-889f-f5a05cc27699", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{user_token}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "{{user_email}}", "type": "text"}]}, "url": "{{base_url}}/forgot-password"}, "response": []}], "id": "e271fb64-1bb1-4cd7-b5c1-cd8046071795"}], "id": "437c383f-b723-4b3b-8d49-b57e63d266fc", "event": [{"listen": "prerequest", "script": {"id": "c83241e3-78d1-4062-8439-ed23351b78b6", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "1a5b76e7-8445-4d2d-b855-c04ed54cfb04", "type": "text/javascript", "exec": ["// Response time to be within a certain threshold", "pm.test(\"Response time within threshold\", function () {", "    pm.expect(pm.response.responseTime).to.be.lessThan(3000);", "});"]}}]}], "event": [{"listen": "prerequest", "script": {"id": "7e7ac43d-b45d-44e9-a067-b9eadab6db28", "type": "text/javascript", "exec": ["", "pm.request.headers.add({", "    key : \"Content-Type\",", "    value : \"application/json\"", "});", "", "", "pm.request.headers.add({", "    key : \"Accept\",", "    value : \"application/json\"", "});", ""]}}, {"listen": "test", "script": {"id": "f1a34666-18b7-4f8e-8465-2cd818a2db2f", "type": "text/javascript", "exec": ["// Parse and set response as JSON, and save it in a variable for later use", "jsonData = JSON.parse(responseBody);", "// VALIDATE JSON", "tests[\"Validate JSON - Body - Present\"] = ((responseBody) !== undefined && (responseBody.length > 1))", "tests[\"Validate JSON - Response - Object\"] = (typeof jsonData) === 'object';", "tests[\"Validate JSON - Header - Content-Type - JSON\"] = postman.getResponseHeader(\"Content-Type\").has('application/json');", "", "// Check if response is present", "pm.test(\"Body is present\", function () {", "    ((responseBody) !== undefined && (responseBody.length > 1));", "});", "", "// Content type header to be JSON", "pm.test(\"Content Type header is JSON\", function () {", "    pm.response.to.be.header(\"Content-Type\", \"application/json\");", "});", "", "// Parse and set response as JSON, and save it in a variable for later use", "jsonData = JSON.parse(responseBody);"]}}], "variable": [{"key": "base_url", "value": "http://*********/api/v1", "type": "string"}, {"key": "web_url", "value": "http://*********", "type": "string"}, {"key": "admin_url", "value": "http://*********/admin", "type": "string"}, {"key": "user_token", "value": ""}, {"value": "", "type": "string", "disabled": true}]}