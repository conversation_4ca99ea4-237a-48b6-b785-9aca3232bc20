version: "3"
services:
  api:
    build:
      context: .
      dockerfile: dockerfiles/Ci.Dockerfile
    container_name: api
    depends_on:
      - pg
    restart: always
    ports:
      - "1010:80"
    environment:
      APP_NAME: "Fresh Car Core"
      DB_HOST: "pg"
      MAIL_HOST: "mailpit"
    networks:
      network:
        ipv4_address: *********
    extra_hosts:
      - host.docker.internal:host-gateway

  pg:
    image: postgres:15
    container_name: pg
    volumes:
      - pg-disk:/var/lib/postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: password
      POSTGRES_DB: testing
    networks:
      network:
        ipv4_address: *********

  mailpit:
    image: "axllent/mailpit:latest"
    container_name: mailpit
    restart: always
    networks:
      network:
        ipv4_address: *********

# On-disk storage of DB data, when containers are stopped
volumes:
  pg-disk: {}

# Local network for services running using this docker-compose config
networks:
  network:
    ipam:
      driver: default
      config:
        - subnet: *********/16
