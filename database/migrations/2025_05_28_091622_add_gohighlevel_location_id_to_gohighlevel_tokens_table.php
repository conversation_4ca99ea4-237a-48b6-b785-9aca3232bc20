<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('gohighlevel_tokens', function (Blueprint $table) {
            $table->foreignId('gohighlevel_location_id')
                ->constrained('gohighlevel_locations');
        });
    }

    public function down(): void
    {
        Schema::table('gohighlevel_tokens', function (Blueprint $table) {
            $table->dropColumn(['gohighlevel_location_id']);
        });
    }
};
