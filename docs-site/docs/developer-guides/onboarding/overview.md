---
sidebar_position: 1
---

# Core Documentation

Welcome to the Fresh Car Core documentation. This Laravel application provides car management and subscription services with a robust Google Cloud infrastructure.

## 🚀 Quick Start for New Developers

### Prerequisites

Before you begin, ensure you have:

- **Google Cloud CLI** installed and configured
- **Access** to the Fresh Car Google Cloud project
- **Docker** installed for local development
- **PHP 8.2+** and **Composer** for local development

### Initial Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd freshcar-lbp-core
   ```

2. **Set up Google Cloud access** (see [Google Cloud Setup](/docs/setup/google-cloud))

3. **Configure local environment**

   ```bash
   cp .env.example .env
   # Edit .env with your local configuration
   ```

4. **Install dependencies**

   ```bash
   composer install
   npm install
   ```

5. **Set up Cloud Run Jobs** (optional, for running Artisan commands)

   ```bash
   ./scripts/setup-cloud-run-jobs.sh
   ```

## 🏗️ Architecture Overview

### Application Stack

- **Framework**: Laravel 11.x
- **Database**: PostgreSQL (Cloud SQL)
- **Hosting**: Google Cloud Run
- **Background Jobs**: Cloud Run Jobs
- **Secrets**: Google Secret Manager
- **Storage**: Google Cloud Storage (if applicable)

### Environments

- **Local Development**: Docker Compose setup
- **Staging**: Automatic deployment on main branch push
- **Production**: Manual promotion from staging

### Key Features

- **Car Management**: CRUD operations for user vehicles
- **Subscription System**: Car club subscriptions with Stripe integration
- **Background Processing**: Queue workers and scheduled tasks
- **API Versioning**: RESTful APIs with proper versioning
- **Authentication**: Laravel Sanctum for API authentication
