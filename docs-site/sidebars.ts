/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */

// export default sidebars;
export default {
    freshcarSidebar: [
        "intro",
        {
            type: "category",
            label: "User Guides",
            items: [
                {
                    type: "autogenerated",
                    dirName: "user-guides",
                },
            ],
        },
        {
            type: "category",
            label: "Developer Guides",
            items: [
                {
                    type: "autogenerated",
                    dirName: "developer-guides",
                },
            ],
        },
        {
            type: "category",
            label: "Fresh Car Apps",
            items: [
                {
                    type: "autogenerated",
                    dirName: "freshcar",
                },
            ],
        },
        // Explicitly add API documentation
        {
            type: "category",
            label: "API Reference",
            items: [
                {
                    type: "category",
                    label: "Fresh Car API V3",
                    link: {
                        type: "doc",
                        id: "api/freshapi/fresh-car-api",
                    },
                    items: require("./docs/api/freshapi/sidebar.ts"),
                },
                {
                    type: "category",
                    label: "Fresh Car API V2",
                    link: {
                        type: "doc",
                        id: "api/freshapi/fresh-car-api",
                    },
                    items: require("./docs/api/freshapi/2.0.0/sidebar.ts"),
                },
            ],
        },
    ],
};
