---
id: get-admin-valeters-get-all-valeters
title: "getAdminValetersGet-all-valeters"
description: "getAdminValetersGet-all-valeters"
sidebar_label: "getAdminValetersGet-all-valeters"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v5DYM/SsCz04mDdCLb4u2u0hRFAsk3R6COdA2xxYiSypJTTo1/N8LyeOZJLMfucxY4uPjEym9CRR7gfoRPnSj9V/QkRILbCtgkhi8kEA9we3NTf7rSFq2UW3wUMN9alsSgXmuQKhNbPUA9eMEDSETQ/24nbcVhEiMOeWugxp60lelPpFeoXNX+7V09abKOxJG0iEcyaGCiDpADRuMdoM5dbNiN/1lshDv81cWnthBDYNqlHqzcaFFNwTReoqBdb6Q9kcGmIUAKtgjW2zc0rGcsbRsh8kp1PDzzc1PMM9z9bbOjkmGq472VxjtNf6XmJ6pEask176c6HXZX2lPLsSRvK7Fv0WaUN9Jeq/YW9//iNDZPb2T8TOHLrV5cSLdni/KfTvQuLRqvS4T6CHSsWSeTIFAvQKq48fHwCPmjv7+90O5fNbvQkm36jL+Y5ZqfkE2Hz7f5cEQy6Jpf3sh82GwknEmctjbjsSQ72KwXsXsAhsdyHRB/yQ1hTdDv9xemzv9VsaIfulliyxV/jUtKvWBLa1rlxojqTkJebnfhPBkfS+VQd+ZJMQGkw7k1bblIZXi6Fx4lhIWo8FE4l3g0ZyemxhJ7WBwwRim3oouoULsQm99ZZwVPYtdUMR5x9PzcbcjRy9AL1Jencwtwk2KJdSRonVimsPr4j6NDXFlhJDbYaXNsBGfqGgbQ0euMi0Tnit9rWcXgHPzUuyW2HHLiKImWUbgW3Lue8xFxTrIr8zAWN+61OVoRJHnwJ1hElIpqTSidWZPbHfr0GCuIAbREX2+rB7L3e5JTfE3sxqc+ZElnt6J0r+6iQ6tz9zluU5H93sEjBYqKP5XvOnEdeGB2wqyy+WkaWpQ6C9285y3/0nE2dK3Z3Mrrv7Ccj/99pDLpFz0JOzNgy3eegyhPyxyUl5N04J4CE/k5+ywT3TIJ8trmLfzPP8PuapcZw==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminValetersGet-all-valeters"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/valeters/get-all-valeters"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminValetersGet-all-valeters

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      