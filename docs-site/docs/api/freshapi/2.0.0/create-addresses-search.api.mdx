---
id: create-addresses-search
title: "createAddressesSearch"
description: "createAddressesSearch"
sidebar_label: "createAddressesSearch"
hide_title: true
hide_table_of_contents: true
api: eJzlVk1v4zYQ/SvEoKdWiZMAPVSXRbrbBVIE3aBxW6CGC4ylscUNRXLJobNeQf+9GMqSnThp974XWxo+vhnOx6M6YNxEKBdwXdeBYoRlAYE+JYr8s6t3UHZQOctkWR7Re6MrZO3s7GN0VmyxaqhFeZJ9OlAtdN5FrlxNwsc7T1CCW32kiqEAH5ynwJqi7JqQZQettrdkN9xAeVmAR2YKFkr4Z4HXZ1/+XnaXxVW/uDj7aTkY5OmNenNkWXZX/Xcw+YwctN1A3xeAda0lcDR3R/7XaCIVwJqN4O/2weyzQfHWuYfk380/ZA6mz/xNHvy48N//8O1lQFgCRe9sHEK/uriQv5piFbQXcijhPlWVzJCgI1UpaN5BuehgRRgoQLlY9ssCJIScypsaSqgCIR+83hOGqoHiGfVrqJa4cUIjuYScuAZKmKHXMxzRszjCI4UthZiDSsFACQ2zj+VsZlyFpnGRy867wP1JBLcCUAMBFLDFoHFlxkIGHtKxxmQYSvjx4uJS8lY897MOFJuzmrZn6PU5fkmBHmkVNVM8t8Qnbt/RlozzLVkenb9GmpC/kvSecaPt5v8Ijd7SVzLeBVenSl4m0uWhCe5lUIZUja3QjX0qLqUyGQLlCCj2D+9daFEy+utf89xYMny/HxT6l8/YekNPx2lq/gK0Xbvsbd/m7+Vk6i0GdX13I3WkEIcjbK9OTjVvdBSc8sFtdU1Rka2905ajWruguCFVO/6NWGVegf55da5u+LUdLdoh9RWGWMivqpBp44Km8d2klYppNQVybF8596DtJhYKba1SpKAwcUOW9/KUnaMx7jHm5ajYKU9h7UKrpsmLKqaqUThgVKCNjjwsZWLjNtoWyujIh2AHFElmlaXHvbUmQ0egoy1PTmaGwFXyeakmRm2iWu2eOrepXVEo1DCxI63AWnygHFvrajKFyooweXopZyeAQ/KSr4e1vUlFRk5xKIGtyJj/Ys5RjIV8oQZK28qkWlY9xvjoQq0CReKYt1KL2qgtBb0eiyadKu3bYr5YLOZRGERPTaqnXhbHaZTy9ewNait8eaK7vSIuAL2GfBUMXFkMM9uyANE9wXTdCiP9EUzfi/lToiACvjzIXdbwAhrCOgt6Bw+0gxLeDh9JZ3OJROAmSUQnH0yiCpNm3324n8ug7z+02mF2Az5CkX9LALktctbzLSu2DgzaTcKNYAfOfMclOeaUimcqkiPdL6HdHUXYdQNi7h7I9iL7w4FY3qFf9n3/Ly2gj/g=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAddressesSearch"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/addresses/search"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAddressesSearch

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["postcode"],"type":"object","properties":{"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"}},"additionalProperties":false,"title":"PostcodeAddressesLookupDTO"}},"text/json":{"schema":{"required":["postcode"],"type":"object","properties":{"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"}},"additionalProperties":false,"title":"PostcodeAddressesLookupDTO"}},"application/*+json":{"schema":{"required":["postcode"],"type":"object","properties":{"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"}},"additionalProperties":false,"title":"PostcodeAddressesLookupDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      