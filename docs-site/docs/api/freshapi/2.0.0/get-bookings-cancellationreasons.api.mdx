---
id: get-bookings-cancellationreasons
title: "getBookingsCancellationreasons"
description: "getBookingsCancellationreasons"
sidebar_label: "getBookingsCancellationreasons"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v3DYQ/SvEnBWvayAX3dq0KRwEQQC76cHYw0ialQhTJDszXHcj6L8XpLTe9UcaX1ZLzps3w/l4Eyj2AvUd/BbCvfU9bCtgkhi8kEA9wdXlZf50JC3bqDZ4qOEmtS2JwDxXINQmtnqA+m6ChpCJob7bztsKQiTG7HLdQQ096RpEPqBvybliY0IJXqB6FuOn8JF0CCsxVBBRB6hhg9FumtVx077qKcR7YikZJ3ZQw6Aapd5sXGjRDUG0nmJgnV9k9TkDzEIAFeyRLTZuKVX2WGq1w+QUanh/efkLzPNcPY+zY5LhXUf7dxjtBX5PTA/UiFWSC1+e8zTs77QnF+JIXo/Bf0SaUN9IeqPYW9//jNDZPb2R8SuHLrX58Ei6PU3ITTvQuJTqOCcT6CHSGjJ3pkCgPgKq9c/HwCPmin76+7ZMnfW7UNytuoz/mFM1H5DNr1+vc2OIZclpf/UizdvBSsaZyGFvOxJDvovBehWzC2x0INMF/UJqCm+Gfru6MNf6I48R/VLLFlmq/GtaVOoDWzqeXWqMpOYxkfP748BWBn1nkhAbTDqQV9uW4S3B0bnwIMUsRoOJxLvAo3ncMzGS2sHggjFMvRVdTIXYhd76yjgrekp2QRHnG08P621Hjs5AZy5PXuaWxE2KxdSRonVimsPT4D6NDXFlhJDb4UibYSPeU8ltDB25yrRMeIr0Ws1eAE7FS7FbbOuVEUVNsrSgKMH/MZcsjo18pQfG+talLlsjijwE7gyTkEpxpRGtM3tiuzs2DeYKYhAd0edh9Vhmuyc1R2kzb5DCxyVR+lc30aH1mbjs6rTq3h1gtHldVl6o4DXt21aQ1S3jp6lBob/YzXO+/icRZw3fnkStyPiZzv75xy1UkGtyltOzRS2auprQH4pEupRP07QgbsM9+Tkr6z0d8qPyGebtPM//ARN0V64=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getBookingsCancellationreasons"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bookings/cancellationreasons"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getBookingsCancellationreasons

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      