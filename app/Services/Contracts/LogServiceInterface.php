<?php

namespace App\Services\Contracts;

interface LogServiceInterface
{
    /**
     * Store a log entry
     *
     * @param string $identifier
     * @param array $payload
     * @param array $metadata
     * @return array|null
     */
    public function storeLog(string $identifier, array $payload, array $metadata = []): ?array;

    /**
     * Retrieve logs by identifier
     *
     * @param string $identifier
     * @param array $options
     * @return array|null
     */
    public function getLogsByIdentifier(string $identifier, array $options = []): ?array;

    /**
     * Retrieve logs with filters
     *
     * @param array $filters
     * @param array $options
     * @return array|null
     */
    public function getLogs(array $filters = [], array $options = []): ?array;
}
