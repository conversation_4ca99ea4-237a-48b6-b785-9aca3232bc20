<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreLogRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'identifier' => [
                'required',
                'string',
                'min:1',
                'max:255',
            ],
            'payload' => [
                'required',
                'array',
            ],
            'metadata' => [
                'sometimes',
                'array',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'identifier.required' => 'The identifier field is required.',
            'identifier.string' => 'The identifier must be a string.',
            'identifier.min' => 'The identifier must be at least 1 character.',
            'identifier.max' => 'The identifier may not be greater than 255 characters.',
            'payload.required' => 'The payload field is required.',
            'payload.array' => 'The payload must be an array.',
            'metadata.array' => 'The metadata must be an array.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'identifier' => 'identifier',
            'payload' => 'payload',
            'metadata' => 'metadata',
        ];
    }
}
