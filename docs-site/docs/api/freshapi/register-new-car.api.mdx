---
id: register-new-car
title: "Register a new car"
description: "This endpoint allows you to register a new car for the authenticated user. The car details are provided in the request body."
sidebar_label: "Register a new car"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Register a new car"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/v3/api/cars"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint allows you to register a new car for the authenticated user. The car details are provided in the request body.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}},"text/json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}},"application/*+json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"201":{"description":"Car created successfully"}}}
>
  
</StatusCodes>


      