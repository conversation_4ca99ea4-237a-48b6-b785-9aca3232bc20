<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('gohighlevel_locations', function (Blueprint $table) {
            $table->string('private_integration_key')->nullable()->after('client_secret');
        });
    }

    public function down(): void
    {
        Schema::table('gohighlevel_locations', function (Blueprint $table) {
            $table->dropColumn('private_integration_key');
        });
    }
};
