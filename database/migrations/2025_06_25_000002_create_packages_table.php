<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->string('name', 200);
            $table->text('description')->nullable();
            $table->integer('duration_minutes')->nullable(); // Duration in minutes
            $table->text('included_features')->nullable(); // JSON or text list of features
            $table->text('feature_description')->nullable();
            
            // Price points
            $table->decimal('standard_price', 10, 2)->nullable();
            $table->decimal('higher_price', 10, 2)->nullable();
            $table->decimal('lower_price', 10, 2)->nullable();
            $table->string('currency', 3)->default('GBP');
            
            // Service group relationship
            $table->foreignId('service_group_id')->constrained()->onDelete('cascade');
            
            // Visibility and status
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index(['service_group_id', 'is_active']);
            $table->index('is_active');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
