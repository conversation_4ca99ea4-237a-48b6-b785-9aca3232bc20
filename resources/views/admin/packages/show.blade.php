<x-app-layout>
    <x-slot name="header">
        Package: {{ $package->name }}
    </x-slot>

    <div class="py-4">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Basic Information</h3>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->name }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Service Group</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->serviceGroup->name ?? 'N/A' }}</dd>
                        </div>

                        <div class="lg:col-span-2">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->description ?: 'No description provided' }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->durationFormatted }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">
                                <x-bullet-boolean :value="$package->isActive"/>
                            </dd>
                        </div>

                        <!-- Features -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Features</h3>
                        </div>

                        @if($package->includedFeatures && count($package->includedFeatures) > 0)
                            <div class="lg:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Included Features</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">
                                    <ul class="list-disc list-inside space-y-1">
                                        @foreach($package->includedFeatures as $feature)
                                            <li>{{ $feature }}</li>
                                        @endforeach
                                    </ul>
                                </dd>
                            </div>
                        @endif

                        @if($package->featureDescription)
                            <div class="lg:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Feature Description</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->featureDescription }}</dd>
                            </div>
                        @endif

                        <!-- Pricing -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Pricing</h3>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Price Range</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->priceRange }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Currency</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->currency }}</dd>
                        </div>

                        @if($package->standardPrice)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Standard Price</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->currency === 'GBP' ? '£' : '$' }}{{ number_format($package->standardPrice, 2) }}</dd>
                            </div>
                        @endif

                        @if($package->higherPrice)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Higher Price</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->currency === 'GBP' ? '£' : '$' }}{{ number_format($package->higherPrice, 2) }}</dd>
                            </div>
                        @endif

                        @if($package->lowerPrice)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Lower Price</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->currency === 'GBP' ? '£' : '$' }}{{ number_format($package->lowerPrice, 2) }}</dd>
                            </div>
                        @endif

                        <!-- Usage Statistics -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Usage Statistics</h3>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Subscriptions</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->subscriptionsCount ?? 0 }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Subscriptions</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->activeSubscriptionsCount ?? 0 }}</dd>
                        </div>

                        <!-- Metadata -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6">Metadata</h3>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Sort Order</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->sortOrder }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->createdAt ? \Carbon\Carbon::parse($package->createdAt)->format('M j, Y g:i A') : 'N/A' }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-300">{{ $package->updatedAt ? \Carbon\Carbon::parse($package->updatedAt)->format('M j, Y g:i A') : 'N/A' }}</dd>
                        </div>
                    </div>

                    <div class="mt-8 flex items-center justify-end space-x-3">
                        <a href="{{ route('admin.packages.index') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            Back to Packages
                        </a>
                        <a href="{{ route('admin.packages.edit', $package->id) }}"
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            Edit Package
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
