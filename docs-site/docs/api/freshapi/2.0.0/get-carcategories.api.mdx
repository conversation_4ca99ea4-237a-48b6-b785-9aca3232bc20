---
id: get-carcategories
title: "getCarcategories"
description: "getCarcategories"
sidebar_label: "getCarcategories"
hide_title: true
hide_table_of_contents: true
api: eJyNVF1v2zoM/SsCn72mK7AXvw29t0OHYRjQbvehyANtM7ZQWdJIKl1m+L9fSE6a9GMfL3EkHh4ekdKZQLEXqO/gEvkSlfrAO1hXwCQxeCGBeoKL8/P86UhatlFt8FDDTWpbEoF5rkCoTWx1B/XdBA0hE0N9t57XFYRIjDnluoMaetJL5HYpZEmgesb6CmAkHcI+GSqIqAPUsMJoV+0zqBBviaXISOyghkE1Sr1audCiG4JoPcXAOr8o/CkDzEIAFWyRLTZuOX/OWBqwweQUanh3fv4W5nmuntfZMMnwpqPtG4z2DH8mpgdqxCrJmS/6n5b9h7bkQhzJ66H4r0gT6l+S3ij21vd/InR2S3/J+IVDl9q8eCRdH8d+0w40Lq06DH8C3UXal8yTKRCoD4Bq/+cq8Ii5ox//uy1XyfpNKOlWXcZfZanmEtm8/3KdB0Msi6btxQuZt4OVjDORw9Z2JIZ8F4P1KmYT2OhApgv6mdQU3gz9dnFmrvVXGSP6pZctslT51xxv3H7tUmMkNY9CTvebEO6t76Uy6DuThNhg0oG82rY8i1IcnQsPUsJiNJhIvAk8msfHI0ZSOxhcMIapt6JLqBC70FtfGWdFj2IXFHHe8fSw3+3I0QnoJOXJydwi3KRYQh0pWiem2T0t7tPYEFdGCLkdDrQZNuI9FW1j6MhVpmXCY6XXevYCcGxeit0S228ZUdQkywh8S879jrmoOAzylRkY61uXuhyNKPIQuDNMQiollUa0zmyJ7eYwNJgriEF0RJ8vq8dyt3tS81t7e3wVSj90FR1an5nK45z2znYHGC1U8NTb1hVk98rhaWpQ6Cu7ec7b3xNxNt710bSK954Y54d/b6GCfOYTCc8eYvHMfQj9rligS3k1TQviNtyTn7Nz3tMunyGvYV7P8/w/8Vs7Ug==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getCarcategories"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/carcategories"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getCarcategories

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      