#!/bin/bash

# Artisan Production - Run Laravel Artisan commands in Cloud Run Jobs (Production Environment)
# This is a convenience wrapper that automatically sets ENVIRONMENT=production

export ENVIRONMENT=production

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Execute the main artisan-cloud script with production environment
exec "$SCRIPT_DIR/artisan-cloud" "$@"
