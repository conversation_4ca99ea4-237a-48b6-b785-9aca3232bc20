---
id: update-booking-status
title: "Update booking status"
description: "This endpoint updates the status of a car club booking."
sidebar_label: "Update booking status"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update booking status"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/v3/api/car-club/bookings/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint updates the status of a car club booking.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"text/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"application/*+json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Booking status updated successfully"},"404":{"description":"Booking not found"}}}
>
  
</StatusCodes>


      