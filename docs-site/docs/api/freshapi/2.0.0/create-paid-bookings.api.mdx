---
id: create-paid-bookings
title: "createPaid-bookings"
description: "createPaid-bookings"
sidebar_label: "createPaid-bookings"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createPaid-bookings"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/paid-bookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createPaid-bookings

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"booking":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateBookingDTO"}},"text/json":{"schema":{"type":"object","properties":{"booking":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateBookingDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"booking":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateBookingDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      