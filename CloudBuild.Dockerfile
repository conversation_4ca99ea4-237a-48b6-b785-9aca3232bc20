FROM devopsfnl/image:php-8.3-np

# Use the PORT environment variable in Apache configuration files.
ARG PORT
ENV PORT=${PORT}
RUN sed -i 's/80/${PORT}/g' /etc/apache2/sites-available/000-default.conf /etc/apache2/ports.conf

# Copy project files to the container
COPY . /var/www/html/

# Set proper permissions for Laravel storage and bootstrap directories
RUN chown -R www-data:www-data /var/www/html
RUN chmod -R 755 /var/www/html
RUN chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

RUN composer install --no-dev --optimize-autoloader

RUN npm install
RUN npm run build:all

ENTRYPOINT ["/var/www/html/cloud-run-web-entrypoint"]
