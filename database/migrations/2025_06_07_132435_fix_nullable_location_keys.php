<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // First, update any NULL values to empty strings
        DB::statement("UPDATE gohighlevel_locations SET client_id = '' WHERE client_id IS NULL");
        DB::statement("UPDATE gohighlevel_locations SET client_secret = '' WHERE client_secret IS NULL");
        
        // Then make the columns NOT NULL with default empty strings
        Schema::table('gohighlevel_locations', function (Blueprint $table) {
            $table->string('client_id')->default('')->nullable(false)->change();
            $table->string('client_secret')->default('')->nullable(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('gohighlevel_locations', function (Blueprint $table) {
            $table->string('client_id')->nullable(false)->default(null)->change();
            $table->string('client_secret')->nullable(false)->default(null)->change();
        });
    }
};