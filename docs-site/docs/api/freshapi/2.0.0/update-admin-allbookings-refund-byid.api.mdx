---
id: update-admin-allbookings-refund-byid
title: "updateAdminAllbookingsRefundByid"
description: "updateAdminAllbookingsRefundByid"
sidebar_label: "updateAdminAllbookingsRefundByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVktv4zYQ/ivEnPpQ4jRFL7ol2S6aomiDxG0PgQ9jcWxxQ5FcPux1Bf33YkhZttfZNse92CI5b37zDXuIuA5QP8ON1rfWviizDjeyUwYWFTj02FEkzxI9GOwIalgWsXsJFSgDNTiMLVTg6WNSniTU0SeqIDQtdQh1D3HnWFGZSGvyUMHK+g5j2frxGoZhUdQpxFsrd6zTWBPJRP5E57RqMCprZh+CNbx3ZtwuP1AToQLnrSMfFQU+VeGRVslIDmuSXVqrCQ0MQwUopWLLqB+OFFeoA1UQVdSsUGyM9Xk3/yNrRvoUv6Z4jsv03fdfTWCs6yk4a0LxdH11xX+SQuOVY5NQw1NqGgohewrUJK/iLoNuSejJQ/28YJCw45zhvYQakpMYKcP1RusRl2MetzvFAD318gaFjmJrZUF1w7DO6K5hhk7NkDVneFCd+aw766emGIAT8Jt90ySvoYY2Rhfq2UzbBnVrQ6x7Z30cziL8jQVEMQAVbNArXOpSONYolVth0tw+P11d/cAlrj73s/IU2gtJmwt06hL/SZ62tAwqUrg0FM/cvqMNaes6MnHv/EtGE8Y3Gn2KuFZm/X8GtdrQGy0+eCtTw4vJ6OKAlyeGeinVHjUTktkljJyUkV0EqvHj/Z6Qfv17njHI7fN4YKSfP2HnGOGnjcJENzALrmz2NXbBe85L3KEXNw/3fIvkQ0lgc32W07xVgeWE83ajJAVBRjqrTAxiZb2ILQlp4+8URbbLon9dX4r7+CWNDk0pfIM+VPwrGoy0tl7Rfq3TUoS0nAI53t+DuxJopEiBvMAUWzJxpJfsHLW225CPg4hWOPJM62Jq0SBCalqBRUZ4WqsQy1E2rO1amUpoFeIh2CJFnncMbcddSZqOhI5UTjLTJXCRXD6SFFHpIJa7U+cmdUvylQiEvmn3ZlmswxfKsXVWkq5E4wkPnl6r2ZnAoXiZbPhs3BIhYkyhXIFpSOv/spyj2F/kK3cglGl0knzqMISt9VJ4ChRDVqUOlRYb8mq1vzQYKnA2xA7zYBineaFEkTlRHJGiKCgXr/Ho1FV5BDqNyrDx3Nz9SJjPgE4Bjw1+TFRwRJr5rcDGoYL68JpYVMDMyKp9v8RAf3o9DLz9MZHnabA4EGLmVqkCf8tpDH0W5fSGgG8ex8fJtwKq16MfN9HsMu/qxCuo4IV2J4+eYTFU0BLKPJX68fyuuLqYs5WD/tnThflqGjEPN/O7X5iDxjcPow5q8LjlEuG2BGALJFgg7/Wg0awTrlm2GM3zOXHZD6P7lOByqK+m2PdFYm5fyAzDlHHkNWc7DP8CcOeWUg==
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminAllbookingsRefundByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/allbookings/refund/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminAllbookingsRefundByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isRefunded":{"type":"boolean"}},"additionalProperties":false,"title":"RefundBookingDTO"}},"text/json":{"schema":{"type":"object","properties":{"isRefunded":{"type":"boolean"}},"additionalProperties":false,"title":"RefundBookingDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isRefunded":{"type":"boolean"}},"additionalProperties":false,"title":"RefundBookingDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      