### Prerequisites

1. Git - To clone this repo
2. Docker with compose plugin - To run the app

[Having Docker issues on Mac?](https://github.com/founderandlightning/fl-laravel_boilerplate/wiki/docker-setup#the-mac-problem)

### Run locally

```
docker compose up -d
```

_When running this the first time, it will take a while to download the images and build the containers_

```
http://10.10.1.1
```

### More info

Checkout [Wiki](https://github.com/founderandlightning/fl-laravel_boilerplate/wiki) for more info about :

- [Coding Guidelines](https://github.com/founderandlightning/fl-laravel_boilerplate/wiki/Coding-Guidelines)
- [Composer.json](https://github.com/founderandlightning/fl-laravel_boilerplate/wiki/Composer-json)
- [Docker setup](https://github.com/founderandlightning/fl-laravel_boilerplate/wiki/Docker-setup)
- [DevOps](https://github.com/founderandlightning/fl-laravel_boilerplate/wiki/DevOps)

### GoHighLevel access

1. In admin panel ({app}/admin/gohighlevel/locations) click "Add Location"
    1. Provide any name
    2. location Id: To find in app.gohighlevel.com (you must be logged in) -> Settings -> on the top right of the section "General Information" 
    3. App Client Id, Client Secret: https://marketplace.gohighlevel.com/ -> My Apps -> choose the app -> on the left menu: Advanced Settings -> Auth option.
        1. There is a section "Client Keys". Click Add and create new pair of keys: Client Id and Client Secret.
        2. Be aware, that client secret is available for copy only once, and later will be always hidden. 
        3. Copy Client Id, Client Secret into fields in the form. 
    4. Api Base Url Authorization and App Base URL has filled by default values. Please take a look, that Api Base Url Authorization should be in the same domain as has place, where we have crated client id and secret (like here: marketplace.gohighlevel.com)    
    5. App Base URL - can be different take a look into gohighlevel documentation: https://highlevel.stoplight.io/docs/integrations/a04191c0fabf9-authorization#3-get-the-apps-authorization-page-url
    6. Save
2. You should have added your new location. with buttons: Auhotize, and Redirect Url Value. Copy this redirect url value and paste into: 
    marketplace.gohighlevel.com -> chosen app -> Left Menu: Advanced Settings -> Auth, "Redirect URLs" section. Paste this redirect url 
    and click "+ Add". And click "Save" 
3. Now we are ready for authorization the platform. Click "Authorize". It leads into "marketplace.gohighlevel.com". There Gohighlevel will nform you that the app (as you named in marketplace.gohighlevel dashboard) requests for permissions. Click "next". Select the Account (the same for which you provided the Location Id in step 1.2) 
4. You should be returned into platform admin panel and the platform is ready for connection with provided Location.
5. Bon appetit

## Google Cloud Setup

### Enable APIs

- sqladmin.googleapis.com
- cloudresourcemanager.googleapis.com
- secretmanager.googleapis.com
- run.googleapis.com
- cloudbuild.googleapis.com
- artifactregistry.googleapis.com
- containerregistry.googleapis.com
- container.googleapis.com
- cloudsql.googleapis.com
- compute.googleapis.com
- iam.googleapis.com
- iamcredentials.googleapis.com
- logging.googleapis.com
- monitoring.googleapis.com
- pubsub.googleapis.com
- storage.googleapis.com
- storage-component.googleapis.com


