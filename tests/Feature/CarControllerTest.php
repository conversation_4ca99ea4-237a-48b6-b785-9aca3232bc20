<?php

namespace Tests\Feature;

use App\Models\Car;
use App\Models\CarCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CarControllerTest extends TestCase
{
    use RefreshDatabase;

    public User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    public function test_user_can_list_their_cars(): void
    {
        $category = CarCategory::factory()->create();
        Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v3/cars');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'registrationNumber',
                        'makeAndModel',
                        'category',
                        'colour',
                        'year',
                    ]
                ]
            ]);
    }

    public function test_user_can_create_a_car(): void
    {
        $carData = [
            'registrationNumber' => 'AB12CDE',
            'makeAndModel' => 'BMW 3 Series',
            'category' => 'Medium',
            'colour' => 'Blue',
            'year' => '2020',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v3/cars', $carData);

        $response->assertStatus(201)
            ->assertJson([
                'message' => 'Car created successfully'
            ]);

        $this->assertDatabaseHas('cars', [
            'user_id' => $this->user->id,
            'registration_number' => 'AB12CDE',
            'make_and_model' => 'BMW 3 Series',
        ]);
    }

    public function test_user_can_view_their_car(): void
    {
        $category = CarCategory::factory()->create();
        $car = Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/v3/cars/{$car->id}");

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'id' => $car->id,
                    'registrationNumber' => $car->registration_number,
                ]
            ]);
    }

    public function test_user_can_delete_their_car(): void
    {
        $category = CarCategory::factory()->create();
        $car = Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson("/api/v3/cars/{$car->id}");

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Car deleted successfully'
            ]);

        $this->assertSoftDeleted('cars', ['id' => $car->id]);
    }

    public function test_user_cannot_access_other_users_cars(): void
    {
        $otherUser = User::factory()->create();
        $category = CarCategory::factory()->create();
        $car = Car::factory()->create([
            'user_id' => $otherUser->id,
            'category_id' => $category->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/v3/cars/{$car->id}");

        $response->assertStatus(404);
    }

    public function test_user_can_update_their_car(): void
    {
        $category = CarCategory::factory()->create();
        $car = Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
        ]);

        $updateData = [
            'makeAndModel' => 'Audi A4',
            'colour' => 'Red',
            'year' => '2021',
        ];

        $response = $this->actingAs($this->user)
            ->patchJson("/api/v3/cars/{$car->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Car updated successfully'
            ]);

        $this->assertDatabaseHas('cars', [
            'id' => $car->id,
            'make_and_model' => 'Audi A4',
            'colour' => 'Red',
            'year' => '2021',
        ]);
    }

    public function test_user_can_search_cars(): void
    {
        $category = CarCategory::factory()->create();

        Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
            'make_and_model' => 'BMW 3 Series',
            'make' => 'BMW',
            'model' => '3 Series',
            'registration_number' => 'AB12CDE',
        ]);

        Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
            'make_and_model' => 'Audi A4',
            'make' => 'Audi',
            'model' => 'A4',
            'registration_number' => 'XY98ZAB',
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v3/cars?search=BMW');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_user_can_filter_cars_by_make(): void
    {
        $category = CarCategory::factory()->create();

        Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
            'make' => 'BMW',
        ]);

        Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
            'make' => 'Audi',
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v3/cars?make=BMW');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
    }

    public function test_user_can_sort_cars(): void
    {
        $category = CarCategory::factory()->create();

        Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
            'year' => 2020,
        ]);

        Car::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $category->id,
            'year' => 2022,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v3/cars?sortBy=year&orderBy=desc');

        $response->assertStatus(200);

        $cars = $response->json('data');
        $this->assertEquals(2022, $cars[0]['year']);
        $this->assertEquals(2020, $cars[1]['year']);
    }

    public function test_user_can_paginate_cars(): void
    {
        // Create a few categories to reuse
        $categories = CarCategory::factory()->count(3)->create();

        // Create cars with random categories from the created ones
        Car::factory()->count(25)->create([
            'user_id' => $this->user->id,
            'category_id' => fn () => $categories->random()->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v3/cars?perPage=10');

        $response->assertStatus(200)
            ->assertJsonCount(10, 'data')
            ->assertJsonStructure([
                'data',
                'links',
                'meta' => [
                    'current_page',
                    'per_page',
                    'total',
                ]
            ]);
    }

    public function test_unauthenticated_user_cannot_access_cars(): void
    {
        $response = $this->getJson('/api/v3/cars');

        $response->assertStatus(401);
    }
}
