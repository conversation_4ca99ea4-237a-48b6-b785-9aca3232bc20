---
id: update-preferredvaleter-byid
title: "updatePreferredvaleterByid"
description: "updatePreferredvaleterByid"
sidebar_label: "updatePreferredvaleterByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVktzGzcM/iscnPpYP+pOL3tL7GTqTqf1xGp68OgALSEtYy65IUEp6s7+9wzIlWRbtptjLtISBD6AIPCBAzCuItR3cBNoSSGQ/oiWmALMK+gxYCcL0RjAYUdQQ5Mi+47CU4trDRUYBzX0yC1UEOhzMoE01BwSVRCbljqEegDe9oJkHNOKAlSw9KFDLqJfL2Ac58WcIr/1eis2jXdMjuUT+96aBtl4d/YpeieyI3C/+EQNQwV98D0FNhRl18QrknD1A9WF95bQwThWgFobAUZ788BuiTZSBWzYisGtX3KBeefY8PZq9nc2ZvrC31dED1P108/fUWhiHij23sXi6+L8XP40xSaYXlDFNjUNxZidRWpSMLzNtbggDBSgvptLqYjvfMZrDTWkXiPTvjzXpTzfbo0U6GP8V1U74tbrUs+NFHSu6xrOsDdnT6v/bHi5L0aQ6MN610gpWKihZe5jfXZmfYO29ZHrofeBx6Mg/xQFVQCggjUGgwtbsiYWJW1LTFY66Lfz818kv9VTP8tAsT3RtD7B3pzifynQhhbRMMVTR3zk9orWZH3fkeOd85dAE/I3gt4yroxb/R+gNWv6RsSb4HVqZLEHnR+K5VYqvaRqVzL7ShaXMNFSruyiUE0f73ec9Me/s1yA0j0fDqT07gt2vVT4oz4RqhuFB5c+u5qa4L0cS11iUG9uruUSKcQS//ri6Eiz1kTRU33wa6MpKnK698ZxVEsfFLektOe/iFXGFdWPF6fqml+y6NCVvDcYYiW/qkGmlQ+GdmubFiqmxT6Qh/KF9/fGrWKl0GmVIgWFiVtyPJFLdo7W+k3M21GxVz0FIXa1b8+oYmpahUVHBVqZyGUrA1u/Mq5S1kQ+BFu0KIjE0WaSakn4QemByaOT2RK4Sn3e0sRobFSL7WPnLnULCpWKhKFpd7Ci1uE95dg6r8lWqgmEB0/P5exI4ZC8TDeyN4lUZOQUyxW4hqx9DTlHsbvIZ+5AGdfYpGW3xxg3PmgVKBLHbEodGqvWFMxyd2kwVtD7yB3msTAN+EKK6ikrqucYdN9MefT1Fo0T0NzTw0SYd4C9ger4iVFB/cpjYl6BsKLYD8MCI/0T7DiK+HOiIGNgfiDDzKvaRPnW+xH0JNT9EwJ++DC9TX5UUD1/hEmIbps51yZZQQX3tH39ETTOxwpaQp3n0zAZXBbfJzOBPQAePWWEvPaD5+bN7PJ3IaTpDSQ1CDUE3MjzCjclIl8KRBSybACLbpVwJboFNA/rJJdxmOOP2S6H+uyZh6FozPw9uXHcp4BlLacdx6/bkKbp
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updatePreferredvaleterByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/PreferredValeter/{customerPreferredValeterId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updatePreferredvaleterByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerPreferredValeterId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      