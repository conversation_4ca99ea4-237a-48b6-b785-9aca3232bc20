---
id: create-account-updateuserdetails
title: "createAccountUpdateuserdetails"
description: "createAccountUpdateuserdetails"
sidebar_label: "createAccountUpdateuserdetails"
hide_title: true
hide_table_of_contents: true
api: eJztV1Fv4zYM/isCHzenTbMVt/ntrt0BHQ5tsaYbsCIPjM0kusqST6LS6xn57wNlx06Tdr1770tiUeRHiqQ+2g0wLgPkd/C+KFy0DLMMPH2JFPiDKx8hb6BwlsmyPGJdG10ga2ePPwdnRRaKFVUoT2KnPZUCJ0ZY8GWs5uQhg4X2gS+xIsjAYPc4y4Afa4Ic3PwzFQwZ1N7V5FlTSK5jYFeRvyhl1elqy7RsQZ2vkFvRLxPIwEZjcG4IcvaRNhmwZlk1UOHXT2SXvIL8ZNy7Dey1XT5nOMT71Ph0nEGlbY+1D7XZOd4PWz5N2g+bl8h0tfigvSg0B2fssyV6I9apFnsH32SAZamlwGiud2qxQBOozyfc1gJy1pXnnBi1CefTKxAEpq/81h5v7fFCe+ySyE8/v/XJW5881yeC4SnUzoa2iJPxWP5KCoXXtUBDDjexKCiE1FeBiug1P0J+18Cc0Mvh72abWQYSQGo46QAoPCFTN/DaEGIgX7buIdvz8ap6RbxyAly7kJoPJXNwjLU+7syOW7vb0J8SJGC/Jh9SvNEbyGHFXIf8+Ni4As3KBc6b2nneHMT0SRRUCwAZrNFrSX9KlFi0mVpgNFKp0/H4RFKa7ftZeAqrUUnrEdb6CL9FTw80D5opHFniA7fntCbj6oosb52/BBqRvxP0hnGp7fI1QKPX9J2I196VsZBFDzob+uNGmKZN1bZL+i4Xl1KZpAL5ViHrHj5ue//Pf6ap54S9/hpel/74ilXdMsUu54yHjh+u0cAMg3C484Ns7zYPG0/uKUzGk19H43ejye/Tk9P89CSf/HY0fnfyr9xobRcuHbIL4qMkVJ2hV++vL6R9yIc2c+vJQTKnKx1ET9XerXVJQZEta6ctB7VwXvGKVOn4klglXFH9e3KkLvgliwptW/ECfcjkVxXItHRe03Zt4lyFOO8D2ZXPnbvXdhkyhbZUchcVRl6R5W6sJOdojHsIaTsodqomL9Slei4IKsRipbDVUZ6WOnC7lYCNW2qbKaMDD8G2WiQlUJYeOmlJhnaUdkyenMy0gatYp62OQdT88alzm0qdqUDoi9UWVtQqvKcUW+VKMplK1NR7ei5nBwpD8qJQkux1IhUYOYa2BLYgY/4POUWxLeQzNVDaFiaWsltjCA/Ol8pTIA7JlCrURq3J68W2aNKpwqAVphcC296Dln1Vx6PqVbrur3J6C60NaivAiVGajprvAGsNWf/Nk8EhPc8yEAIW5aaZY6BbbzYbEX+J5GXIzAbeTXMmgxVhmYZOA/f0CDmctZ9Oo6mEJOomSmgHn1FCT/0Uub66mQrjdJ9fUmrIweMDZOk3B5CJluqQ3pdE1oBBu4y4FN0WM03h+GSI79FZirTbQvu4E2HTtBpTd092I/OnPRDLGjazzWbzH6sJ90M=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAccountUpdateuserdetails"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/UpdateUserDetails"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAccountUpdateuserdetails

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["contactNumber","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true}},"additionalProperties":false,"title":"UpdateCustomerDetailsDTO"}},"text/json":{"schema":{"required":["contactNumber","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true}},"additionalProperties":false,"title":"UpdateCustomerDetailsDTO"}},"application/*+json":{"schema":{"required":["contactNumber","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true}},"additionalProperties":false,"title":"UpdateCustomerDetailsDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      