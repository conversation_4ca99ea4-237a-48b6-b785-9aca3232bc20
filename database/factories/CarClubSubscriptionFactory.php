<?php

namespace Database\Factories;

use App\Models\Car;
use App\Models\CarClubSubscription;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CarClubSubscriptionFactory extends Factory
{
    protected $model = CarClubSubscription::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'car_id' => Car::factory(),
            'status' => CarClubSubscription::STATUS_ACTIVE,
            'frequency' => $this->faker->numberBetween(1, 4), // 1-4 weeks
            'cleans' => $this->faker->numberBetween(1, 3), // 1-3 cleans
            'resource_id' => $this->faker->numberBetween(1, 100),
            'resource_name' => $this->faker->words(2, true),
            'category' => $this->faker->randomElement(['basic', 'premium', 'deluxe']),
            'package_group_id' => $this->faker->numberBetween(1, 10),
            'customer_name' => $this->faker->name(),
            'amount' => $this->faker->randomFloat(2, 15.00, 99.99),
            'currency' => 'GBP',
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CarClubSubscription::STATUS_ACTIVE,
        ]);
    }

    public function paused(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CarClubSubscription::STATUS_PAUSED,
            'paused_at' => now(),
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CarClubSubscription::STATUS_CANCELLED,
            'cancelled_at' => now(),
            'cancellation_reason_id' => $this->faker->numberBetween(1, 5),
            'cancellation_reason_notes' => $this->faker->sentence(),
        ]);
    }

    public function withStripeSubscription(): static
    {
        return $this->state(fn (array $attributes) => [
            'stripe_subscription_id' => 'sub_' . $this->faker->uuid(),
            'stripe_customer_id' => 'cus_' . $this->faker->uuid(),
            'stripe_price_id' => 'price_' . $this->faker->uuid(),
        ]);
    }
}
