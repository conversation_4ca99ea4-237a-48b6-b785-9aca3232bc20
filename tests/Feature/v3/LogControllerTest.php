<?php

namespace Tests\Feature\v3;

use App\Services\Contracts\LogServiceInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\TestCase;

class LogControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private LogServiceInterface $logServiceMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->logServiceMock = Mockery::mock(LogServiceInterface::class);
        $this->app->instance(LogServiceInterface::class, $this->logServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_store_log_successfully(): void
    {
        $identifier = 'Create User';
        $payload = ['user_id' => 123, 'email' => '<EMAIL>'];
        $metadata = ['source' => 'frontend'];

        $expectedResponse = [
            'insertedId' => '507f1f77bcf86cd799439011'
        ];

        $this->logServiceMock
            ->shouldReceive('storeLog')
            ->once()
            ->with($identifier, $payload, $metadata)
            ->andReturn($expectedResponse);

        $response = $this->postJson('/api/v3/logs', [
            'identifier' => $identifier,
            'payload' => $payload,
            'metadata' => $metadata,
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'data' => $expectedResponse,
                'message' => 'Log stored successfully'
            ]);
    }

    public function test_store_log_validation_fails_missing_identifier(): void
    {
        $response = $this->postJson('/api/v3/logs', [
            'payload' => ['user_id' => 123],
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['identifier']);
    }

    public function test_store_log_validation_fails_missing_payload(): void
    {
        $response = $this->postJson('/api/v3/logs', [
            'identifier' => 'Create User',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['payload']);
    }

    public function test_store_log_validation_fails_invalid_payload_type(): void
    {
        $response = $this->postJson('/api/v3/logs', [
            'identifier' => 'Create User',
            'payload' => 'invalid_payload_type',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['payload']);
    }

    public function test_store_log_without_metadata(): void
    {
        $identifier = 'Update User';
        $payload = ['user_id' => 456, 'name' => 'John Doe'];

        $expectedResponse = [
            'insertedId' => '507f1f77bcf86cd799439012'
        ];

        $this->logServiceMock
            ->shouldReceive('storeLog')
            ->once()
            ->with($identifier, $payload, [])
            ->andReturn($expectedResponse);

        $response = $this->postJson('/api/v3/logs', [
            'identifier' => $identifier,
            'payload' => $payload,
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'data' => $expectedResponse,
                'message' => 'Log stored successfully'
            ]);
    }

    public function test_get_logs_by_identifier(): void
    {
        $identifier = 'Create User';
        $expectedLogs = [
            'documents' => [
                [
                    '_id' => '507f1f77bcf86cd799439011',
                    'identifier' => $identifier,
                    'payload' => ['user_id' => 123],
                    'created_at' => '2023-01-01T00:00:00.000Z'
                ]
            ]
        ];

        $this->logServiceMock
            ->shouldReceive('getLogsByIdentifier')
            ->once()
            ->with($identifier)
            ->andReturn($expectedLogs);

        $response = $this->getJson("/api/v3/logs/{$identifier}");

        $response->assertStatus(200)
            ->assertJson([
                'data' => $expectedLogs,
                'message' => 'Logs retrieved successfully'
            ]);
    }

    public function test_get_all_logs(): void
    {
        $expectedLogs = [
            'documents' => [
                [
                    '_id' => '507f1f77bcf86cd799439011',
                    'identifier' => 'Create User',
                    'payload' => ['user_id' => 123],
                    'created_at' => '2023-01-01T00:00:00.000Z'
                ],
                [
                    '_id' => '507f1f77bcf86cd799439012',
                    'identifier' => 'Update User',
                    'payload' => ['user_id' => 456],
                    'created_at' => '2023-01-01T01:00:00.000Z'
                ]
            ]
        ];

        $this->logServiceMock
            ->shouldReceive('getLogs')
            ->once()
            ->with([], ['limit' => 50, 'sort' => ['created_at' => -1]])
            ->andReturn($expectedLogs);

        $response = $this->getJson('/api/v3/logs');

        $response->assertStatus(200)
            ->assertJson([
                'data' => $expectedLogs,
                'message' => 'Logs retrieved successfully'
            ]);
    }

    public function test_store_log_handles_service_exception(): void
    {
        $identifier = 'Create User';
        $payload = ['user_id' => 123];

        $this->logServiceMock
            ->shouldReceive('storeLog')
            ->once()
            ->with($identifier, $payload, [])
            ->andThrow(new \Exception('MongoDB connection failed'));

        $response = $this->postJson('/api/v3/logs', [
            'identifier' => $identifier,
            'payload' => $payload,
        ]);

        $response->assertStatus(500)
            ->assertJson([
                'data' => null,
                'message' => 'Failed to store log: MongoDB connection failed'
            ]);
    }
}
