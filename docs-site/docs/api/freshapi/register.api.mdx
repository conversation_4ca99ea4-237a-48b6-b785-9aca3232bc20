---
id: register
title: "Register a new user"
description: "Register a new user account. Can register with email or invitation key."
sidebar_label: "Register a new user"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Register a new user"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/auth/register"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Register a new user account. Can register with email or invitation key.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["password"],"properties":{"email":{"type":"string","format":"email","maxLength":255,"description":"User email address (required if invitation_key not provided)"},"invitation_key":{"type":"string","minLength":64,"maxLength":64,"description":"64-character invitation key (required if email not provided)"},"password":{"type":"string","format":"password","minLength":8,"description":"User password"},"password_confirmation":{"type":"string","format":"password","description":"Password confirmation (must match password)"},"role":{"type":"array","items":{"type":"string"},"description":"Optional array of role names"}},"oneOf":[{"required":["email","password","password_confirmation"]},{"required":["invitation_key","password","password_confirmation"]}],"title":"RegisterRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"201":{"description":"User registered successfully","content":{"application/json":{"schema":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"integer","format":"int64","description":"User ID"},"email":{"type":"string","format":"email","description":"User email address"},"email_verified_at":{"type":"string","format":"date-time","nullable":true,"description":"Email verification timestamp"},"first_name":{"type":"string","nullable":true,"description":"User first name"},"last_name":{"type":"string","nullable":true,"description":"User last name"},"birth_date":{"type":"string","format":"date","nullable":true,"description":"User birth date"},"created_at":{"type":"string","format":"date-time","description":"Account creation timestamp"},"full_name":{"type":"string","description":"User full name"},"initials":{"type":"string","description":"User initials"},"image":{"type":"string","nullable":true,"description":"User profile image URL"},"image_thumbnail":{"type":"string","nullable":true,"description":"User profile image thumbnail URL"},"onboarded":{"type":"boolean","description":"Whether user has completed onboarding"}},"title":"UserData"},"message":{"type":"string","description":"Success message"}},"title":"UserRegistrationResponse"}}}},"422":{"description":"Validation error","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Validation error message"},"errors":{"type":"object","additionalProperties":{"type":"array","items":{"type":"string"}},"description":"Field-specific validation errors"}},"title":"ValidationErrorResponse"}}}}}}
>
  
</StatusCodes>


      