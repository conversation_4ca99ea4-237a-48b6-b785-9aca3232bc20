---
id: get-admin-customercarclub-byid
title: "getAdminCustomercarclubByid"
description: "getAdminCustomercarclubByid"
sidebar_label: "getAdminCustomercarclubByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVU1v5DYM/SsCTy2gzaQpevFtm3aLFEWxaNL2EMyBtjm2EFnyitSk04H/e0HZ85Vkt3uZsajHx0eKovYg2DFUj3CbWeJA6RbTrc/1+3ZwAdYWRkw4kFBS1B4CDgQVNAv6rgULLkAFI0oPFhJ9yi5RC5WkTBa46WlAqPYgu1E9XRDqKIGFTUwDymz6/gamaa3uPMbAxOpxc32tfy1xk9woLmqc+9w0xAzTZIGpycnJriirCRMlqB7XShRHSqgudy1U0JGUhA5JNpgan+sfd071Xwb4MnYg6eNCCXbOuoIVjm6F6rVqLt1W+1OpJlDJaXuoZU4eKuhFRq5WKx8b9H1kqfZjTDK9EvabAsxMABa2mBzWfi6Vesy12mD2WtQfrq+/g2ma7Ms4m0Tcv2tp+w5Hd4X/5kTPVLMT4qtQkroM+xNtycdxoCCH4J8jzShfSXov2LnQ/R+hd1v6SsaPKba50cWRdH3qkHttw7lUhz45dqSGhKVTdb0A7PLx4dCmv/79ULrOhU0s7k684j+oVHOLybz/eKcHQ4lnTdubVzIfeseKM2OKW9cSGwrtGF0QNpuYjPRk2ii/k5jCq9C/bq7MnXzOY8Aw17LBxFZ/TYNCXUyODmufa8O5Pgo5t9cxPrnQsTUYWpOZksEsPQVxTblBJTh6H5+5bLORaEZKen/N8Z6x4dz0BmeMSdQ5lnmrEPvYuWCNdywnsTOKkloCPS/Wljydgc5cLjLzs3CTx7LVkqDzbOrdZfCQh5qSNUyYmv5Aq7ABn6hoG2JL3pomEZ4ivVWzV4BT8fLYznuLybCgZJ6PIDTk/ZeYi4rDQb5xBsaFxudWd0dkfo6pNYmYhIsrDei82VJym8OhwWRhjCwDBm3WZW53JKZMN/NivJm3ZuHxigj9I6vRowtKW27qfpl9j4CjAwtl+oGFF/MPLFRnj8Xago44ddvva2T6M/lpUvOnTEkH+fo02cqQbB3rdwvVBj3TK4VNDEJB7+c3fyxvz7cG7NvKFyOGXRmgPusKLDzR7vJRm9bT2az/5ecHzTBrvseavBgTReyb/Pv9jHiITxSm6RhOdK2Rpuk/KWyqWg==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminCustomercarclubByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/customercarclub/{customerId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminCustomercarclubByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      