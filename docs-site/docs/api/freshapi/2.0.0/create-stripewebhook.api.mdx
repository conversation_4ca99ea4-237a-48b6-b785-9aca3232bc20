---
id: create-stripewebhook
title: "createStripewebhook"
description: "createStripewebhook"
sidebar_label: "createStripewebhook"
hide_title: true
hide_table_of_contents: true
api: eJyNVMFu2zgQ/RVizkqcBtiLbsUuCqQodgM4bQ+BDyNpbBGmSO7M0KlX0L8vSNmOE6dpL5bJefPmcYZ8IyhuBOpHWCrbSN+p6UPYwqoCJonBCwnUI9ze3ORPR9KyjWqDhxqWqW1JBKapAqE2sdU91I8jNIRMDPXjalpVECIx5pS7DmpomVBprvZ0qFa9In4bM5D2IVPEIAoVRNQealhgtIuX6rMc3hFLUZPYQQ29apR6sXChRdcH0XqMgXW6KP4lA8xMABXskC02bm5Dzpj7sMbkFGr44+bmA0zTVL2us2aS/qqj3RVGe43/Jc5HEask1570ouxftCMX4kBej8V/RppQf5N0qbixfvMrQmd39JuM9xy61ObFiXT1PP1l29Mwt+p4B0bQfaRDyTyZAoH6CKgOfz4FHjB39PP3h3KjrF+Hkm7VZfynLNX8iWw+3t/lwRDLrGl3eyHzobeScSZy2NmOxJDvYrBexawDG+3JdEH/JjWFN0O/3V6bO/1ZxoB+7mWLLFX+NS0qbQJbOq5daoyk5iTkfL8JYWv9RiqDvjNJiA0m7cmrbcvrKMXRufAkJSxGg4nE68CDOb0hMZLa3uCMMUwbKzqHCrELG+sr46zos9gZRZx3PD0ddjtydAY6S3lxMjcLNymWUEeK1olp9i+L+zQ0xJURQm77I22GDbilom0IHbnKlNd9qvRWzy4Az81LsZtjhy0jippkHoFvybn3mIuK4yDfmIGxvnWpy9GIIk+BO8MkpFJSaUDrzI7Yro9Dg6kqfjSgz5fVY7nbs4GZd13u9DCUfugiOrQ+k5X3OR7M7REwWqguzTkbWA6PY4NCX9lNU97+NxFnC149+1Zx4TPzvP9n+QAV5HOfaXj1GItvHkLo98UGXcqrcZwRD2FLfsruuaV9PkRew7Sapul/yXlBHg==
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createStripewebhook"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/StripeWebhook"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createStripewebhook

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      