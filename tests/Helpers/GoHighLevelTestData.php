<?php

namespace Tests\Helpers;

class GoHighLevelTestData
{
    /**
     * Get sample webhook data based on examples/customwebhook.json
     */
    public static function getSampleWebhookData(array $overrides = []): array
    {
        $defaultData = [
            'contact_id' => 'mYTqcN6I8esiYUgLSqSI',
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'full_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'tags' => 'urgent repair',
            'country' => 'GB',
            'date_created' => '2025-06-02T08:48:24.571Z',
            'full_address' => null,
            'contact_type' => 'lead',
            'opportunity_name' => 'Car Service Booking',
            'status' => 'won',
            'lead_value' => 150,
            'opportunity_source' => 'website',
            'source' => 'website',
            'pipleline_stage' => 'booked',
            'pipeline_id' => 'iy8yb8BBsbY1WiqzlcQ3',
            'id' => 'VOn9DcpHfTGo5iGwYHdH',
            'pipeline_name' => 'Smart Repair',
            'user' => [
                'firstName' => 'Sales',
                'lastName' => 'Rep',
                'email' => '<EMAIL>'
            ],
            'owner' => 'Sales Rep',
            'location' => [
                'name' => 'Test Location',
                'address' => '123 Main Street',
                'city' => 'London',
                'state' => null,
                'country' => 'GB',
                'postalCode' => 'SW1A 1AA',
                'fullAddress' => '123 Main Street, London SW1A 1AA',
                'id' => 'SiOmQ4U4fRp5dvSKYoLe'
            ],
            'workflow' => [
                'id' => '08580efd-c57e-4fa3-b382-ceae806f79ae',
                'name' => 'testWorkflowWebhook'
            ],
            'triggerData' => [],
            'contact' => [
                'attributionSource' => [
                    'sessionSource' => 'CRM UI',
                    'medium' => 'manual',
                    'mediumId' => null
                ],
                'lastAttributionSource' => []
            ],
            'attributionSource' => [],
            'customData' => []
        ];

        return array_merge($defaultData, $overrides);
    }

    /**
     * Get sample webhook data for a new customer
     */
    public static function getNewCustomerWebhookData(): array
    {
        return self::getSampleWebhookData([
            'email' => '<EMAIL>',
            'first_name' => 'New',
            'last_name' => 'Customer',
            'opportunity_name' => 'First Time Service'
        ]);
    }

    /**
     * Get sample webhook data for an existing customer
     */
    public static function getExistingCustomerWebhookData(): array
    {
        return self::getSampleWebhookData([
            'email' => '<EMAIL>',
            'first_name' => 'Existing',
            'last_name' => 'Customer',
            'opportunity_name' => 'Repeat Service'
        ]);
    }

    /**
     * Get sample webhook data with non-won status
     */
    public static function getNonWonWebhookData(): array
    {
        return self::getSampleWebhookData([
            'status' => 'open',
            'opportunity_name' => 'Pending Opportunity'
        ]);
    }

    /**
     * Get sample webhook data with missing email
     */
    public static function getMissingEmailWebhookData(): array
    {
        $data = self::getSampleWebhookData();
        unset($data['email']);
        return $data;
    }

    /**
     * Get sample webhook data in array format (as shown in examples/customwebhook.json)
     */
    public static function getArrayFormatWebhookData(): array
    {
        return [
            self::getSampleWebhookData([
                'first_name' => 'Luk',
                'last_name' => '3',
                'email' => '<EMAIL>',
                'opportunity_name' => 'Luk3',
                'lead_value' => 2
            ]),
            [
                'Spatie\\WebhookClient\\WebhookConfig' => [
                    'name' => 'gohighlevel-webhook-default',
                    'signingSecret' => 'gohighlevel_key.pub',
                    'signatureHeaderName' => 'x-wh-signature',
                    'signatureValidator' => [],
                    'webhookProfile' => [],
                    'webhookResponse' => [],
                    'webhookModel' => 'Spatie\\WebhookClient\\Models\\WebhookCall',
                    'storeHeaders' => [],
                    'processWebhookJobClass' => 'App\\Services\\GoHighLevel\\WebhookHandlers\\DefaultHandler'
                ]
            ]
        ];
    }

    /**
     * Get expected FreshCar API data structure based on sample-booking.json
     */
    public static function getExpectedFreshCarApiData(array $overrides = []): array
    {
        $defaultData = [
            'bookingId' => 0,
            'requestedDate' => '2024-07-29T15:51:28.071Z',
            'confirmedDate' => '2024-07-29T15:51:28.071Z',
            'addressId' => 0,
            'additionalComments' => 'string',
            'notes' => 'string',
            'preferredTime' => '2024-07-29T15:51:28.071Z',
            'bookingStatusId' => 0,
            'totalCost' => 0,
            'enquiryStatus' => 'string',
            'resourceId' => 0,
            'resourceName' => 'string',
            'timeOfDay' => 'string',
            'bookingReferenceNumber' => 'string',
            'bookingHubDurationMinutes' => 'string',
            'overridePrice' => 0,
            'bookingCustomerCars' => [
                [
                    'bookingCustomerCarId' => 0,
                    'bookingId' => 0,
                    'customerCarId' => 0,
                    'packageGroupId' => 0,
                    'registrationNumber' => 'string',
                    'makeAndModel' => 'string',
                    'category' => 'string',
                    'colour' => 'string',
                    'year' => 'string',
                    'optionalExtras' => [
                        [
                            'packageItemId' => 0
                        ]
                    ]
                ]
            ],
            'isPrepaid' => true,
            'isRefunded' => true,
            'customerCarClubPackageId' => 0,
            'currency' => 0
        ];

        return array_merge($defaultData, $overrides);
    }

    /**
     * Get minimal webhook data for testing edge cases
     */
    public static function getMinimalWebhookData(): array
    {
        return [
            'id' => 'minimal-test',
            'email' => '<EMAIL>',
            'status' => 'won'
        ];
    }

    /**
     * Get webhook data with all optional fields missing
     */
    public static function getWebhookDataWithMissingFields(): array
    {
        return [
            'id' => 'missing-fields-test',
            'email' => '<EMAIL>',
            'status' => 'won'
            // All other fields intentionally missing
        ];
    }

    /**
     * Get webhook data with null values
     */
    public static function getWebhookDataWithNulls(): array
    {
        return [
            'id' => 'null-test',
            'email' => '<EMAIL>',
            'status' => 'won',
            'first_name' => null,
            'last_name' => null,
            'tags' => null,
            'opportunity_name' => null,
            'lead_value' => null,
            'owner' => null,
            'location' => null
        ];
    }
}
