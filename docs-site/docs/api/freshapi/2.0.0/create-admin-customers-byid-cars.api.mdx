---
id: create-admin-customers-byid-cars
title: "createAdminCustomersByidCars"
description: "createAdminCustomersByidCars"
sidebar_label: "createAdminCustomersByidCars"
hide_title: true
hide_table_of_contents: true
api: eJztV0tz2zYQ/iuYPfUBR4o7ufDmOM2MO2niidX24NFhRaxExCDA4CFH5fC/dxakSElWGv8AX2xhuS/sfvgWaCHiJkBxD9cpRFeTv0YfrlStLSwlNOixpkieVVqwWBMUUA6qNwokaAsFNBgrkODpa9KeFBTRJ5IQyopqhKKFuGvYUttIG/IgYe18jbEX/XYJXbfszSnEt07t2KZ0NpKN/BObxugSo3Z29iU4y7LJ+RT2Hmp8oCur/nSKTM5oo0P02fJjqlfkeVtDNm71hcoIEhrvGvJRU8hxp0rcqOckL8EmY3BlqN94dzZu0UKN3z6Q3cQKijdzCbW2++XrMakQvbYb6OTxVo6tXz/DvMRIG+d3p6bzU90z6ZfOuHQu5R9a7ghP7Z4RsZOASmmuFprbg26s0QSSEHVk3UOQvlt8AraL9C2+YOIFE4eYOKSLX359AccLOEZwdLnWoXE29M27nM/5n6JQet2wQyjgLpUlhZDBFKhMXsddnoArQs+tuV/ywOKwuWPcdig9YaQ8O/dhw9udVjxRQZ5E+IFyTbFy7LRxIQOOB2wBM2z0DNlotgddmLXTPO5mZW8fyG/3Uzt5AwVUMTahmM2MK9FULsSibZyP3ZPMPrCC6B2AhC16zYXPxWKLvlprTIYB/WY+f81lladx1p5CdaFoe4GNfoX/Jk+PtAo6UnhlKT4J+462ZFxTk4374N9zmjA+0+ldxI22mx85NHpLz/R4651KJS9Gp8sJI3dMMX2p9kgZ2YBDwnAn4vWgIIcf7/cU8cc/i4w7pq3P043o929YN4zpJ3wzP88e09k55oVJPh34A9lwkCdJf0APeELbtcv7Gs7Ye66huEYvrm5vGDHkQ1+s7eWT+i0qHVhPNN5ttaIgyKrGaRuDWDsvYkVCufiRosh+WfXvy1fiJn7PokbbN5mhL/mvGDamab82aSVCWo2JHMpXzj1ouwlSoFUiBfICU6zIxmGE5OBojHsM+XMQ0YmGPJO6GCkgiJDKSmCvIw47kh0bt9FWCqNDnJLttYgrKyw9DlJFhg6UDkyOdmb6xEVq8idFEbUJYrU7Dm4zHqQIhL6s9m5ZjXGRc6sZGVJkThojnavZE4WpeKlR/bdBJELEmELfAluSMf/nOWexb+SZHghtS5MUf20whEfnlfAUKIZsSjVqI7bk9XrfNEYqk2eNefjvny6ZdkXmXTESr2DmFed4ejy9+ZrZGNSWHWcSaQdWvgdsNPBA4leTHI8nOyuO3kqZnJcSmH7Zrm1XGOgvb7qOxV8T8WG8X06smwlc6cC/1TjdTlIcH0rw0+fhZvOzAHk+9UGIdpfJ3SRegYQH2h0/7bplJ6EiVHnetYPCdR/rYsFuJgdPHmjMiuMQu/10t2CiGx52DDcowOMjX7jwsU/A9VjI9zOWtWDQbhJuWLf3mcd+4pqPfTlh0Zzp2S22ba+xcA9ku27cceQ1b7br/gOb1EK/
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAdminCustomersByidCars"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/admin/customers/{customerId}/cars"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAdminCustomersByidCars

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}},"text/json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}},"application/*+json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      