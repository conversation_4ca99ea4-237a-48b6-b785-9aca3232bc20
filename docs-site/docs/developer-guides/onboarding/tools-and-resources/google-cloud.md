# Google Cloud Setup

This guide will help you set up Google Cloud access for the Fresh Car Core project.

## For New Developers

### 1. Get Project Access

- Contact your administrator for access to `fresh-car-test` project
- Ensure you have Developer role or equivalent

### 2. Install Google Cloud CLI

**macOS:**

```bash
brew install --cask google-cloud-sdk
```

**Linux/macOS (alternative):**

```bash
curl https://sdk.cloud.google.com | bash
```

### 3. Authenticate and Configure

```bash
gcloud auth login
gcloud config set project fresh-car-test
gcloud config set run/region europe-west1
```

### 4. Verify Access

```bash
gcloud run services list
gcloud secrets list
```

## Common Tasks

### Running Artisan Commands

**Local Development:**

```bash
php artisan migrate
php artisan queue:work
```

**Staging Environment:**

```bash
./artisan-staging migrate
./artisan-staging queue:work
```

**Production Environment:**

```bash
./artisan-cloud migrate
./artisan-production migrate
```

### Monitoring

**Check Application Status:**

```bash
# Staging
curl https://staging-url/health

# Production
curl https://production-url/health
```

**Monitor Cloud Run Jobs:**

```bash
./scripts/job-monitor.sh status laravel-migrate-job-production
./scripts/job-monitor.sh logs laravel-queue-worker-job-staging
```

## Security

### Secrets Management

- All sensitive data stored in Google Secret Manager
- Environment-specific secret prefixes (`staging-*`, `production-*`)
- Automatic secret injection in Cloud Run

### Authentication

- API authentication via Laravel Sanctum
- User-specific data access controls
- Proper input validation and sanitization

## Useful Resources

- **Google Cloud Console**: [console.cloud.google.com](https://console.cloud.google.com)
- **Cloud Run Documentation**: [cloud.google.com/run/docs](https://cloud.google.com/run/docs)
