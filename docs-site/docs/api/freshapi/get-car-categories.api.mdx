---
id: get-car-categories
title: "List all car categories"
description: "This endpoint retrieves all available car categories in the system."
sidebar_label: "List all car categories"
hide_title: true
hide_table_of_contents: true
api: eJytV02T0zgQ/Std4jBQ5ThhBthd32aHjwo1wBQZ4ECmQJY7thhZEvrIkE3lv2+17GSchF32sLnEsVqvW63XrztrFnjtWfGZXXAHFzxgbZxEz24yVqEXTtogjWYFu26kB9SVNVIHcBicxCV64EoBX3KpeKkQBHcgdiggNYQGwa98wDZnGTMWHSfEacUKVmO44G7gNWMOvTXao2fFmp1OJvS1H8jFvottJBX4KAR6v4hKrVjGhNEBdSAAbq2SIvkdf/OEsmZeNNhyegori6xg3DlO+2TA1g/em/IbisAyZh1FH2QXm6wGNlIHrNGxjC2Ma3noXp2dssMsftDye0SQFeogFxIdLIxLORpkbsU2GdO8xYEHH5zU9RHeW94imMURAjzEvM4zOJm1XKmTDE7eYCVjS0+X3NVID7MPH08eka89zF+5fI6BS4UVDF7/LAQCtk4KfBNVkFZJdANwHdtyP2ELZXg48nZFCNDuIGDBRTDbtEk/cLch9nyP0mFFhJYV67N4k7Egg8KOPD3dVs+v37ENfTL2ZPL4mGcfNI+hMU7+hRWM4DyGhi6toxHsPNF+jyI6GVas+LxmJXJHR/18s7nJWIuhMT3XiUM8NKxg4+XZmFs5FtyNxJD+Ht0SnU9A0SlWsCYE64vxWBnBVWN8KNbWuLA5ytQlGUAHwDK25E5SSSay0o7uhAseFWX78eTxhI6fHfqpcInK2BZ1yBcOfSO4y4XJ4+1PmLAz3fo9xuNWjmhRChw9nvzx+9Nnz06fPPntbJJjpIIa3aEPp7mLOufWjo+czAKvpa7/1cGvAr1ypooi3dsW5ub+1makBF2etne3oyk5oWtJJqzYGmT9w8steV9/uj7y+vrTdWINUagjTYO8QgfR04GoXv5MKNDB5/DiB2+twgJO9vYVW7t1MLeoNyeJdFIvTIq0p/ZLygGQOp5fTYkA6HwXyFk+yScsYz9GytRpT5fBsWzr8TZ1aS23qeBLLm5rZ6KuLowyjhXswcv0YRnjKlzjj3Dk8FBG2JfUMc6vptBwDyWihp5bWEEw4NAqLjDloTLhLQZIiLTj42kGXFcgPWgTYIWBWkn0WEG56pRGSaIdt9bnX+Z6rh88gFlsW+5Wc73zbJ1ZygrvG5dPwtFy3ZFKcOezg6bV/1axnGsfy92RBgtQGnMrde27KKNHB3xPH3KYBmqN5s6nZT/XwYBFR3IHuy7oqWk1wDsjcFhLH7qlhKxMLXUGSvqwC3euOzMkdQaNd/0pKlR4bzXcs3c41YUO0dLSXFdJzj3ldc99J9AZeORONFtcMmv5LaboWlOhykA45ORqrnf5Ocxbb/GzBEZbdWv9q/4uL5zxfvTOyVpqeI/eRCcQZg1Pp3548e797NHgon20pHH+P+1LHBDJsDItl52aow8+773vi/1cnys1oFCv/QdX3pf1JXd8iQpmXIsQ2xymWqhYdTzvVANSGW+Ho59JRDHXX79+net/kIGViW7UaUFv2M87XCSZ72YHdt0gXMq6CZoCe+VMJC3bV8+7u7s8NKi2ZlT2tpfRTcaUFKg9DkDfTK+PQIxF3WU6N64e95v8mGw3GQvoWv9uMeu6wGBfLUMTy1yYdrwgtUHHdbULZSBNpR0J43BcKlOO6cbGl9OLF29nL5KqOR5wpGQrw3EXJ3LQOqR1ykMaYxfGiU6GUPvoEPoWBT7wUioZVonhCy5JrnmNeZL3vnuuGUGy4tkkY5b6BWuljgFJOlesYNMr4FXl0Hswrivt6XO4a1APOUPDQ8ZSid9DPv0lIsWFLZcKhGlLqRM3Uitn1vjQcj24rUvpkw4dyMBhr1rfT8v/06Df98+AP8LYKi41nTWxZt0PQZ/Z8oz6iZVE3v1B6CZjNOqQ0Xpdco8fnNps6PX3iI7mrJv7CSeNWhnrKidNTrdIOTsXAm1Io5CKacI//BuwN6C9ekHEpvsZ9P+Dfp/Qt/8X9GqAvV53FtepKmk664JIVco2N5vN5m9G4LQe
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"List all car categories"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/car-categories"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint retrieves all available car categories in the system.

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Car categories retrieved successfully","content":{"application/json":{"schema":{"type":"array","items":{"type":"object","properties":{"id":{"type":"integer","format":"int32","description":"Unique identifier for the car category"},"name":{"type":"string","description":"Name of the car category (e.g., 'Small', 'Medium', 'Large', 'SUV')"},"description":{"type":"string","description":"Detailed description of the car category"},"priceMultiplier":{"type":"number","format":"float","description":"Price multiplier factor for this category"}},"required":["id","name"],"title":"CarCategoryDTO"}}}}},"401":{"description":"Unauthorized - Authentication required"}}}
>
  
</StatusCodes>


      