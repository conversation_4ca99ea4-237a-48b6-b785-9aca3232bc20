<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_invitations', function (Blueprint $table) {
            // 'default' used only for avoiding not-null column errors if records already exist in the DB
            // removed in the next migration
            if (config('database.default') === 'sqlite') {
                // SQLite doesn't support the PostgreSQL random() function
                $table->string('signature', 64)->default('temp_signature');
            } else {
                $table->string('signature', 64)->default(DB::raw('md5(random()::text)'));
            }
        });
    }

    public function down(): void
    {
        Schema::table('user_invitations', function (Blueprint $table) {
            $table->dropColumn('signature');
        });
    }
};
