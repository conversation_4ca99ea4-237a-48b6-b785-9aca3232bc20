<?php

use App\Services\WebhookActivityService;
use Spatie\Activitylog\Models\Activity;
use Spatie\WebhookClient\Models\WebhookCall;

beforeEach(function () {
    $this->superAdmin = createSuperAdmin();
    $this->freshcarAdmin = createUser();
    $this->freshcarAdmin->assignRole('freshcar_admin');
    $this->regularUser = createUser();
});

test('super admin can access webhook monitoring dashboard', function () {
    $this->actingAs($this->superAdmin)
        ->get(route('admin.webhook-monitoring.index'))
        ->assertOk()
        ->assertSee('Webhook Processing Dashboard');
});

test('freshcar admin can access webhook monitoring dashboard', function () {
    $this->actingAs($this->freshcarAdmin)
        ->get(route('admin.webhook-monitoring.index'))
        ->assertOk()
        ->assertSee('Webhook Processing Dashboard');
});

test('regular user cannot access webhook monitoring dashboard', function () {
    $this->actingAs($this->regularUser)
        ->get(route('admin.webhook-monitoring.index'))
        ->assertForbidden();
});

test('webhook monitoring dashboard shows metrics correctly', function () {
    // Create some test webhook activities
    $webhookCall = WebhookCall::create([
        'name' => 'test-webhook',
        'url' => 'https://example.com/webhook',
        'headers' => [],
        'payload' => json_encode(['test' => 'data']),
    ]);

    // Create activity logs
    Activity::create([
        'description' => WebhookActivityService::LOG_WEBHOOK_RECEIVED,
        'subject_type' => WebhookCall::class,
        'subject_id' => $webhookCall->id,
        'properties' => [
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
        ],
        'created_at' => now(),
    ]);

    Activity::create([
        'description' => WebhookActivityService::LOG_WEBHOOK_PROCESSING_COMPLETED,
        'subject_type' => WebhookCall::class,
        'subject_id' => $webhookCall->id,
        'properties' => [
            'webhook_id' => $webhookCall->id,
            'webhook_name' => $webhookCall->name,
            'processing_time_seconds' => 1.5,
        ],
        'created_at' => now(),
    ]);

    $this->actingAs($this->superAdmin)
        ->get(route('admin.webhook-monitoring.index'))
        ->assertOk()
        ->assertSee('Total Webhooks')
        ->assertSee('Success Rate')
        ->assertSee('Avg Processing Time');
});

test('webhook monitoring metrics API returns correct data', function () {
    $this->actingAs($this->superAdmin)
        ->get(route('admin.webhook-monitoring.metrics'))
        ->assertOk()
        ->assertJsonStructure([
            'metrics' => [
                'total_webhooks',
                'successful_webhooks',
                'failed_webhooks',
                'success_rate',
                'average_processing_time',
                'new_customers',
                'existing_customers',
            ],
            'signatureMetrics' => [
                'success',
                'failure',
                'na',
                'total',
            ],
            'period',
        ]);
});

test('webhook activity service logs activities correctly', function () {
    $webhookCall = WebhookCall::create([
        'name' => 'test-webhook',
        'url' => 'https://example.com/webhook',
        'headers' => [],
        'payload' => json_encode(['test' => 'data']),
    ]);

    $service = app(WebhookActivityService::class);

    // Test webhook received logging
    $service->logWebhookReceived($webhookCall);

    $this->assertDatabaseHas('activity_log', [
        'description' => WebhookActivityService::LOG_WEBHOOK_RECEIVED,
        'subject_type' => WebhookCall::class,
        'subject_id' => $webhookCall->id,
    ]);

    // Test signature validation logging
    $service->logSignatureValidation($webhookCall, WebhookActivityService::SIGNATURE_SUCCESS);

    $this->assertDatabaseHas('activity_log', [
        'description' => WebhookActivityService::LOG_WEBHOOK_SIGNATURE_VALIDATED,
        'subject_type' => WebhookCall::class,
        'subject_id' => $webhookCall->id,
    ]);

    // Test email check logging
    $service->logEmailCheck($webhookCall, '<EMAIL>', true);

    $this->assertDatabaseHas('activity_log', [
        'description' => WebhookActivityService::LOG_WEBHOOK_EMAIL_CHECK,
        'subject_type' => WebhookCall::class,
        'subject_id' => $webhookCall->id,
    ]);
});

test('webhook monitoring navigation is visible to authorized users', function () {
    $this->actingAs($this->superAdmin)
        ->get(route('admin.dashboard'))
        ->assertSee('Webhook Monitoring');

    $this->actingAs($this->freshcarAdmin)
        ->get(route('admin.dashboard'))
        ->assertSee('Webhook Monitoring');
});

test('webhook monitoring navigation is hidden from unauthorized users', function () {
    $this->actingAs($this->regularUser)
        ->get(route('admin.dashboard'))
        ->assertDontSee('Webhook Monitoring');
});

test('webhook activity service handles array payload correctly', function () {
    $webhookCall = WebhookCall::create([
        'name' => 'test-webhook',
        'url' => 'https://example.com/webhook',
        'headers' => [],
        'payload' => ['test' => 'data', 'nested' => ['key' => 'value']], // Array payload
    ]);

    $service = app(WebhookActivityService::class);

    // This should not throw an error
    $service->logWebhookReceived($webhookCall);

    $this->assertDatabaseHas('activity_log', [
        'description' => WebhookActivityService::LOG_WEBHOOK_RECEIVED,
        'subject_type' => WebhookCall::class,
        'subject_id' => $webhookCall->id,
    ]);

    // Check that payload_size was calculated correctly for array
    $activity = Activity::where('description', WebhookActivityService::LOG_WEBHOOK_RECEIVED)
        ->where('subject_id', $webhookCall->id)
        ->first();

    expect($activity->properties['payload_size'])->toBeGreaterThan(0);
});

test('webhook activity service handles string payload correctly', function () {
    $webhookCall = WebhookCall::create([
        'name' => 'test-webhook',
        'url' => 'https://example.com/webhook',
        'headers' => [],
        'payload' => json_encode(['test' => 'data']), // String payload
    ]);

    $service = app(WebhookActivityService::class);

    // This should not throw an error
    $service->logWebhookReceived($webhookCall);

    $this->assertDatabaseHas('activity_log', [
        'description' => WebhookActivityService::LOG_WEBHOOK_RECEIVED,
        'subject_type' => WebhookCall::class,
        'subject_id' => $webhookCall->id,
    ]);

    // Check that payload_size was calculated correctly for string
    $activity = Activity::where('description', WebhookActivityService::LOG_WEBHOOK_RECEIVED)
        ->where('subject_id', $webhookCall->id)
        ->first();

    expect($activity->properties['payload_size'])->toBe(strlen(json_encode(['test' => 'data'])));
});

test('webhook activity service captures location information correctly', function () {
    // Create a test location
    $location = \App\Models\GohighlevelLocation::create([
        'name' => 'Test Location',
        'location_id' => 'test-location-123',
        'client_id' => 'test_client',
        'client_secret' => 'test_secret',
        'base_url_authorization' => 'https://test.com/auth',
        'base_url_api' => 'https://test.com/api',
    ]);

    $webhookCall = WebhookCall::create([
        'name' => 'gohighlevel-opportunity-status-update',
        'url' => 'https://example.com/webhook',
        'headers' => [],
        'payload' => json_encode([
            'locationId' => 'test-location-123',
            'webhookId' => 'webhook-abc-123',
            'email' => '<EMAIL>',
            'status' => 'won'
        ]),
    ]);

    $service = app(WebhookActivityService::class);
    $service->logWebhookReceived($webhookCall);

    $activity = Activity::where('description', WebhookActivityService::LOG_WEBHOOK_RECEIVED)
        ->where('subject_id', $webhookCall->id)
        ->first();

    $properties = $activity->properties->toArray();

    expect($properties['ghl_location_id'])->toBe('test-location-123');
    expect($properties['ghl_location_name'])->toBe('Test Location');
    expect($properties['ghl_webhook_id'])->toBe('webhook-abc-123');
});

test('webhook activity resource displays location information correctly', function () {
    $location = \App\Models\GohighlevelLocation::create([
        'name' => 'Auto Repair Shop',
        'location_id' => 'location-456',
        'client_id' => 'test_client',
        'client_secret' => 'test_secret',
        'base_url_authorization' => 'https://test.com/auth',
        'base_url_api' => 'https://test.com/api',
    ]);

    $webhookCall = WebhookCall::create([
        'name' => 'gohighlevel-opportunity-status-update',
        'url' => 'https://example.com/webhook',
        'headers' => [],
        'payload' => json_encode([
            'locationId' => 'location-456',
            'webhookId' => 'webhook-def-456',
            'email' => '<EMAIL>'
        ]),
    ]);

    $service = app(WebhookActivityService::class);
    $service->logWebhookReceived($webhookCall);

    $activity = Activity::where('description', WebhookActivityService::LOG_WEBHOOK_RECEIVED)
        ->where('subject_id', $webhookCall->id)
        ->first();

    $resource = new \App\Http\Resources\WebhookActivityResource($activity);
    $resourceArray = $resource->toArray(request());

    expect($resourceArray['location_display'])->toBe('Auto Repair Shop');
    expect($resourceArray['webhook_group_id'])->toBe('webhook-def-456');
    expect($resourceArray['ghl_location_name'])->toBe('Auto Repair Shop');
});

test('webhook detail view shows complete processing flow', function () {
    $location = \App\Models\GohighlevelLocation::create([
        'name' => 'Test Auto Shop',
        'location_id' => 'test-location-789',
        'client_id' => 'test_client',
        'client_secret' => 'test_secret',
        'base_url_authorization' => 'https://test.com/auth',
        'base_url_api' => 'https://test.com/api',
    ]);

    $webhookCall = WebhookCall::create([
        'name' => 'gohighlevel-opportunity-status-update',
        'url' => 'https://example.com/webhook',
        'headers' => [],
        'payload' => json_encode([
            'locationId' => 'test-location-789',
            'webhookId' => 'test-webhook-flow-123',
            'email' => '<EMAIL>',
            'status' => 'won'
        ]),
    ]);

    $service = app(WebhookActivityService::class);

    // Create a complete processing flow
    $service->logWebhookReceived($webhookCall);
    $service->logSignatureValidation($webhookCall, WebhookActivityService::SIGNATURE_SUCCESS);
    $service->logProcessingStarted($webhookCall);
    $service->logEmailCheck($webhookCall, '<EMAIL>', true);
    $service->logBookingCreated($webhookCall, '<EMAIL>', 'new_customer', ['booking_id' => 123]);
    $service->logProcessingCompleted($webhookCall, 2.5);

    $response = $this->actingAs($this->superAdmin)
        ->get(route('admin.webhook-monitoring.show', 'test-webhook-flow-123'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.webhook-monitoring.show');
    $response->assertViewHas('webhookGroupId', 'test-webhook-flow-123');
    $response->assertViewHas('activities');
    $response->assertViewHas('webhookInfo');

    // Check that all activities are present in chronological order
    $activities = $response->viewData('activities');
    expect($activities)->toHaveCount(6);

    // Check webhook info
    $webhookInfo = $response->viewData('webhookInfo');
    expect($webhookInfo['location_display'])->toBe('Test Auto Shop');
    expect($webhookInfo['customer_email'])->toBe('<EMAIL>');

    // Check that the view contains expected content
    $response->assertSee('Webhook Processing Flow');
    $response->assertSee('test-webhook-flow-123');
    $response->assertSee('Test Auto Shop');
    $response->assertSee('<EMAIL>');
    $response->assertSee('Processing Timeline');
});

test('webhook detail view returns 404 for non-existent webhook', function () {
    $response = $this->actingAs($this->superAdmin)
        ->get(route('admin.webhook-monitoring.show', 'non-existent-webhook-id'));

    $response->assertStatus(404);
});
