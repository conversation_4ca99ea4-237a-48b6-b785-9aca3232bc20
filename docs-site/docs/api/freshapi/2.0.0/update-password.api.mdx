---
id: update-password
title: "updatePassword"
description: "updatePassword"
sidebar_label: "updatePassword"
hide_title: true
hide_table_of_contents: true
api: eJztVktv3DYQ/ivEHFva6xjoRaemSQO4CBKjXrcHYw+z0qzEmCIVcribraD/Xgy12ped1sgtQC674nDmm+E8PrIH31FANt7dVFBA6ipkusUYNz5UoIGxjlA8wOuy9MkxLDQE+pwo8m++2kLRQ+kdk2P5xK6zpsxos0/RO5HFsqEW5UvsTKBK4ErvVia0H2hz5ItaNBY0uBOpt9V+tdDA246gAL/8RCWDhi7ICdhQFB8jRNFDa9x7cjU3ULzaG0UOxtWgYeVDiwzFTn849fJy824yGU6j/jaEZ5LyLUCDBqwqI1VAe3uUnhXaSBrYsBWM+5Nav51/zKZMX/hH8b7T4h0P4E8//6ji91hFMQ4UO+/imNDrqyv5qyiWwXSCCQXcpbKkGLOrSGUKhrdQPPSwJAwUoHhYDAt9ZvSE3Vvixgvvdz7mSqKcEWbYmdmO8Wf350aRwppCzN5SsFBAw9zFYjazvkTb+MhF3/nAA5wH8F4U1AgAGtYYDC7teEyxGM+5wmQln79cXb2ShOhzP6tAsbmoaH2BnbnEf1KgDS2jYYqXjviJ27e0Juu7lhxPzr8GmpBfCHrHWBtX/x+gNWt6IeJt8FUqZbEHXRyqeydTPKZqqnE/9aK4lMpkFSgmBb37eDd16B9/z3PHCDP8ebjGf/+CbScNuZ9eSJHCrzTKL0vfnrHAUfufDN1B/NwkTbuDBuNWPh9gNwnvJFnqDQb1+vZGWoNCHLOyvn6SqHljouipLvi1qSgqclXnjeOoVj4obkhVnj8Qq4wrqn9dX6ob/ppFi26sZokhavlVJTLVPhia1jYtVUzLfSDH8qX3j8bVUSt0lZLkKUzckOMdHWfnaK3fxLwdFXvVURDyUPs3WFQxlY3CUUcFqk3kcSsDW18bp5U1kQ/BjlokmVWONjtpRZaOlI5MTk5mx8BV6vJWRYzGRrXcnjp3qV1S0CoShrKZYEWtxUfKsbW+IqtVGQgPnp7L2ROFQ/IyQcneTqQiI6c4lsCVZO1/IecopkI+UwNlXGlTJbsTVatAkThm09z6ak3BrKaiSacKNbaYL1KHebpGGlVHlHjSnvupzE+pzqJxgpPJod9R7ANgZ0Dvn9X67CqQa1aIVDT7fomR7oMdBhF/ThSE6hcH/sxsr6EhrDL19/BIWyjgzfg0v5hLPKJuk8T15JkuNLO/Cm4/3s2FOXbPeykrFBBwAzr/FiBcMOY8vylE1oNFVyesRXfEzJdgksPuE3JGSznS3Ra67VGEfT9qzP0juUHukfFALGsYFsMw/AvzInzZ
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updatePassword"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/UpdatePassword"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updatePassword

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["confirmNewPassword","email","newPassword","oldPassword"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"oldPassword":{"minLength":1,"type":"string","format":"password"},"newPassword":{"minLength":1,"type":"string","format":"password"},"confirmNewPassword":{"minLength":1,"type":"string","format":"password"}},"additionalProperties":false,"title":"UpdatePasswordDTO"}},"text/json":{"schema":{"required":["confirmNewPassword","email","newPassword","oldPassword"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"oldPassword":{"minLength":1,"type":"string","format":"password"},"newPassword":{"minLength":1,"type":"string","format":"password"},"confirmNewPassword":{"minLength":1,"type":"string","format":"password"}},"additionalProperties":false,"title":"UpdatePasswordDTO"}},"application/*+json":{"schema":{"required":["confirmNewPassword","email","newPassword","oldPassword"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"oldPassword":{"minLength":1,"type":"string","format":"password"},"newPassword":{"minLength":1,"type":"string","format":"password"},"confirmNewPassword":{"minLength":1,"type":"string","format":"password"}},"additionalProperties":false,"title":"UpdatePasswordDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      