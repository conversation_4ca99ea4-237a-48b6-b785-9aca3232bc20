---
id: get-cars
title: "getCars"
description: "getCars"
sidebar_label: "getCars"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v5DYM/SsCz9pMGqAX34q0W6QoigWStodgDrTNsYXIkkpSk04N//dC8kxm8rHtXsYj8vGRIqk3g+Ig0DzCLTJsLTBJikFIoJnh5vq6fHqSjl1SFwM0cJ+7jkRgWSwIdZmdHqB5nKElZGJoHrfL1kJMxFhC7npoYCC9RRawb8jO9ol0jEcoWEioIzSwweQ23YoQ4j2x1FyZPTQwqiZpNhsfO/RjFG3mFFmXd2l+LQCzEoCFPbLD1q+XLBHrLXeYvUID319ffwfLsti3eXZMMn7qaf8Jk7vCfzLTM7XilOQq1LJfp/2R9uRjmijoKfnXSDPqN5LeKw4uDP9H6N2evpHxC8c+d+XwQro9z/a+G2laW3Wa8Ax6SHRMWSZTIdCcAPb453PkCUtHf/nzoe6LC7tYw536gv9cSjW3yOaHL3dlMMSy1rS/eVfmw+ik4EziuHc9iaHQp+iCitlFNjqS6aP+Rmoqb4H+cXNl7vRrEROGtZdlw2z5NR0qDZEdnc4+t0Zy+1LIpb2N8cmFQazB0JssxAazjhTUdXX3a3L0Pj5LdYvRaBLxLvJkXl6IGMndaHDFGKbBia6uSuzj4II13omei11RxMUS6Plo7cnTBegi5NXN/Fq4yam6elJ0Xkx7eJ085KkltkYIuRtPtAU24RPV2qbYk7emY8Jzpo969g5wbl5O/eo7mowoapZ1BKEj7/+LuVZxGuQHMzAudD73xZtQ5Dlyb5iEVGooTei82RO73WlosFhIUXTCUJY1YN3tgdR8pGEvj0Hpb90kjy4Ugvom56OOPQImBxaqkm0tFK0q1nluUeh39stSzH9l4qKl27NEVTm9UMeff3oAC+WGF5nfPLuqkEcXhkMVPJ/LaZ5XxEN8orAUnXyiQym9nGHZLsvyL/3xJZU=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getCars"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/cars"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getCars

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      