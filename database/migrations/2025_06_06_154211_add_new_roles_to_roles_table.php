<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Config;

return new class extends Migration
{
    public function up(): void
    {
        // Define new roles to add
        $roles = [
            'freshcar_admin',
            'operator',
            'car_club_subscriber',
        ];

        // Create each role
        foreach ($roles as $role) {
            Role::updateOrCreate(['name' => $role]);
        }
    }

    public function down(): void
    {
        // Remove the roles added in this migration
        $roles = [
            'freshcar_admin',
            'operator',
            'car_club_subscriber',
        ];

        foreach ($roles as $role) {
            Role::where('name', $role)->delete();
        }
    }
};