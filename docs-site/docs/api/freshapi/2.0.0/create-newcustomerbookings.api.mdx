---
id: create-newcustomerbookings
title: "createNewcustomerbookings"
description: "createNewcustomerbookings"
sidebar_label: "createNewcustomerbookings"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createNewcustomerbookings"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/newcustomerbookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createNewcustomerbookings

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"registerDTO":{"required":["address","contactNumber","email","firstName","lastName","password"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true}},"additionalProperties":false,"title":"RegisterDTO"},"bookingDTO":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"NewCustomerCarBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"NewCustomerBookingDTO"}},"text/json":{"schema":{"type":"object","properties":{"registerDTO":{"required":["address","contactNumber","email","firstName","lastName","password"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true}},"additionalProperties":false,"title":"RegisterDTO"},"bookingDTO":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"NewCustomerCarBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"NewCustomerBookingDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"registerDTO":{"required":["address","contactNumber","email","firstName","lastName","password"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true}},"additionalProperties":false,"title":"RegisterDTO"},"bookingDTO":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"NewCustomerCarBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"NewCustomerBookingDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      