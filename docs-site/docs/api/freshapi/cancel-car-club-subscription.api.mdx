---
id: cancel-car-club-subscription
title: "Cancel a subscription"
description: "This endpoint cancels an active car club subscription. The subscription is identified by its unique ID. The cancellation reason and notes are optional."
sidebar_label: "Cancel a subscription"
hide_title: true
hide_table_of_contents: true
api: eJztV1tz27gV/isY5GF3W4pS7Gza8s0rOx3teBOPpXYfIs/mEDwisQYBBhcrqkb/vXMASqIsZ9PHPqxeJJHnfvnwYcs91I4XH/kULJuqUPKHjFfohJWdl0bzgi8a6RjqqjNSeyZAC1SOgWYgvHxCJsAyoULJXCgPejlbNHjyhEnHZIXay5XEipUbJr1jQcvPAdnsOikk6wqigkVwRjPQFdPGo2NgkZloDFTOM246tFF0VvGCJ90pWEpjPvDMM96BhRY9Wsp1yzW0yAs+DG9W8YxLyrcD3/CMW/wcpMWKF94GzLgTDbbAiy33m460pfZYo+UZXxnbgk+PLi/4bveQ1NH5n0y1IR1htEft6Sd0nZIixj3+3VGJt+fGTfk7Ck+RW8rSS3TRzKA+97E8lPq3Q8q4DkpBqTCls8teMPWeijyw5ryVuj7X3WUcqkqmPtwN4luBcphxLz3J8hdaMR04vV584GTL4xf/ZyHI1mAw/vLXPytC1iy6zmiXnF9MJvR1Ck9DG3v8wIq5IAQ6twpKbfgu428mb76hq41nKxN0FZvhUAQr/SbiRYlg0fLi4wOtdou+MYQ4FSr0GOHFN7zg46fLMXRyLMCOCBHHQ4Bx4+0p3uw4ebFPe1AKVvGCN953rhiPlRGgGuN8se2M9SR8GvstCbBkgGf8CaykRsRCkUbKdgVBUa9fT15PqKTZcz8VPqEyXYva5yuLrhFgc2Hy8Hjm8foouvd7bg86OaKXUuDo9eQff//x7duLN2/+djnJMdA8jNbo/EVug86h68ZnTuYeaqnrP3TwrUDvrKmCiE3dm3k4dnROC5XqtO/rYa7JCe+xnv73Aln/491+dX7+dXHm9edfF+wq+MZY+Z90gDUIFVoWHCXkG2Q/RSssmc/ZzRdoO4UF++5Er9jLbb15RL37Lg4k4cH98VDpdb+++ZM/2OP9/u7oyFuZWIB+M99RaRmxgau7Gc0VWpfyu8wn+YRn/MtImTrqpMaMZVuP9x2J7/IuYkMJ4rG2tFFTo4zlBX/1Ln54xkH5BX7xZw53z6v6W6QfV3cz1oBjJaJm/chixbxhFjsFAmN5K+Pfo2fRImn8+yKL7EG6uNwb9ExqFlyiH6QhlKRphq5z+W9LvdSvXrF5aFuwm6U+eO6seZIVHlmQYytjWQs6zaoA67LEg8BjbazE/X8VyqU+gYHjC1Ya8yh17VKUwaFlEHxDHCkdAzmbeQZKmbWLr91Se8M6tITh7EB/HKFdwyAJMYu1dD69ipaVqaXOmJLOH8Jd6iSGNAhM47rPImLaMamBzklyKoXOQkevlrpCD1I5quuJex3aEm3GHIIVzd4uibXwiDG61lSoMiYsArla6hcJpTtKvFTA0FXpXf+o7+XUGudGH6yspWb36EywAtm8gZj199MP9/MfBo12oSPodP+TXpwBEQUr04IkwhrX0+W996uTZi71lVKDEeoJ5rOW92hxCxaeULE5aOFDm7OZFipUac4TGLGIDjTR9Owl5CmW+tOnT0v9FXTZmGBHCWJ6QZ5Fogoinh49SyZefivrxmsK7J/WBILIU1Ber9e5b1DtxWjtux6ddxlXUqB2ODD6y2xxZsR0qFOlc2Prca/kxiQbeaJt3YfVPB0uA71a+iaUuTDtOJ7faEFXh1AG0FR2I2EsjktlyjF1bHw7m968n99EVLPgcaRkK/05UaDhoPcsvqc6xDvRyliRYAi1CxZZf/Ix56GUSvpNnPAVSDoFoMY8nhr9obzlZJIXbycZ7+gY4q3UIRKKcsMLPrtjUFUWnWPGptWeXbN1g3o4M1jFCtOKH03++E2LFBe2IBUTpi2ljrMRGQLvjPMt6EG3EjdjcLKRzw/AwSXn//jO2B/08dLRKZCaqhfncNvTuI/86ZJOqE7SOvRUjkjBEI14xotn18eHjBNfIwPbbQkO/2XVbkePPwe0RCQfjjQtMr5KOvpdHejxV+v5/X1/G/2BfS2H/iHoTWSDKtA/nvFH3JxfdXcPu4wnkIiRJKFp8jdakKmjkbML6wkJvr65vVnQBpX9VZfwnBfcwppu0bBOYZi+bnFE1/SlQNcBapJNVuPtIVALDnzsGf+Ksb6Y6HabJBYRznaHvCO8Ubq73X8BpqH6uw==
sidebar_class_name: "delete api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Cancel a subscription"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/v3/api/car-club/subscriptions/{subscriptionId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint cancels an active car club subscription. The subscription is identified by its unique ID. The cancellation reason and notes are optional.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"subscriptionId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"CarClubSubscriptionCancellationDTO"}},"text/json":{"schema":{"type":"object","properties":{"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"CarClubSubscriptionCancellationDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"CarClubSubscriptionCancellationDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Subscription cancelled successfully"},"404":{"description":"Subscription not found"}}}
>
  
</StatusCodes>


      