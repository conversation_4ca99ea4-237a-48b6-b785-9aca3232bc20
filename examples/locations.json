{"locations": [{"id": "gbCMI7jRyNu9X9qJiyuL", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car - Sheffield - MASTER", "country": "GB", "locale": "en_US", "timezone": "Europe/London", "firstName": "Sam", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+447969647848", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car - Sheffield - MASTER", "country": "GB", "timezone": "Europe/London", "email": "<EMAIL>"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["phone", "email"], "saasSettings": {"saasMode": "activated", "customerId": "cus_RCfXUIWEfVP6HL", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2023-03-17T14:42:46.190Z", "domain": ""}, {"id": "JLgCDbJb6tkQe5uTxUwb", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car Franchise Leads", "address": "29a Stafford Street", "city": "Edinburgh", "state": "Scotland", "country": "GB", "postalCode": "EH3 7BJ", "website": "https://www.freshcarvaleting.com/franchise-opportunities/", "timezone": "Europe/London", "firstName": "Sam", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+441315107800", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car Franchise Leads", "address": "29a Stafford Street", "city": "Edinburgh", "state": "Scotland", "country": "GB", "postalCode": "EH3 7BJ", "website": "https://www.freshcarvaleting.com/franchise-opportunities/", "timezone": "Europe/London"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": true, "contactUniqueIdentifiers": ["phone", "email"], "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2023-04-20T07:31:28.643Z", "domain": ""}, {"id": "RaSZJkYY0d3WqH9tXuzp", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car Mobile SMART Repairs Birmingham", "address": "444 College Road, Crossways Court", "city": "Birmingham", "state": "England", "country": "GB", "postalCode": "B44 0HE", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-birmingham/", "timezone": "Europe/London", "firstName": "Arturs", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+441212852912", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/e4ec4d34-5001-484b-be88-3714c755cec9.png", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car Mobile SMART Repairs Birmingham", "address": "444 College Road, Crossways Court", "city": "Birmingham", "state": "England", "country": "GB", "postalCode": "B44 0HE", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-birmingham/", "timezone": "Europe/London", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/e4ec4d34-5001-484b-be88-3714c755cec9.png"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["phone", "email"], "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2023-11-15T08:59:12.891Z", "domain": ""}, {"id": "PU9vSIl4zreFEPxpMEfX", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car Mobile SMART Repairs Glasgow (<PERSON><PERSON>)", "address": "2nd floor, Clyde offices, 48 West George Street", "city": "Glasgow", "state": "Scotland", "country": "GB", "postalCode": "G2 1BP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-glasgow/", "timezone": "Europe/London", "firstName": "Sami", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+441412808725", "automaticMobileAppInvite": true, "business": {"name": "Fresh Car Mobile SMART Repairs Glasgow (<PERSON><PERSON>)", "address": "2nd floor, Clyde offices, 48 West George Street", "city": "Glasgow", "state": "Scotland", "country": "GB", "postalCode": "G2 1BP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-glasgow/", "timezone": "Europe/London"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["phone", "email"], "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2023-11-13T09:35:25.281Z", "domain": ""}, {"id": "KSXw5T8iQixCZ4vSoFQq", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car Mobile SMART Repairs Glasgow/Paisley (<PERSON>)", "address": "2nd Floor, Clyde Offices, 48 W <PERSON>", "city": "Glasgow", "state": "Scotland", "country": "GB", "postalCode": "G2 1BP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-glasgow/", "timezone": "Europe/London", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+441412808725", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/f0049aa5-bbef-4639-a954-ce811a821c85.png", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car Mobile SMART Repairs Glasgow/Paisley (<PERSON>)", "address": "2nd Floor, Clyde Offices, 48 W <PERSON>", "city": "Glasgow", "state": "Scotland", "country": "GB", "postalCode": "G2 1BP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-glasgow/", "timezone": "Europe/London", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/f0049aa5-bbef-4639-a954-ce811a821c85.png"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["phone", "email"], "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2023-11-10T09:39:06.497Z", "domain": ""}, {"id": "m4u24FafMYQDoLt9fcGI", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car SMART Repairs - Dundee/Perth/Aberdeen", "address": "Pretoria, Colliston", "city": "Arbroath", "state": "Scotland", "country": "GB", "postalCode": "DD11 3RP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-dundee/", "timezone": "Europe/London", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "david<PERSON><PERSON><PERSON>@freshcar.co.uk", "phone": "************", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car SMART Repairs - Dundee/Perth/Aberdeen", "address": "Pretoria, Colliston", "city": "Arbroath", "state": "Scotland", "country": "GB", "postalCode": "DD11 3RP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-dundee/", "timezone": "Europe/London", "email": ""}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["email", "phone"], "crmSettings": {"deSyncOwners": true, "syncFollowers": {"contact": true, "opportunity": true}}, "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2025-05-12T10:27:08.474Z", "domain": ""}, {"id": "0tzu9JKOXhgtm5OTlrYO", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car SMART Repairs Bristol", "address": "470 Bath road", "city": "Bristol", "state": "England", "country": "GB", "postalCode": "BS4 3AP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-bristol/", "timezone": "Europe/London", "firstName": "Fresh Car Smart Repairs", "lastName": "Bristol", "email": "<EMAIL>", "phone": "+448081759420", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/373124f8-76b6-4e8b-be11-8002a9bc38d7.png", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car SMART Repairs Bristol", "address": "470 Bath road", "city": "Bristol", "state": "England", "country": "GB", "postalCode": "BS4 3AP", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-bristol/", "timezone": "Europe/London", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/373124f8-76b6-4e8b-be11-8002a9bc38d7.png"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": true, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["email", "phone"], "crmSettings": {"deSyncOwners": true, "syncFollowers": {"contact": true, "opportunity": true}}, "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2024-10-04T09:56:21.872Z", "domain": ""}, {"id": "7d9ZTS4XAGsANFlo61Uk", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car SMART Repairs North Manchester", "address": "20 Eltham Street", "city": "Manchester", "state": "England", "country": "GB", "postalCode": "M19 3AL", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-manchester/", "timezone": "Europe/London", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "0808 175 9420", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car SMART Repairs North Manchester", "address": "20 Eltham Street", "city": "Manchester", "state": "England", "country": "GB", "postalCode": "M19 3AL", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-manchester/", "timezone": "Europe/London", "email": ""}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["email", "phone"], "crmSettings": {"deSyncOwners": true, "syncFollowers": {"contact": true, "opportunity": true}}, "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2025-02-04T10:56:28.090Z", "domain": ""}, {"id": "OARvrKfTsVjkR7CeIRsj", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car SMART Repairs Northampton", "address": "13 Violet Close", "city": "Northampton", "state": "England", "country": "GB", "postalCode": "NN142JR", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-northampton/", "timezone": "Europe/London", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+448081759420", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/c119e4eb-27bc-450e-8b58-cf32c9f8c92d.png", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car SMART Repairs Northampton", "address": "13 Violet Close", "city": "Northampton", "state": "England", "country": "GB", "postalCode": "NN142JR", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-northampton/", "timezone": "Europe/London", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/c119e4eb-27bc-450e-8b58-cf32c9f8c92d.png"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": false, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["phone", "email"], "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2024-03-19T09:43:49.390Z", "domain": ""}, {"id": "G5rDymH8Qj6cWpjQ2Lh5", "companyId": "gLssl5EgVt3dRajTiEST", "name": "Fresh Car SMART Repairs Norwich", "address": "82 Rye Ave", "city": "Norwich", "state": "England", "country": "GB", "postalCode": "NR3 2SQ", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-norwich/", "timezone": "Europe/Amsterdam", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "+448081759420", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/2413c0d2-b04e-4b54-b947-014eec5f73f0.png", "automaticMobileAppInvite": false, "business": {"name": "Fresh Car SMART Repairs Norwich", "address": "82 Rye Ave", "city": "Norwich", "state": "England", "country": "GB", "postalCode": "NR3 2SQ", "website": "https://www.freshcar.co.uk/packages-and-prices/maintenance/smart-repair-norwich/", "timezone": "Europe/Amsterdam", "logoUrl": "https://msgsndr-private.storage.googleapis.com/locationPhotos/2413c0d2-b04e-4b54-b947-014eec5f73f0.png"}, "social": {"facebookUrl": "", "googlePlus": "", "linkedIn": "", "foursquare": "", "twitter": "", "yelp": "", "instagram": "", "youtube": "", "pinterest": "", "blogRss": "", "googlePlacesId": ""}, "settings": {"allowDuplicateContact": true, "allowDuplicateOpportunity": true, "allowFacebookNameMerge": false, "disableContactTimezone": false, "contactUniqueIdentifiers": ["email", "phone"], "crmSettings": {"deSyncOwners": true, "syncFollowers": {"contact": true, "opportunity": true}}, "saasSettings": {"saasMode": "not_activated", "twilioRebilling": {"enabled": false, "markup": 10}}}, "dateAdded": "2024-10-04T08:13:07.254Z", "domain": ""}], "traceId": "ecf48877-4194-4446-b39b-50ba6b49462b"}