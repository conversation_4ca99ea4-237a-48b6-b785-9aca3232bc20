<?php

namespace Database\Seeders;

use App\Models\Package;
use App\Models\ServiceGroup;
use Illuminate\Database\Seeder;

class PackageSeeder extends Seeder
{
    public function run(): void
    {
        // Get service groups
        $valetGroup = ServiceGroup::where('name', 'Valet')->first();
        $smartGroup = ServiceGroup::where('name', 'SMART')->first();
        $detailingGroup = ServiceGroup::where('name', 'Detailing')->first();
        $maintenanceGroup = ServiceGroup::where('name', 'Maintenance')->first();
        $protectionGroup = ServiceGroup::where('name', 'Protection')->first();

        $packages = [
            // Valet Packages
            [
                'name' => 'Basic Valet Clean',
                'description' => 'Essential exterior and interior cleaning service.',
                'duration_minutes' => 60,
                'included_features' => [
                    'Exterior wash and dry',
                    'Interior vacuum',
                    'Dashboard wipe down',
                    'Window cleaning'
                ],
                'feature_description' => 'Perfect for regular maintenance cleaning to keep your car looking fresh.',
                'standard_price' => 25.00,
                'higher_price' => 35.00,
                'lower_price' => 20.00,
                'currency' => 'GBP',
                'service_group_id' => $valetGroup?->id,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Premium Valet Clean',
                'description' => 'Comprehensive cleaning service with additional care.',
                'duration_minutes' => 90,
                'included_features' => [
                    'Full exterior wash and wax',
                    'Deep interior vacuum',
                    'Leather conditioning',
                    'Tire shine',
                    'Air freshener',
                    'Dashboard protection'
                ],
                'feature_description' => 'Enhanced cleaning service with protective treatments for a showroom finish.',
                'standard_price' => 45.00,
                'higher_price' => 55.00,
                'lower_price' => 35.00,
                'currency' => 'GBP',
                'service_group_id' => $valetGroup?->id,
                'is_active' => true,
                'sort_order' => 2,
            ],

            // SMART Packages
            [
                'name' => 'Minor Scratch Repair',
                'description' => 'Professional repair of minor scratches and scuffs.',
                'duration_minutes' => 120,
                'included_features' => [
                    'Scratch assessment',
                    'Paint touch-up',
                    'Polishing',
                    'Protective coating'
                ],
                'feature_description' => 'Restore your vehicle\'s appearance by removing minor paint damage.',
                'standard_price' => 85.00,
                'higher_price' => 120.00,
                'lower_price' => 65.00,
                'currency' => 'GBP',
                'service_group_id' => $smartGroup?->id,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Dent Removal',
                'description' => 'Paintless dent removal for small to medium dents.',
                'duration_minutes' => 180,
                'included_features' => [
                    'Dent assessment',
                    'Paintless dent removal',
                    'Quality inspection',
                    'Touch-up if needed'
                ],
                'feature_description' => 'Remove unsightly dents without affecting your vehicle\'s original paint.',
                'standard_price' => 150.00,
                'higher_price' => 200.00,
                'lower_price' => 120.00,
                'currency' => 'GBP',
                'service_group_id' => $smartGroup?->id,
                'is_active' => true,
                'sort_order' => 2,
            ],

            // Detailing Packages
            [
                'name' => 'Full Detail Service',
                'description' => 'Complete interior and exterior detailing service.',
                'duration_minutes' => 240,
                'included_features' => [
                    'Paint correction',
                    'Clay bar treatment',
                    'Machine polishing',
                    'Interior deep clean',
                    'Leather treatment',
                    'Engine bay cleaning'
                ],
                'feature_description' => 'Comprehensive detailing service to restore your vehicle to like-new condition.',
                'standard_price' => 200.00,
                'higher_price' => 280.00,
                'lower_price' => 160.00,
                'currency' => 'GBP',
                'service_group_id' => $detailingGroup?->id,
                'is_active' => true,
                'sort_order' => 1,
            ],

            // Maintenance Packages
            [
                'name' => 'Basic Service Check',
                'description' => 'Essential vehicle health check and basic maintenance.',
                'duration_minutes' => 90,
                'included_features' => [
                    'Oil level check',
                    'Tire pressure check',
                    'Fluid level inspection',
                    'Battery test',
                    'Light check'
                ],
                'feature_description' => 'Keep your vehicle running smoothly with regular maintenance checks.',
                'standard_price' => 35.00,
                'higher_price' => 45.00,
                'lower_price' => 25.00,
                'currency' => 'GBP',
                'service_group_id' => $maintenanceGroup?->id,
                'is_active' => true,
                'sort_order' => 1,
            ],

            // Protection Packages
            [
                'name' => 'Ceramic Coating',
                'description' => 'Professional ceramic coating application for long-lasting protection.',
                'duration_minutes' => 360,
                'included_features' => [
                    'Paint preparation',
                    'Ceramic coating application',
                    'Curing time',
                    '2-year warranty',
                    'Maintenance kit'
                ],
                'feature_description' => 'Ultimate paint protection with hydrophobic properties and UV resistance.',
                'standard_price' => 450.00,
                'higher_price' => 600.00,
                'lower_price' => 350.00,
                'currency' => 'GBP',
                'service_group_id' => $protectionGroup?->id,
                'is_active' => true,
                'sort_order' => 1,
            ],
        ];

        foreach ($packages as $packageData) {
            if ($packageData['service_group_id']) {
                Package::updateOrCreate(
                    [
                        'name' => $packageData['name'],
                        'service_group_id' => $packageData['service_group_id']
                    ],
                    $packageData
                );
            }
        }
    }
}
