---
id: get-car-club-cancellation-reasons
title: "Get Cancellation reasons"
description: "This endpoint retrieves all available cancellation reasons in the system."
sidebar_label: "Get Cancellation reasons"
hide_title: true
hide_table_of_contents: true
api: eJytVktz2zYQ/is7yCHtDEUqdpK2vLmKk1HGSTyR2xwsTwKCKxJjEEAWgGRVw//eAUjZ8qNND9VBorjv17e7Y543jpWXbMYJZipU7CpjNTpB0nppNCvZRSsdoK6tkdoDoSeJa3TAlQK+5lLxSiEIrgUqxaMQEHJntAOpwbcIbus8djnLmLFIiWVes5I16GecotXZgfTnQZhljNBZox06Vu7Y0XQaf+77NnvK6t7FGlwQAp1bBaW2rO8z5lAEkn7Lyssdq5ATEisvr/qrjHXoWzN6xTJmuW9ZyYr1ccGtLASniVChKg7jnNCtqw5pjeSS3kCKlaz13rqyKJQRXLXG+XJnDfmePczvWWSAQQHL2JqTjBlNQUeJIeoVD8qzkr2Yvpiyvu+zh3ZqXKMytkPt8xWhawWnXJg8XD+y+OaOdW/3sT5u5SQSpcDJi+lvv756/fro5ctfjqc5BjIWJxt0/iinoHNubfHIyMLzRurmXw38yNFzMnUQqbh7NVd3RVyIFrshT/tS7pjfWhyNxLIkFlbuGbLx4a2hjsd0vv9y8cjq+y8XcBJ8a0j+NXRWi7xGguBiQLGjf09aYFCfw+kN76zCEp7fkyv3fDtvrlH3z1MPSr0yyVPpVXTtbcwBxPk7OZ/HBkBygyPH+TSfsozdTJRpksyQwUJ2TbFPXaLlVjcxOC6uGzJB1zOjDLGSPXubPixjXPkLvPGPDPYPw/+aBv7kfA4td1Ahahh7C2vwBgit4gJTHmrjP6KHpDFK/HmUAdc1SAfaeNiijxgQHNZQbZOEUDK2HbfW5V+XeqmfPYNF6DpO26W+tWzJrGWNd7jjYGUIOq6HphKcXBa/QXCPjSGJ+/8qVEvtQnUb0gEBKmOupW7c4GVwSMCDb1F7KVLJcpj7iGxm4xLZLbU3YJFWhjq4hS8XoaUFPjABYSOdH0hJszKN1Bko6fytu0s9sCHFVxo3YxQ1KrzjOpS5F5waXIdgI2mpa/RcKhfzes+8Dl2FlIFDTqLd641sHb/G5F1nalQZCEIeTS31bX4e5m3keCqBwdYDbXw11nJGxrnJJ5KN1PAZnQkkEBYtT1H/NPv0efHzQaFdsBHj3H+SSz0gEmNtOi4j5H8P6LzLR+sn94q51CdKHbRQ5JaED0o+jvUZJ75GBQuuhQ9dDnMtVKiHPh9QA9IY77faUxBRLvW3b9+W+h9gYGsCTQYsGBlZxoTRnosE85onsLpoEc5k03odHXtHJkQsu4+em80m9y2qPVscezvCaJ8xJQVqhwdKP8wvHikxFvWQ6dxQU4xCroi8fcY8Uuc+rRbDFjiQa6RvQ5UL0xWriDZIXNe3rhxAU2UnwhAWlTJVEStWnM1npx8XpwnViHucKNlJ/3izx+aIdEj0mId0hawMiQGGULtACOOKAud5JZX029ThKy4jXPMG8wTv4/bcsaiSla+nGbNxX7BO6uAxQueWlWx+DryuCZ0DQ8Noz9/ApkV92DNYpwzHEb9T+eqHGqNf2HGpQJiukjr1RlrlzBrnO64PqvUOPTx13DxcVrvUPqj9/3yojWvU440vrOJSx5BT8+zG0+iSrY/jWrEy9vB4HqXHJw6kq4zFEyhK7XYVd/gHqb6Pr78HpHiOXd1dPukiOzjI3p3Gzo0FOFjwDxZ6OrxGEtfbdEepEP/tdgPHRRq7eH5dY6xMGkPWX/V9/zfwp+Ny
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get Cancellation reasons"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/car-club/cancellation-reasons"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint retrieves all available cancellation reasons in the system.

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Cancellation reasons retrieved successfully"}}}
>
  
</StatusCodes>


      