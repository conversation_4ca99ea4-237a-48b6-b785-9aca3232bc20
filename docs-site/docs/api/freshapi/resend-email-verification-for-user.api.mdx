---
id: resend-email-verification-for-user
title: "Resend email verification for specific user"
description: "Resend email verification for a specific user using signed URL"
sidebar_label: "Resend email verification for specific user"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Resend email verification for specific user"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/auth/verify-email-resend/{id}/{hash}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Resend email verification for a specific user using signed URL

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"description":"User ID","schema":{"type":"integer","format":"int64"}},{"name":"hash","in":"path","required":true,"description":"Email hash for verification","schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Verification link sent","content":{"application/json":{"schema":{"type":"object","properties":{"data":{"type":"object","properties":{"status":{"type":"string","description":"Verification status"}}},"message":{"type":"string","description":"Success message"}},"title":"EmailVerificationResponse"}}}},"403":{"description":"Invalid signature","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}},"422":{"description":"User already verified","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}},"429":{"description":"Too many requests","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}}}}
>
  
</StatusCodes>


      