{"registerDTO": {"firstName": "{example) Joe", "lastName": "Bloggs", "contactNumber": "", "email": "<EMAIL>", "address": {"addressLine1": "", "town": "", "postcode": ""}, "password": "Temp9431!"}, "bookingDTO": {"requestedDate": "2025-06-08T22:10:06.598555Z", "confirmedDate": "2025-06-08T22:10:06.599786Z", "addressId": 0, "additionalComments": "submitted with no photos", "notes": "Booking created from GoHighLevel opportunity: {example) Joe -", "preferredTime": "2025-06-08T22:10:06.599883Z", "bookingStatusId": 0, "totalCost": 77, "enquiryStatus": "confirmed", "resourceId": 0, "resourceName": "<PERSON><PERSON>", "timeOfDay": "morning", "bookingReferenceNumber": "GHL-935AB82B", "bookingHubDurationMinutes": "60", "overridePrice": 77, "bookingCustomerCars": [], "isPrepaid": false, "isRefunded": false, "customerCarClubPackageId": 0, "currency": 0}, "paymentDetails": {"isPayNow": false, "paymentMethodId": 0}, "password": "Temp1993!"}