---
id: pause-car-club-subscription
title: "Pause a subscription"
description: "This endpoint pauses an active car club subscription. The subscription is identified by its unique ID. The pause reason and date are optional."
sidebar_label: "Pause a subscription"
hide_title: true
hide_table_of_contents: true
api: eJzNVt1z00YQ/1d2jgdgRpZMEmirtzRAxwwlGZyWB5yBlbSWbnK6E/fhxPXof+/sSU7sOEAfm5dYd/t9v/3tboTH2on8szhDC2cqFOIqERW50srOS6NFLi4b6YB01RmpPXQYHDlADVh6uSIo0UKpQgEuFHdqKVw2tHcC0oGsSHu5lFRBsQbpHQQtvwWC2etBIRoHS+iMBtQVVOgJ0BKYaARVKhJhOrLIn7NK5CLqnKHl4Oc7DkUiOrTYkifLGW6ExpZELnajmlUiEVJHM74RibD0LUhLlci9DZQIVzbUosg3wq871pbaU01WJGJpbIt+ODo+En1/xequM9qRY42j6ZT/7VdzN8Qh3wpcKEtybhmUWos+ESfTk58oauNhaYKuRN8nwlEZrPTrmGVBaMmK/PMVB9SSb0wsk3E+lsQ3IhfZ6jjDTmYl2gk/XrZbFJdt9mvUZzFQwZ7salvOYJXIReN95/IsU6ZE1Rjn801nrO/FQxi9ZwEYDIhErNBKLNRQKdYYMl5iUFzTF9MXU9H3ffLQT0UrUqZrSft0ack1Jdq0NGm4PvD4+l506/fQHnZywpeypMmL6W+/vnz16ujk5JfjaUrBmo4mN+T8UWqDTrHrsgMnc4+11PUPHfws0AtrqlDGh92aubp/1TlDcKjT9m3v0MhOxIhS/h4FkvHH2y1E3326PPD67tMlnAbfGCv/ie0EDWFFFoLjhHxD8Hu0AoP5FN7cYtspyuHpnl6+ldt4c026fxpBKfXSxEilVxzaW64BMM2cXswYAGTdEMhxOk2nIhG3E2XqqDNUMJNtnW1LF+/STtecHJbXtWX4nxllrMjFk7fxTyQClb+kW3/gsH+Y/pfIa6cXM2jQQUGkYcQWVeANWOoUlhTrUBn/gTxEi6zx91ES6Um62Ilr8iA1xF4u1lGjVJJhh13n0i8LvdBPnsA8tC3a9ULfee6sWcmK7unVwdJYaFEPoCrRumRgWPRUGytp+61CsdB7XXt/AYUx11LXbogyOLKAwTfMviUO/DzzgEqZGxev3UJ7Ax1ZJjW4I1jH1NQADkJgqZbOD1fRsjK11Ako6fxduAs9iJHlI003YxYVKbqX2tXZS04NoUPo+GqhK/IoleO67rnXoS3IJuAIbdls7bJYi9cUo2tNRSqB0hKyq4V+dFS5e4nHChi6argbj8a3PLPGucm5lbXU8JGcCbYkmDcYs352dv5x/nznoV3omOPcf9KLGCijYGValBp4KpHzLh29n+495kKfKrUDoXGGPXjysa3fo8UVKZijLn1oU5jpUoVqwPnAGhDbmBHNZ49RRL7QX79+Xejv0MDaBDsZuGAUFIkojfZYRpofBzFP/PeybrzmwP6wJjCX7bPnzc1N6htSWzFu+26k0T4RSpakHe0Y/XN2eWDEdKSHSqfG1tmo5DKW7RPhybbufDkfpsCOXi19E4q0NG0Why1Z1NVdKDvUVHST0ljKCmWKjF8sez87e/Nh/iaymkVPEyVb6Q+nOoOD7yHecx3isrU0thxoiLQLlmAcUeA8FlJJv44IX6Jkusaa0kjv4/TcCDYp8lfTRHQ8L0QrdfA8wIu1yMXsArCqLDkHxg6tPXsNNw3pXcxQFSvMLX5v8uVPLXJc1KJUUJq2kDpiI47yuIO0qHde6yIufLjXkA8H1SZCh7T/P+6i4xz2dOuzTqHUXLOIvs24a30Wq2OeS53kJhj3LZ7ZuxwkEpEf7KXD0nWVCF6r2NBmU6Cjv6zqez7+Fsjyznd1v03FxaySjn9XIl+icvSDcj77OK67z+F7uYyHqNdxaVOBv0Qirml9uEv3V/3OynlxPudeZEjtrCwPVpQY8aNONptB4jISSX/nMxILu+r7fwENsnwZ
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Pause a subscription"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/v3/api/car-club/subscriptions/{subscriptionId}/pause"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint pauses an active car club subscription. The subscription is identified by its unique ID. The pause reason and date are optional.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"subscriptionId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Subscription paused successfully"},"404":{"description":"Subscription not found"}}}
>
  
</StatusCodes>


      