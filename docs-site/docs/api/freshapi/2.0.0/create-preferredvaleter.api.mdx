---
id: create-preferredvaleter
title: "createPreferredvaleter"
description: "createPreferredvaleter"
sidebar_label: "createPreferredvaleter"
hide_title: true
hide_table_of_contents: true
api: eJzlVk1z2zYQ/SuYPbb0R9zphbc0bmbcySSeWk0OHh1WxIpEDAIMsJCicvjfOwtItCPZTe65SATw8HaxHw8YgbGNUN/DbaA1hUD6I1piCrCsINCXRJH/8HoH9QiNd0yO5ROHwZoG2Xh38Tl6J3Ox6ahH+ZJ9JpAW3kDRp9DQzTVU8+A99iQGeDcQ1OBXn6lhqGAIfqDAhmKhmbfW4wFrHFNLASpY+9Ajl6nfrmA6oq9H6I17R67lDupXs7HIwbgWpqkC1NrIEdDePjG8RhupAjZsBf9a6+PYXC8+5P1MX/knPv7TIvjl1581DlOxOHgXi79Xl5fypyk2wQxCDDXcpaahGLO9SE0KhndQ34+wIgwUoL5fTssKxHwO6I2GGppAyDSb3ew7szrifhHWE3deiAYfc1xRYgAXOJiLk34Xv8KGQsxupWChho55iPXFhfUN2s5HrsfBB55OXHgnAFUIoIINBoMrW+IhO0pA1pisZOr3y8tXErnq2M46UOzONG3OcDDn+G8KtKVVNEzx3BGfmL2mDVk/9OT4YPwl0oT8g6R3jK1x7fcIrdnQDzLeBq9TI4OZdPlYBnfSMCVUh2KYy1xMSmYyBOoDoNp/vD3U/l+fFrm0pAn/flTtP79iP1g67qLL4x6Zu6EC49Y+O7Cv+7dyWPUGg3p9eyOppRDLqTZXJwdddCYKTg3Bb4ymqMjpwRvHUa19UNyR0p7fE6vMK9CPV+fqhl/a0aMr2WgwxEp+VYNMrQ+GDmObViqm1ezI0/mV9w/GtbFS6LRKkYLCxB053itXNo7W+m3My1GxVwMFkRU1t2NUMTWdwoJRgVoTuSxlYutb4yplTeRHZwuKJLLK0XY/q8nSE9CTLd+czBbHVRrykiZGY6Na7b417lK/olCpSBia7kArsB4fKPvWe022UlklZkvPxewE8Bi8NOiytp9SkZFTLClwDVn7f8zZi0Min8mBMq6xScvqgDFufdAqUCSOeSv1aKzaUDDrQ9KkUkXUesx3jitVXIRQfU8w5+7K9/dg0Tjhy00+7kXyHnAwUD37LBIhFMQ4rjDSP8FOk0x/SRRE05eP+pdlvYKOUGeNH+GBdlDDm/KSOluIHwK3Sfw5eVWJTMwifvvhbiGdv3+NSVqhhoBbuVFxCzWAXCA55rnjZW4Ei65N2Aq2cOYrL8kh50AcyUr2dL+EbvfEw3EsiIV/IDfJPVAOxDKGaTlN038xfqbn
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createPreferredvaleter"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/PreferredValeter"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createPreferredvaleter

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["resourceID","resourceName"],"type":"object","properties":{"resourceID":{"type":"integer","format":"int32"},"resourceName":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"AddPreferredValeterDTO"}},"text/json":{"schema":{"required":["resourceID","resourceName"],"type":"object","properties":{"resourceID":{"type":"integer","format":"int32"},"resourceName":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"AddPreferredValeterDTO"}},"application/*+json":{"schema":{"required":["resourceID","resourceName"],"type":"object","properties":{"resourceID":{"type":"integer","format":"int32"},"resourceName":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"AddPreferredValeterDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      