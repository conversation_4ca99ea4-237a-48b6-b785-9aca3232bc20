#!/bin/bash

# Setup script for Cloud Run environment variables
# This script helps you set up environment variables for your Laravel app on Cloud Run

set -e

ENVIRONMENT=${1:-"staging"}
PROJECT_ID=${2:-"fresh-car-test"}
REGION=${3:-"europe-west1"}
CLOUD_SQL_INSTANCE=${4:-"freshcar-staging-core"}

echo "Setting up environment variables for Laravel on Cloud Run"
echo "Environment: $ENVIRONMENT"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Cloud SQL Instance: $CLOUD_SQL_INSTANCE"

# Construct Cloud SQL connection name
CLOUD_SQL_CONNECTION_NAME="$PROJECT_ID:$REGION:$CLOUD_SQL_INSTANCE"
echo "Cloud SQL Connection Name: $CLOUD_SQL_CONNECTION_NAME"

# Function to create secrets in Secret Manager
create_secret() {
    local secret_name=$1
    local secret_value=$2
    
    echo "Creating secret: $secret_name"
    
    # Check if secret already exists
    if gcloud secrets describe "$secret_name" --project="$PROJECT_ID" >/dev/null 2>&1; then
        echo "Secret $secret_name already exists, updating..."
        echo -n "$secret_value" | gcloud secrets versions add "$secret_name" --data-file=- --project="$PROJECT_ID"
    else
        echo "Creating new secret: $secret_name"
        echo -n "$secret_value" | gcloud secrets create "$secret_name" --data-file=- --project="$PROJECT_ID"
    fi
}

# Function to set substitution variables for Cloud Build
set_substitution_vars() {
    echo "Setting up Cloud Build substitution variables..."
    
    # You can set these via Cloud Build triggers or pass them during build
    echo "Add these to your Cloud Build trigger substitution variables:"
    echo "_DB_CONNECTION=pgsql"
    echo "_DB_PORT=5432"
    echo "_DB_DATABASE=your-db-name"
    echo "_DB_USERNAME=your-db-username"
    echo "_CLOUD_SQL_CONNECTION_NAME=$CLOUD_SQL_CONNECTION_NAME"
}

# Create secrets for sensitive data
echo "Creating secrets in Secret Manager..."

# Generate APP_KEY if not provided
if [ -z "$APP_KEY" ]; then
    echo "Generating new APP_KEY..."
    APP_KEY=$(openssl rand -base64 32)
fi

create_secret "$ENVIRONMENT-app-key" "$APP_KEY"

# You'll need to provide these values
read -p "Enter your database password: " -s DB_PASSWORD
echo
create_secret "$ENVIRONMENT-db-password" "$DB_PASSWORD"

read -p "Enter your Stripe secret key: " -s STRIPE_SECRET
echo
create_secret "$ENVIRONMENT-stripe-secret" "$STRIPE_SECRET"

# read -p "Enter your Stripe webhook secret: " -s STRIPE_WEBHOOK_SECRET
# echo
# create_secret "$ENVIRONMENT-stripe-webhook-secret" "$STRIPE_WEBHOOK_SECRET"

# Set IAM permissions for Cloud Run to access secrets
echo "Setting IAM permissions for Cloud Run service account..."
SERVICE_ACCOUNT="<EMAIL>"

gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$SERVICE_ACCOUNT" \
    --role="roles/secretmanager.secretAccessor"

# Set IAM permissions for Cloud Run to access Cloud SQL
echo "Setting IAM permissions for Cloud Run to access Cloud SQL..."
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$SERVICE_ACCOUNT" \
    --role="roles/cloudsql.client"

echo "Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update your Cloud Build trigger with substitution variables"
echo "2. Run your Cloud Build to deploy with the new environment variables"
echo "3. Verify your Laravel app can access the environment variables"

set_substitution_vars
