---
id: forgot-password
title: "Request password reset"
description: "Send password reset link to user's email"
sidebar_label: "Request password reset"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Request password reset"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/auth/forgot-password"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Send password reset link to user's email

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["email"],"properties":{"email":{"type":"string","format":"email","description":"User email address"}},"title":"ForgotPasswordRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Password reset link sent","content":{"application/json":{"schema":{"type":"object","properties":{"status":{"type":"string","description":"Password reset status message"}},"title":"PasswordResetLinkResponse"}}}},"422":{"description":"Validation error","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Validation error message"},"errors":{"type":"object","additionalProperties":{"type":"array","items":{"type":"string"}},"description":"Field-specific validation errors"}},"title":"ValidationErrorResponse"}}}},"429":{"description":"Too many requests","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}}}}
>
  
</StatusCodes>


      