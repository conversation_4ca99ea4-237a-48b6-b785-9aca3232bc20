---
id: create-bookableslots-query
title: "createBookableslotsQuery"
description: "createBookableslotsQuery"
sidebar_label: "createBookableslotsQuery"
hide_title: true
hide_table_of_contents: true
api: eJztWN9v2zYQ/leIw542OXG8FV31UrRpO2Qo1mzxNqCGB5yli82GIlXy6NQV9L8PR/pX7DTLy97yYkvH4/Hj3X0fJXXAOA9QTuC1czc4M3RlHAeYFuDpc6TAr129grKDylkmy3KJbWt0haydPf0UnBVbqBbUoFzJPO2plqA1MkEBrQtcuZokLK9aghLc7BNVLGPeteRZU0iroBcg2s7T7doZvccVFKCZmn37t4OcI9Pc+dWec2Cv7RwKsNEY2SmU7CP1BbRY3eCcfvEuthf13hRtmebkoYBr5xvkbPpxBH0BrpX9o3n7hT1e1I+B+1C4I1BVDOwa8ufoz02cXWaMj4N3FK4vAOtaZ8SXe8m6RhOoANYsvnC+Tf9+O9yLb1vTsoNG2/dk57yA8kzSyUzeQgn/TPDV4OvHaXdWjPrJcPBimg1y9VK93LNMu1H/HRQHteqL3EH3FHG7Y3EYsG5IvD0FF331f6fp90h+dYcwb8YfQKYzfeEnTjxx4okTO07sHxff//BEjidyPJFjTY4+Q2idDbkTR8Oh/NUUKq9Tw0AJV7GqKIREpkBV9JpXUE46mBF68lBOpv1UGox8YplsBipPyLRZM8iaCQUUB9EfcGyIF06CSe0gFWoBJZxiq09n+7s5/byeEcgvyYeELnoDJSyY21CenhpXoVm4wGXXOs/9EY734qByAChgiV4nQJIPmZHzco3RSEGeDYdnksDicJ1rT2ExqGk5wFaf4Nfo6ZZmQTOFE0t8tOwbWpJxbUOWN4t/K2hEfmTQK8a5tvP/Cmj0kh4Z8dK7OlZysw063XXDlYhpTtWmJ7bdLEtKZZILlBuHYn3xbtPiv/49Th0mAv3H7tn/7RdsWkNHWjs5kM0d0w7VcXif9k2G04ckayi72ynGLnimN4yGo58Gw+eD0Yvx2bPy2Vk5+vlk+PzsI9yl9LAvQNtrl9Kx5uI7Sb06R69eXV5Io5EPOcfL0VHaxwsdxE+13i11TUGRrVunLQd17bziBana8W/EKsUV179GJ+qCvzWjQZt7o0IfCvlVVU6ips29iTMV4mwLZN8+W1egUGhrFQN5hZEXZHl9xqbF0Rh3G9JwUOxUS160TG01IqgQq4XC7KM8zXXgPJQCGzfXtlBGB96BzV4kpVCWbtfWmgztOe1NubMzk4Gr2Kahmhi1CWq2uru4jc2MfKECoa8Wm7Di1uANJWyNq8kUKgnXdqX7cnbksEtebOs8tjapwMgx5BLYiox5KHJCsSnkPTVQ2lYm1jLaYgi3ztfKUyAOaSo1qI1aktfXm6LB+oRsMD0dWUxczdqs7oizulfGt3RPbyCtQW0lZFKdbi3cE8BWC/HvvOoXkMV7WoDIs7h13QwD/elN34s5j5eT6U6V05lTwIKwTgdQBzckInCevxIMxgJG3E1MDziHXwyE3tvT5fLD1TjByl8amkx5j7fCZryFEmAjIUnikq0Dg3YecS6+OWY6jqPsdJuNA7FLSDcPXXa1h7DrssfY3ZDt5XTKG2K5h37a9/2/hyD7CQ==
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createBookableslotsQuery"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/bookableSlots/query"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createBookableslotsQuery

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["date","postcode"],"type":"object","properties":{"carBookings":{"type":"array","items":{"type":"object","properties":{"carCategory":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"optionalExtraIds":{"type":"array","items":{"type":"integer","format":"int32"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true}},"additionalProperties":false,"title":"CarBookingBookableSlot"},"nullable":true},"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"},"date":{"type":"string","format":"date-time"},"resourceId":{"type":"integer","format":"int32","nullable":true}},"additionalProperties":false,"title":"QueryBookableSlotsDTO"}},"text/json":{"schema":{"required":["date","postcode"],"type":"object","properties":{"carBookings":{"type":"array","items":{"type":"object","properties":{"carCategory":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"optionalExtraIds":{"type":"array","items":{"type":"integer","format":"int32"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true}},"additionalProperties":false,"title":"CarBookingBookableSlot"},"nullable":true},"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"},"date":{"type":"string","format":"date-time"},"resourceId":{"type":"integer","format":"int32","nullable":true}},"additionalProperties":false,"title":"QueryBookableSlotsDTO"}},"application/*+json":{"schema":{"required":["date","postcode"],"type":"object","properties":{"carBookings":{"type":"array","items":{"type":"object","properties":{"carCategory":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"optionalExtraIds":{"type":"array","items":{"type":"integer","format":"int32"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true}},"additionalProperties":false,"title":"CarBookingBookableSlot"},"nullable":true},"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"},"date":{"type":"string","format":"date-time"},"resourceId":{"type":"integer","format":"int32","nullable":true}},"additionalProperties":false,"title":"QueryBookableSlotsDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      