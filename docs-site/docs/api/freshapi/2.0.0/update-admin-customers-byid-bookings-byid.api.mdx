---
id: update-admin-customers-byid-bookings-byid
title: "updateAdminCustomersByidBookingsByid"
description: "updateAdminCustomersByidBookingsByid"
sidebar_label: "updateAdminCustomersByidBookingsByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVktz4zYM/iscnPpQ4jSdXnTLY3eaTqfN7LrtIeMDLMI2NxSpJSF7XY3++w5ISY7jbJtDp7MXWySBDw8CH9EB4zpC+QA3bWRfU7j2/tG49ZWujYNFAQ0GrIkpiFQHDmuCEqpB+k5DAcZBCQ3yBgoI9LE1gTSUHFoqIFYbqhHKDnjfiKZxTGsKUMDKhxo5b/14CX1fTPDL7MR/ir7I6hT52uu96FTeMTmWT2waaypk493sQ/RO9k7A/fIDVQwFNME3FNhQlFMTb8kSi1eT6NJ7S+gkKECtjQCjvX+it0IbqQA2bEXhvV9xhnnj2PD+dv57Umb6xF+XR09T9d33X5Froh4oNt7FbOvy4kL+NMUqmEZQRbetKooxGYtUtcHwPlX2kjBQgPJhIaUitlOMdxpKaBuNTKknxj6J13ujh2ZJ31A8s/RKpZp443Wu8UqKPNV6CTNszAxFezZ2W5x1h8brZ0OXxFk39UsPElXYju3aBgslbJibWM5m1ldoNz5y2TU+cH/i8q8ioDIAFLDFYHBpczZFI6dzha2Vzvrp4uIHyXvx3M4qUNycadqeYWPO8e820I6W0TDFc0d8YvaWtmR9U5Pj0fiXQFvkV4K+Z1wbt/43QGu29ErE++B1W8liAl0ciui9dEBO1VhKU4WLSRjoKlV8FiiGj7cjV/3y1zwVpnTVuwNZvfmEdSOVf9Q/QoG98OPKJ1NDc7yVsNQNBnV1fyeXSCFm/7eXJyHNNyaKnGqC3xpNUZHTjTeOo1r5oHhDSnv+jVglXBH98/Jc3fGXNGp0Oe8VhljIr6qQae2DoXFt26WK7XJy5On+WNSFQqdVGykobHlDjgfSScbRWr+L6Tgq9qqhIISvpraNKrbVRmGWUYHWJnI+SsDWr40rlDWRD85mKQqy42g37GpJ+EHoicpRZDY7rtomHWliNDaq5f7YuGvrJYVCRcJQbUZYEavxkZJvtddkC1UFwoOll3J2InBIXiIfORu2VGTkNuYrcBVZ+0/IyYvxIl+4A2VcZVstpw3GuPNBq0CROCZVqtFYtaVgVuOlQV9A4yPXmJ6L4Z3PFKkSR6qJJJUwoxppUr1ErlNnpfexsWicWEgN3g0M+gDYGJAnRUaZYppZIhRQHg0wY9rk4DB6LAoQrhSgrltipD+C7XvZ/thSkEdjcaDIxLbaRPnW04P1zOdp4IBv3g2TzLcKipdjGTbR7RMT21ZWUMAj7Y8HMGG2/9HyIUH9oi9gQ6jTs9kN5zfZ1NlcUA76JxOWcOf09t1fzW9+TjeRRzNpASgh4E6mPtxlB3yuTxFIex1YdOsW1yKbQdMM0cr1H8aLY7JNrr4YYtdlibl/JNf3U8Qsa4m27z8DRn7xxg==
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminCustomersByidBookingsByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/customers/{customerId}/bookings/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminCustomersByidBookingsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}},{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      