---
id: fresh-car-api
title: "Fresh Car API"
description: "This API provides endpoints for the dotNet FreshAPI V2. It provides endpoints for managing cars, car categories, car club subscriptions, car club bookings, and user authentication. It allows users to perform operations such as user registration and login, listing cars, registering new cars, deleting cars, listing car categories, looking up car details by registration number, searching cars by make and model, creating car club subscriptions, creating car club bookings, updating booking statuses, cancelling car club subscriptions, and managing user authentication including password resets and email verification."
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import ApiLogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: v2"}
>
</span>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Fresh Car API"}
>
</Heading>



This API provides endpoints for the dotNet FreshAPI V2. It provides endpoints for managing cars, car categories, car club subscriptions, car club bookings, and user authentication. It allows users to perform operations such as user registration and login, listing cars, registering new cars, deleting cars, listing car categories, looking up car details by registration number, searching cars by make and model, creating car club subscriptions, creating car club bookings, updating booking statuses, cancelling car club subscriptions, and managing user authentication including password resets and email verification.

<div
  style={{"marginBottom":"2rem"}}
>
  <Heading
    id={"authentication"}
    as={"h2"}
    className={"openapi-tabs__heading"}
    children={"Authentication"}
  >
  </Heading><SchemaTabs
    className={"openapi-tabs__security-schemes"}
  >
    <TabItem
      label={"HTTP: Bearer Auth"}
      value={"bearer"}
    >
      
      
      
      
      <div>
        <table>
          <tbody>
            <tr>
              <th>
                Security Scheme Type:
              </th><td>
                http
              </td>
            </tr><tr>
              <th>
                HTTP Authorization Scheme:
              </th><td>
                bearer
              </td>
            </tr><tr>
              <th>
                Bearer format:
              </th><td>
                JWT
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </TabItem>
  </SchemaTabs>
</div>
      