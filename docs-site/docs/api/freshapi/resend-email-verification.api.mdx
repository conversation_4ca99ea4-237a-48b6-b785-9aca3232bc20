---
id: resend-email-verification
title: "Resend email verification"
description: "Resend email verification notification to authenticated user"
sidebar_label: "Resend email verification"
hide_title: true
hide_table_of_contents: true
api: eJzlV9ty2zYQ/RUM8pB2hiJl59KGb67jdJRxY4+lJA+WJwHBFYkYBJgFYFvV6N87C1L3uMlM+xY/yJSwNxwszh4uuBeV4/k1Pwm+BuOVFF5Zw28SXoKTqNr4NedX4MCUDBqhNLsDVLPelBnrN1+8ZWITCUoWHCBPuG0Bo8Wo5DnHGOyMYn3YCsUTWmmtceB4vuDHwyH92y1k24FpZW6ZA+N5wqU1np7yBRdtq3uT7IsjtwV3soZG0JOft8BzbosvIMmxRarOqy5pKfwPWDkvfHBbds6jMhXfh22n2t5puVwmvAHnRAXfjzAOUoJzbOVAzl55TS4HCF718FGOZcKfD48OAXxvdk7o/0Puh7d0hmhxs6FkH/NH3E7KUtGj0AxihOiWcBO0FgUB4jHADj5kto/J8fE3MHGATGgEUc777v4ZkXl1iMzEWtYIM2cIXwM4734uVJYJdyADKj/n+fWCFyAQkOfXN8sbusS+tkRorXVxf8LXPOcZXbAsUmW2TZWDbarkFBnvAF0MHFDznNfety7PMm2l0LV1Pl+0Fv3yYG/nZMC6ADzhdwIVbSniSh7dQc5E0J7n/Gh4NIy0s5+nhDvQtm3A+HSG4GopMJU2DbcHGV9vTFd5D+OJVg1oUUkYHA1f/f7i5cvj589/ezZMIdDZD+7B+eMUg0lF22aHZOdFpUz1rwm+V+gl2jLIjnD7MDebUxxTc3Y4rc5y3UeUhI4lmvB8ZZD0D28sNoLgfPtxcpD17ccJoxlqUf3dsX0NogRkwdGGfA3sjxiFdeFTdvYgmlZDzp7u+OUru4W3t2CWTyPjKzOzsdK+U98QBuxUIDu5HFEDALqukGfpMB3yhD8MtK2iT4dgppoqW0EX19I2XppCyNsKbTDlqdUWec6fvIl/POFC+wk8+IOEy/3tf5rUytESq4VjBYBhfW9BSaIAodVCQsShtP4deBYjkseH44QJUzLlSEuwOXimDAmHkhXz6CG1orYTbevST1MzNU+esHFoGoHzqVlnbtHeqRIcA1O2Vhnv2Ix4Q5iuqaRAl9Ano8FXWVSw+q5DMTUuFOstbS2wwtpbZSrXVRniqNhRSykbeSa0tvcuLrup8Za1gDOLDVtLH8dckDUTnRFDqJTz3VKMrG2lTMK0cn5d7tR0ZkAMxwzc97soQcPGattnZ3O6K52FlpampgQvlHaE6056E5oCMGEOBMp6FZfMGnELsbrGlqATJhEEpZqaNT77uPUW3wIwtGW31v/Un+UpWucGF6gqZdgVOBtQAhvXIu76l9OLq/GvWwftQksc537IL/aAjIalbYQy60mW9tl3pe/UnGi91UJkrRD2jry/1ucCxR1oNhZG+tCkbGSkDmXX5x1rsHiNqaPpt29RRD41nz9/nppHaGBuAw46LugN+xEsZKR5IyJZTWpg56qqvaHC/kQbiMt22fP+/j71NeiVGV37tqfRZcK1kkCDbxP0r9HkIIhtwXRIpxarrHdyGdnSLAVs3MVs3E2BLb9K+ToUqbRNNiO2ARSmXJeyRU1FO5AWISu0LTI6sex8dHr2bnwWWQ2Fh4FWjfKHYoWag9ZZXCccFLHBzKLsaAiMCwisH1EkyAullZ/HDp8JRXQtKkgjvffTc8EpJM9fDhPe0rzgjTLBA1HnnOd8dMlEWSJpdIvd1R69Zvc1mN1XoYgwXfFNyBffjSjW71zSNoUynXSIuoQ0RyPM1mk9+o62P60WGwn3X1/s+tHp4cFnrRbK0DZjwyx6OXTNyZEnPKboZtUjkugm4SR6yGexKISD96iXS/r5awAkBXaz0TpRhCW8u0NRQ90CoXciJbQ+iiIdqLYDjboj3S4vxtTjscaNFNgb/TF8vyTMfCv4YtFZTOIFJaHWVREvLF/eLJfLfwCEsHCa
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Resend email verification"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/auth/email/verification-notification"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Resend email verification notification to authenticated user

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Verification link sent","content":{"application/json":{"schema":{"type":"object","properties":{"data":{"type":"object","properties":{"status":{"type":"string","description":"Verification status"}}},"message":{"type":"string","description":"Success message"}},"title":"EmailVerificationResponse"}}}},"401":{"description":"Unauthenticated","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}},"422":{"description":"User already verified","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}},"429":{"description":"Too many requests","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}}}}
>
  
</StatusCodes>


      