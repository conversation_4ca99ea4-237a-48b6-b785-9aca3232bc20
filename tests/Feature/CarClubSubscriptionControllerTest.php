<?php

namespace Tests\Feature;

use App\Models\Car;
use App\Models\CarCategory;
use App\Models\CarClubSubscription;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CarClubSubscriptionControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $testUser;
    protected Car $testCar;

    protected function setUp(): void
    {
        parent::setUp();

        $this->testUser = User::factory()->create();

        // Create or get an existing category to avoid unique constraint violations
        $category = CarCategory::firstOrCreate(
            ['name' => 'Test Category'],
            [
                'description' => 'Test category for testing',
                'price_multiplier' => 1.00,
                'is_active' => true,
            ]
        );

        $this->testCar = Car::factory()->create([
            'user_id' => $this->testUser->id,
            'category_id' => $category->id,
        ]);

        Sanctum::actingAs($this->testUser);
    }

    public function test_can_list_user_subscriptions()
    {
        // Create some subscriptions for the user
        CarClubSubscription::factory()->count(3)->create([
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
        ]);

        // Create a subscription for another user (should not be returned)
        $otherUser = User::factory()->create();
        $otherCategory = CarCategory::factory()->create();
        $otherCar = Car::factory()->create([
            'user_id' => $otherUser->id,
            'category_id' => $otherCategory->id,
        ]);
        CarClubSubscription::factory()->create([
            'user_id' => $otherUser->id,
            'car_id' => $otherCar->id,
        ]);

        $response = $this->getJson('/api/v3/car-club/subscriptions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'status',
                        'frequency',
                        'cleans',
                        'resourceId',
                        'packageGroupId',
                        'car',
                        'dates',
                        'actions',
                    ]
                ]
            ])
            ->assertJsonCount(3, 'data');
    }

    public function test_can_create_subscription()
    {
        $subscriptionData = [
            'customerCarID' => $this->testCar->id,
            'frequency' => 2,
            'cleans' => 1,
            'resourceId' => 123,
            'resourceName' => 'Test Resource',
            'category' => 'premium',
            'packageGroupId' => 456,
            'customerName' => 'John Doe',
        ];

        $response = $this->postJson('/api/v3/car-club/subscriptions', $subscriptionData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'status',
                    'frequency',
                    'cleans',
                    'resourceId',
                    'packageGroupId',
                    'car',
                ]
            ]);

        $this->assertDatabaseHas('car_club_subscriptions', [
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
            'frequency' => 2,
            'cleans' => 1,
            'resource_id' => 123,
            'package_group_id' => 456,
            'status' => CarClubSubscription::STATUS_ACTIVE,
        ]);
    }

    public function test_cannot_create_subscription_for_other_users_car()
    {
        $otherUser = User::factory()->create();
        $otherCategory = CarCategory::factory()->create();
        $otherCar = Car::factory()->create([
            'user_id' => $otherUser->id,
            'category_id' => $otherCategory->id,
        ]);

        $subscriptionData = [
            'customerCarID' => $otherCar->id,
            'frequency' => 2,
            'cleans' => 1,
            'resourceId' => 123,
            'packageGroupId' => 456,
        ];

        $response = $this->postJson('/api/v3/car-club/subscriptions', $subscriptionData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['customerCarID']);
    }

    public function test_can_cancel_subscription()
    {
        $subscription = CarClubSubscription::factory()->create([
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
            'status' => CarClubSubscription::STATUS_ACTIVE,
        ]);

        $cancellationData = [
            'cancellationReasonId' => 1,
            'cancellationReasonNotes' => 'No longer needed',
        ];

        $response = $this->deleteJson("/api/v3/car-club/subscriptions/{$subscription->id}", $cancellationData);

        $response->assertStatus(200);

        $subscription->refresh();
        $this->assertEquals(CarClubSubscription::STATUS_CANCELLED, $subscription->status);
        $this->assertEquals(1, $subscription->cancellation_reason_id);
        $this->assertEquals('No longer needed', $subscription->cancellation_reason_notes);
        $this->assertNotNull($subscription->cancelled_at);
    }

    public function test_can_pause_subscription()
    {
        $subscription = CarClubSubscription::factory()->create([
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
            'status' => CarClubSubscription::STATUS_ACTIVE,
        ]);

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$subscription->id}/pause");

        $response->assertStatus(200);

        $subscription->refresh();
        $this->assertEquals(CarClubSubscription::STATUS_PAUSED, $subscription->status);
        $this->assertNotNull($subscription->paused_at);
    }

    public function test_can_pause_subscription_with_reason_and_date()
    {
        $subscription = CarClubSubscription::factory()->create([
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
            'status' => CarClubSubscription::STATUS_ACTIVE,
        ]);

        $pauseData = [
            'reason' => 'Going on vacation',
            'pauseUntil' => now()->addWeeks(2)->toDateString(),
        ];

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$subscription->id}/pause", $pauseData);

        $response->assertStatus(200);

        $subscription->refresh();
        $this->assertEquals(CarClubSubscription::STATUS_PAUSED, $subscription->status);
        $this->assertEquals('Going on vacation', $subscription->pause_reason);
        $this->assertNotNull($subscription->pause_until);
        $this->assertNotNull($subscription->paused_at);
    }

    public function test_can_resume_subscription()
    {
        $subscription = CarClubSubscription::factory()->create([
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
            'status' => CarClubSubscription::STATUS_PAUSED,
        ]);

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$subscription->id}/resume");

        $response->assertStatus(200);

        $subscription->refresh();
        $this->assertEquals(CarClubSubscription::STATUS_ACTIVE, $subscription->status);
        $this->assertNotNull($subscription->resumed_at);
    }

    public function test_can_resume_subscription_with_reason_and_date()
    {
        $subscription = CarClubSubscription::factory()->create([
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
            'status' => CarClubSubscription::STATUS_PAUSED,
        ]);

        $resumeData = [
            'reason' => 'Back from vacation',
            'resumeDate' => now()->toDateString(),
        ];

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$subscription->id}/resume", $resumeData);

        $response->assertStatus(200);

        $subscription->refresh();
        $this->assertEquals(CarClubSubscription::STATUS_ACTIVE, $subscription->status);
        $this->assertEquals('Back from vacation', $subscription->resume_reason);
        $this->assertNotNull($subscription->resume_date);
        $this->assertNotNull($subscription->resumed_at);
    }

    public function test_cannot_access_other_users_subscriptions()
    {
        $otherUser = User::factory()->create();
        $otherCategory = CarCategory::factory()->create();
        $otherCar = Car::factory()->create([
            'user_id' => $otherUser->id,
            'category_id' => $otherCategory->id,
        ]);
        $otherSubscription = CarClubSubscription::factory()->create([
            'user_id' => $otherUser->id,
            'car_id' => $otherCar->id,
        ]);

        $response = $this->deleteJson("/api/v3/car-club/subscriptions/{$otherSubscription->id}");
        $response->assertStatus(404);

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$otherSubscription->id}/pause");
        $response->assertStatus(404);

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$otherSubscription->id}/resume");
        $response->assertStatus(404);
    }
}
