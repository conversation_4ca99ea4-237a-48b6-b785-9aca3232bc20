---
id: create-carclub-subscribe
title: "createCarclubSubscribe"
description: "createCarclubSubscribe"
sidebar_label: "createCarclubSubscribe"
hide_title: true
hide_table_of_contents: true
api: eJztWEtz2zYQ/iuYPbZMrLrTC2+p3HTcaRNP7LYHjw5LciUhBgEEWEhRNfzvmQUpUZLtRm2uvkjU4tsH9/EtxS0wLiKU9zDFMDWpglkBgT4livyzazZQbqF2lsmyXKL3RtfI2tmLj9FZkcV6SS3KFW88QQmu+kg1QwE+OE+BNcVsJkV2LYUphuurA7i2TAsKUMDchRa5F/14CV0B8xyKrTdn4mtDaOOZ4EDRpVDTdfMfFd5hSwcqkYO2CyjAJmOwMgQlh0QSDjItXNicBfZYP+CCfg0u+bND2iX17JA8blqyfEWM2sSvV03HG9y8c+sDZOWcpBlGa38QL11zFPRXI7jOTfV/dKZGk+VbqgPxWdrSRDpQfFNL4z51J10B2DRajtHcHCRgjiZSAaxZ7MHNUfau7t6frzrM1wda6MgUdrpMn/lllF5G6WWUvnmUDpfTd9+/zNTLTL3M1LfNVNd3lXc29um+nEzkq6FYB+37cOE21TXFmH1FqlPQvIHyfgsVYaAA5f2smxUgrvNwSlKhDoRMUwy1SdVtqsRgRVCc2H4W1uaaQgnexdwWyEso4QK9vqh7+EU8wEcKKwoxx5WCgRKWzD6WFxfG1WiWLnK59S5w9yiG3wWgegNQwAqDlvrlhIhGn5E5JiO9/9Nk8oOkrjj1Mw8Ul68aWr1Cr1/jPynQmqqomeJrS/zI7RWtyDgvBd05f85oQj7T6C3jQtvF1wwavaIzLd4E16TcuXujs7EPboV9+1TtumHf3uJSKpMh0u49oBgu3u7Y5Le/73JvCaN/GP+a/PIZW2/oCe6eHDHzZOTdyTGrTk45cxzSkRpH2SkDTk757RB6SmMjUcnUP0FNj5RHBnrm6JhoRtApn2Sa6QrQdu5y+oeRfyulVlMM6s3NtTQ2hdjXdHX5qMx3Sx0Fp3xwK91QVGQb77TlqOYuKF6Sahy/I1bZrkD/unytrvk5jRZt34s1hljIpxqyrmn326RKDVOcAzmUV849aLuIhULbqBQpKEy8JMvDQ0B2jsa4dczHUbFTnoKsKbVno6hiqpcKe4wKmQD7o2zYuIW2hTI68hhsGGhSJJbWg7QhQwegA5WjOzN94Cr5fNT0TaKqzbFzm9qKQqEiYaiXO7MCa/GBcmyta8gUKpPk3tNTOXsEGJOXfNOfDSIVGTnFvgS2JmP+zXKOYlfIJ2qgtK1NauTUY4xrFxoVKBLHrEotaqNWFPR8V7S8t13kFvMqtP1k9XtADYtAPbsw9uSS/1d5gzo/CGSO2w5L4h7Q6zzj2ZhQ0N7crABZBQLabiuM9GcwXSfiT4mEDu5n4wbIm62AJWGT19wWHkgYY9q/MHl1J6EI3CQJ6dHLEyHK/R67eX97J9w3vHSR0kIJAdcy0biGEkB2aM67ALJsCwbtIuFCsL3NvPGT3Of4HHFMrDnS4Qjt5iDC7bZH3LkHsp1swv6GWH5DN+u67gsQxmeL
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createCarclubSubscribe"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/carclub/subscribe"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createCarclubSubscribe

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"customerCarID":{"type":"integer","format":"int32"},"frequency":{"type":"integer","format":"int32"},"cleans":{"type":"integer","format":"int32"},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"customerName":{"type":"string","nullable":true},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CarClubRegisterDTO"}},"text/json":{"schema":{"type":"object","properties":{"customerCarID":{"type":"integer","format":"int32"},"frequency":{"type":"integer","format":"int32"},"cleans":{"type":"integer","format":"int32"},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"customerName":{"type":"string","nullable":true},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CarClubRegisterDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"customerCarID":{"type":"integer","format":"int32"},"frequency":{"type":"integer","format":"int32"},"cleans":{"type":"integer","format":"int32"},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"customerName":{"type":"string","nullable":true},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CarClubRegisterDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      