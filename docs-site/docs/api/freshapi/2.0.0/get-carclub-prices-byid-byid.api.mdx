---
id: get-carclub-prices-byid-byid
title: "getCarclubPricesByidByid"
description: "getCarclubPricesByidByid"
sidebar_label: "getCarclubPricesByidByid"
hide_title: true
hide_table_of_contents: true
api: eJy9VUtvJDUQ/itWnUDy7oQgLn2DwKIghCIS2EM0h2p3TbcVt+21yxOGVv93VO6eyWSTXZYLh3m4Hl+V6/F5AsY+Q3MPV5iuXGlhqyFiwpGYkigm8DgSNBDRPGBP1x1osL4KeAANiT4Um6iDhlMhDdkMNCI0E/AhiqP1TD0l0LALaUReRN9ewjzrE7rBdIVMfUiH/4qfOVnfwzxvxTjH4DNl0V9eXMhPR9kkG9kGQb0txlDOEhwymZIsH+o1W8JECZr7rQCFSAnF5bqDBnriK0zGlfYmWUP5h4Pt5AP6I/TPGI7EQ1jBQC+3a2CD0W7M4rKJ1WcznUo9b6azwswgKaf9sTElOWhgYI652WxcMOiGkLmZYkg8v8jtVzFQCwBo2GOy2LqlVOKx1GqHxUmLvru4+Abm2qLncXaJ8vCmo/0bjPYt/l0SPVKbLVN+6+vVnof9kfbkQhzJ8zH4p0AL8heC3jL21vf/Bujsnr4Q8SaFrhg5nEC3TxNyK0O3lOo4J6f5k5CwzqWcVwO9/nl3HPpf3t/VqbN+F6q7ZSf27yRVdYVJfX9zLY2hlJec9pcv0rwbbBY7FVPY246yIt/FYD1ntQtJ8UCqC/wbsaq4Yvrn5Vt1zZ/yGNEvtTSYspZvZZaBs3Q8u9KqXNpTIufyNoQH6/usFfpOlUxJYeGBPFtTN6gGR+fCY67qrDioSEnYQJ32LKtczKBwsVGJept5UVVgF3rrtXI281OyixXJ/itPj6u0I0dnRmcuz27mlsRViVXVEaN1WbWH58F9GVtKWmXCZIYjrJiN+EA1tzF05LQyifAp0ms1e2HwVLwSu0W3ilRm5JKXFnhDzn0OuWZxbOQrPVDWG1c60UbM+TGkTiXKxLm60ojWqT0luzs2DWYNMWQe0cuwrjTdE6uV4NTCcEroTb1GhqcFYfqLN9Gh9QJa93Ra+e8eMFrQsDKg8GJFBQ3N+YPTnL8PWw3Cc+I9TS1m+iO5eRbxh0JJ2Hz7RG+VKTub5X8HzQ5dpheJmuCZvCzpV7+vz83XCvTrF1iF6A+VRV2RE2h4oMOzZ1JI6X8MfF6heTufPTg//3QHGmQgzpryEUvVMr0aYJoWi7vwQH6eT/FYzhJpnv8BwcTmlQ==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getCarclubPricesByidByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/carclub/prices/{packageId}/{carCategory}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getCarclubPricesByidByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"packageId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}},{"name":"carCategory","in":"path","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      