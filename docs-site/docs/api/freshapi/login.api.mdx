---
id: login
title: "Authenticate user"
description: "Login with email and password to receive authentication token"
sidebar_label: "Authenticate user"
hide_title: true
hide_table_of_contents: true
api: eJzFWFtz27YS/isY5KHtDHWJm+ac8um4jtNxx609ltM8WB4HBFckahBAcbGiavjfzyxASpSl2O5lpnqwJWD328Xu8tsF19SzytH8hh4HX4PygjMvtKK3GS3BcStM/JnTc10JRZbC1wQaJiRhqiSGObfUtiReEwscxAMQtgNEvL4HRTOqDdi4clbSnEpEoxm18HsA53/Q5Yrm6/hTWChp7m2AjHKtPCiPW8wY2WFOfnPo0po6XkPD8JtfGaA51cVvwH2Hm4BuaHSXZrR3Fs9mLPrjBTjUThJbGOetUBXN6ELbhnmabzB2Y/LBge2jUZYWnKPtwM5TgBuhg5ib3RaP0kBTgB3AFVpLYGpP96oTJQFB+hiXsGBBepovmHTQthn1wkvoc3qVUkDbto3WnNHKpbgcTaf471AhuMA5OLcIGJW/nKbdNJTMRykm5cWC5jfPyYthiIXyUIEdxlgo//bN4QCfvcPQ/rN5jwt3D2DFQkB5x/yTyCXzMPKiAZpRFaRkBaYklf2utdNoKOH2D5VowHnWGLS7ENb5O8UaOGTwaex4kghAIkCbUcn+Hhrqb8AKYX19h2d9NhgvQ4+AJMq3GeUWmP9zsd7FPOZcB+VJBNqPbJDyi6E4FMgg5eboQgkvmHQv1N2Io2rDqr8afmP1QkggEYN8uDrfAN75OjSF+kLJ/2noDVpvRKtCM1tC+QKm+liDr3uiqpkjXDdGgoeSdDDo1ZCr0IN3SBBt9hwvpJbzbNiPDzWqxIHb5pFWb9vbNqMNOPeFxOwizxI5kl7hAOcmlu1I983R0T7PnqkHJkWJpVmim0w6oi2Ji8ljsFbbf45+X3y8Xx+5sD1nRuOCO2SMlaVAHSYvd7OV5Ji1bEUzKjw0B54ZDNOuF+8FyHLkDHDkxb24uJ2ob10+xc29+H+/H/9rrUnD1Cr1UcK8h8Z49y/E+/RxkPtG+Ux9bwLepSmqPX7Sh1Haj02bUQc8WOFXNL+5xWfA1xonOKNdPBLzNc3pBKe+ST9xOLAPgFVws6bBSprT2nvj8slEas5krZ3P10Zb3+45fY4CJAHQjD4wK9DXGDDUSHnqBhr6evp6in5mj+2U8ABSmwaUHy8suJozO+Z6HO73LL7bivZ29/GYESPcFBxGr6ff//e7t2+P3rz5z7fTMQRM6mgJzh+NbVBjZsxknxE8q4SqnjTwnKOXVpeBxwrvYW636Zlh1aU4FcDszryIRjAtUQRZOQlk3Zf3fZ/86eP1ntWfPl4TZEptxR/p8aqBlZG68UC+BvJDRCEJfkxOPzPk8px8taOX93LryKntV/EBxYfmansJ6HQHQznFFvE/SOtjrpvhJD+o/e2YnMZcbMALHYPQVfd7DC85YZYcX55hbYF16YzfjqfjKc3o55HUVdRJyZmIppr0WYl7YxONFYzfV1YHVZ5oqS3N6av38YM8J/01fPZ7BvcI7O66Fg63YgMsABTpyha6C5WRjEMMcan9L+BJRESNX4+yeAETjijtyQo8EQq7aUmKVdTgUmBFM2Pc+G6u5urVKzILTcPsaq42lo3VD6IER0CVRgvlHVkg1zCV6pUz6zL8SzjzUGkroP8tQzFXLhSbIw02SKH1vVCVS17GLr97LxyTM0+YlHrp4rabK6+JAYtDG9lcFh1eNGrCkhCxUAnn01ZEjoyTESmc37g7V0kMsDKIgmV3ihIkbKWGOjuHk8l1EgxuzVUJngnpMK475lXAcsuIA2Z53eOiWMPuIXrX6BJk1g2XqpqrTXwex62TOBTAYMq01y11uTyx2rnRhRXYnK7A6WA5kFnN4qm/Prm4mn0zSLQLBunTvUgv1gCPgqVumFCku6e7cWd9d3aaq2MpByXUDVCPXwUkxjhnlj2AJDOmuA/NmJwpLkOZ6jwRUprFsKJx7RD75HP16dOnufoCw6x0sKNEM51g17YZjx0kzfT0ugZyLqraK3TsR6sD0uQuMS+Xy7GvQfZi+NibjqHxtiQ4YLPcgv58dr0Hog2oFOmxttWkU3ITlMX+C7ZxF4tZajADvUr4OhTIepMFsg1YpsqNKwNqKsyIawuTQupighmbnJ+dnP4yO42sZvH+I0Uj/P6Yg8WB+yTuYxwEssFCW55oCJQLFkjX/YjzrBBS+FWs8AUT2AlYBePhm4Y1tfHK93aaUYOcTBuhQrzjFSucbS/7izOOtCHdyMmyBjWsGYjvP9JQsYH87llE9CtdzrluCqHSC604y+DQ0jA1yNagkCE68rgBrrfj3t9+B9a1Yw+f/cRIJhSeL1bKuhukbiiq0v7UtxnFYQnX1+uCOfhgZdvi8u8BbDeS9TMS/mozmh6QOHvdA4bmJPk/ukbrKC5DHLgfj644lCSNY87B+Cdlh6Pg5cUMS77oXuIh69GcWrbEtsyWNKfx/V/kupTIJf6TTFUhDsA0YeLn/9OyPUE=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Authenticate user"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/auth/login"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Login with email and password to receive authentication token

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["email","password"],"properties":{"email":{"type":"string","format":"email","description":"User email address"},"password":{"type":"string","format":"password","description":"User password"},"remember":{"type":"boolean","description":"Remember user login","default":false}},"title":"LoginRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Login successful","content":{"application/json":{"schema":{"type":"object","properties":{"data":{"allOf":[{"type":"object","properties":{"id":{"type":"integer","format":"int64","description":"User ID"},"email":{"type":"string","format":"email","description":"User email address"},"email_verified_at":{"type":"string","format":"date-time","nullable":true,"description":"Email verification timestamp"},"first_name":{"type":"string","nullable":true,"description":"User first name"},"last_name":{"type":"string","nullable":true,"description":"User last name"},"birth_date":{"type":"string","format":"date","nullable":true,"description":"User birth date"},"created_at":{"type":"string","format":"date-time","description":"Account creation timestamp"},"full_name":{"type":"string","description":"User full name"},"initials":{"type":"string","description":"User initials"},"image":{"type":"string","nullable":true,"description":"User profile image URL"},"image_thumbnail":{"type":"string","nullable":true,"description":"User profile image thumbnail URL"},"onboarded":{"type":"boolean","description":"Whether user has completed onboarding"}},"title":"UserData"},{"type":"object","properties":{"token":{"type":"string","description":"Authentication token"}},"required":["token"]}]},"message":{"type":"string","description":"Success message"}},"title":"LoginResponse"}}}},"422":{"description":"Invalid credentials or validation error","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Validation error message"},"errors":{"type":"object","additionalProperties":{"type":"array","items":{"type":"string"}},"description":"Field-specific validation errors"}},"title":"ValidationErrorResponse"}}}},"429":{"description":"Too many login attempts","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}}}}
>
  
</StatusCodes>


      