---
id: register-account
title: "registerAccount"
description: "registerAccount"
sidebar_label: "registerAccount"
hide_title: true
hide_table_of_contents: true
api: eJztWUtvGzcQ/ivEHNu1Lck10u7NeQEpgtiI1RaooQO1O5IYc8kNOZSjLvTfi+E+JTl+5JSDLvbucF78ZvgNsarAlugkKWs+5JCCw6XyhO4yy2wwBAmQXHpIb6GVzBJw+DWgp9c230BaQWYNoSF+lGWpVRbdnX3x1rDMZyssJD+xnXKYszuZ5w69hySay4w+hWKODhLAQioNCSyU8/RJFggJaNk9ltL7e+tyToQ2JUIKdv4FM861dLwbUuhjXsGTLdDxxqpWVxnCZYyzsK6QVIvOJ5CACVrLuUZIyQXcJkCK+K2CQn77iGZJK0jHoy6sJ6fM8iHDPvVd44tRAoUyna99V9vBTl9suYvji81r2Nnscb1cEl4tXivHCtUBFh2qrHdCKtbsAKC2NJ9xgQ5Nhn3WT4Lbds7DDfVRGRxzK1hPmc05Otl783S3NPY/3Cw78fd65hnwD8wnD5k/FxZ2cP5DDiJML26bDue9kJNn9Gsw5CKDoAkFpLejZNyX6RHsm3MJbxoP23r7imlH6utBWRdSexwYNG13WWP1dnrFiXhpFCmP+bv2CDyOFROgzK+M3nTYdaT0xPkZbKYzqY/uQrnieuDlabuDEj4Xhc8Nx8ftc+HxGx3Z+sjWR7Y+svWRrX92th5esX/59UjbR9o+0vaRto+0/VPTNps59KU1vj4Ik9GI/+XoM6dK9gYp3IQs40PH2h6z4BRtIL2tYI7S8cG9nW1nyZ7R4RebAmll+WMOt00kc4YJzmSpzhqtszY94Ehujc7HQMFpSGFFVPr07EzbTOqV9ZRWpXW0hf3YH1lB1A4ggbV0iqGKO2SLeosLGTSDejEajRmLZD/OwqFfneS4PpGlOpX/BYf3OPeK0J8apIOwb3GN2pYFGmqDf89pkPRMpzckl8osn3Ko1Rqf6fHa2Txk/NI5nfWFveGJXUPVlrdrSA7JlYkqkLYKSfPwvm3TP/+ZxmbhW8Dn/qPcu2+yKOuhOhzPo747+47vh2gv7MdjL9sbfP1CM9J6wc7sgslo8tvJ6NXJ5I/p+CK9GKeT309Hr8b/wiPzqfc1GEOD0THaHwQHBg3DPyg/H8prPu7fe6Ydbr3h0NEOCe1gs8ssAwJWZmFjZRvk33MXiTfSicvrD3xm0Pm6XdaTgw6arpRnPVE6u1Y5eoEmL60y5MXCOkErFLmlT0gi+mXVvyen4gN9z6KQpm7zTDqf8F+RScKldQrbdx3mwod5l8hQPrf2TpmlT4Q0uQgenZCBVmiouZPG4FJre+/jshdkRYmOqVV035q98CFbCVnriJrD6qXoWNulMonQylOfbMt0LDF430hz1DhQGpjs7EzXiYtQxqUcSSrtxXyzG9zEFkyER+myVeuW1Qp5hzG3wuaoE5E5lH2khzA7UOjBC2VerzUi4UlS8HUJTIZaP+Y5ZtEW8oEaCGUyHXJebdtVOPRIPprGEyvW6NSiLVp7yShkvJ2Y+vC3gIt+tuz0Z8dX8QtSqaUy7CjSZtWMnVuQpYIEehfd6JklwMOFdapqLj3+5fR2y+KvAfm83c76mRKHXwIrlHmchBXc4SZeT+KPDydTzoTVdeCMDn6IYOrtBuP11c2U2bT5AaOoD7yT95DEvylAAraGO16BWVaBlmYZ5JJ1a5/xNhB27uh7VB0zbZak2QwyrKpaY2rv0Gx5ttYbIn6H7Wy73f4PxpXXjA==
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"registerAccount"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/Register"}
  context={"endpoint"}
>
  
</MethodEndpoint>



registerAccount

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["address","contactNumber","email","firstName","lastName","password"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true}},"additionalProperties":false,"title":"RegisterDTO"}},"text/json":{"schema":{"required":["address","contactNumber","email","firstName","lastName","password"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true}},"additionalProperties":false,"title":"RegisterDTO"}},"application/*+json":{"schema":{"required":["address","contactNumber","email","firstName","lastName","password"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true}},"additionalProperties":false,"title":"RegisterDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      