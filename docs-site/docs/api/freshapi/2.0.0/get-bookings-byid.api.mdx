---
id: get-bookings-byid
title: "getBookingsByid"
description: "getBookingsByid"
sidebar_label: "getBookingsByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVU2P4zYM/SsCTy2gnUyn6MW3brtbTFEUi860PQxyYGzGFkaWtCKVaRr4vxf0Rz52st29JDb5+PhEkfQBBFuG6gnexvjsQgtrCwkz9iSU1XGAgD1BBZsJcN+ABReggoTSgYVMH4vL1EAluZAFrjvqEaoDyD5poAtCLWWwsI25R5lM39/BMKw1nFMMTKwRd7e3+tcQ19klcVHzPJS6JmYYBgtMdclO9qOwDWGmDNXTWolioowact9ABS3JfCJ+u3eq+ZL0tb8n6eIcCnY6XQUrTG41H51Xh2MRBlAxebcUqWQPFXQiiavVyscafRdZqkOKWYZX6X9TgJkIwMIOs8ONn4qgEVMVtli8luuH29vvYBgG+2mebSbu3jS0e4PJ3eC/JdMLbdgJ8U0Yj3GZ9mfakY+ppyBL8s+RFpSvJH0QbF1ov0To3Y6+kvFDjk2p9eVIuj7d/YM22FSqpQOOvaYpYe7BsWkngJ0f3i8N+Ovfj2M/ubCNY7gTr/j3KtX8hNn8+OFeL4YyT5p2d69kPnaOFWdSjjvXEBsKTYouCJttzEY6Mk2U30nMyKvQv+5uzL18LqLHMNWyxsxWf02NQm3MjpZ3XzaGy+Yo5Ny+NKo1GBpTmLLBIh0FcfU4G2Ny9D6+8OhmI9EkyjqZ5jhBbLjUncEJYzK1jmVyjcQ+ti5Y4x3LSeyEoqyWQC+ztSFPZ6CzkIuT+Um4KWl0NSToPJvN/jJ5KP2GsjVMmOtuoVVYj880autjQ96aOhOeMl2r2SvAqXglNZNvNhkWlMLTFYSavP8/5lHFcpFX7sC4UPvSqDch80vMjcnEJDyGUo/Omx1lt10uDQYLKbL0GLRZ54Xckphlh5lrS+44FUL/yCp5dEGZxuE8zAvuCTA5nY+ZCCxUp02/tqBbTGGHwwaZ/sx+GNT8sVDWLbw+La9xDzaO9bmBaoue6ZWiOgahoCP4zR/zh+NbA/a60tmIYT/uSF/0DSw80/7igzSsh7P9/cu7R7CgNT8rwSeLYNR6lf5wmBCP8ZnCMByzib5rpmH4D0LLieI=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getBookingsByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bookings/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getBookingsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      