<?php

use App\Services\FreshcarApi\FreshCarApiClient;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

beforeEach(function () {
    $this->client = new FreshCarApiClient('https://api.freshcar.test');
});

describe('emailAvailable', function () {
    test('returns true when email is available', function () {
        Http::fake([
            'https://api.freshcar.test/api/Account/EmailAvailable' => Http::response([
                'message' => null,
                'payload' => true,
                'statusCode' => 200
            ], Response::HTTP_OK)
        ]);

        $result = $this->client->emailAvailable('<EMAIL>');

        expect($result)->toBeTrue();

        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.freshcar.test/api/Account/EmailAvailable'
                && $request['email'] === '<EMAIL>';
        });
    });

    test('returns false when email is not available (exists)', function () {
        Http::fake([
            'https://api.freshcar.test/api/Account/EmailAvailable' => Http::response([
                'message' => null,
                'payload' => false,
                'statusCode' => 200
            ], Response::HTTP_OK)
        ]);

        $result = $this->client->emailAvailable('<EMAIL>');

        expect($result)->toBeFalse();
    });

    test('returns false when payload is missing', function () {
        Http::fake([
            'https://api.freshcar.test/api/Account/EmailAvailable' => Http::response([
                'message' => null,
                'statusCode' => 200
            ], Response::HTTP_OK)
        ]);

        $result = $this->client->emailAvailable('<EMAIL>');

        expect($result)->toBeFalse();
    });

    test('throws exception for HTTP errors', function () {
        Http::fake([
            'https://api.freshcar.test/api/Account/EmailAvailable' => Http::response([], Response::HTTP_INTERNAL_SERVER_ERROR)
        ]);

        expect(fn () => $this->client->emailAvailable('<EMAIL>'))
            ->toThrow(RequestException::class);
    });
});

describe('newCustomerBooking', function () {
    test('successfully creates new customer booking', function () {
        $bookingData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Temp1234!',
            'bookingReferenceNumber' => 'GHL-********'
        ];

        $expectedResponse = [
            'bookingId' => 123,
            'status' => 'created'
        ];

        Http::fake([
            'https://api.freshcar.test/api/newcustomerbookings' => Http::response($expectedResponse, Response::HTTP_OK)
        ]);

        $result = $this->client->newCustomerBooking($bookingData);

        expect($result)->toBe($expectedResponse);

        Http::assertSent(function ($request) use ($bookingData) {
            return $request->url() === 'https://api.freshcar.test/api/newcustomerbookings'
                && $request->data() === $bookingData;
        });
    });

    test('throws exception on API error', function () {
        Http::fake([
            'https://api.freshcar.test/api/newcustomerbookings' => Http::response([], Response::HTTP_BAD_REQUEST)
        ]);

        expect(fn () => $this->client->newCustomerBooking([]))
            ->toThrow(RequestException::class);
    });
});

describe('previousCustomerBooking', function () {
    test('successfully creates previous customer booking', function () {
        $bookingData = [
            'email' => '<EMAIL>',
            'bookingReferenceNumber' => 'GHL-87654321'
        ];

        $expectedResponse = [
            'bookingId' => 456,
            'status' => 'created'
        ];

        Http::fake([
            'https://api.freshcar.test/api/bookings' => Http::response($expectedResponse, Response::HTTP_OK)
        ]);

        $result = $this->client->previousCustomerBooking($bookingData);

        expect($result)->toBe($expectedResponse);

        Http::assertSent(function ($request) use ($bookingData) {
            return $request->url() === 'https://api.freshcar.test/api/bookings'
                && $request->data() === $bookingData;
        });
    });

    test('throws exception on API error', function () {
        Http::fake([
            'https://api.freshcar.test/api/bookings' => Http::response([], Response::HTTP_UNAUTHORIZED)
        ]);

        expect(fn () => $this->client->previousCustomerBooking([]))
            ->toThrow(RequestException::class);
    });
});

test('uses correct headers for all requests', function () {
    Http::fake();

    $this->client->emailAvailable('<EMAIL>');

    Http::assertSent(function ($request) {
        return $request->hasHeader('Accept', 'application/json')
            && $request->hasHeader('Content-Type', 'application/json');
    });
});
