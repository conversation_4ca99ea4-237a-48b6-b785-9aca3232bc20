/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
    --ifm-color-primary: #3d98a1;
    --ifm-color-primary-dark: #37898f;
    --ifm-color-primary-darker: #348187;
    --ifm-color-primary-darkest: #2b6a6f;
    --ifm-color-primary-light: #43a7b1;
    --ifm-color-primary-lighter: #4aafb9;
    --ifm-color-primary-lightest: #62bbc4;
    --ifm-code-font-size: 95%;
    --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme="dark"] {
    --ifm-color-primary: #55b0b9;
    --ifm-color-primary-dark: #45a5ae;
    --ifm-color-primary-darker: #3f9ba3;
    --ifm-color-primary-darkest: #348187;
    --ifm-color-primary-light: #68b8c0;
    --ifm-color-primary-lighter: #77bfc6;
    --ifm-color-primary-lightest: #94cdd2;
    --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

.api-method > .menu__link,
.schema > .menu__link {
    align-items: center;
    justify-content: start;
}

.api-method > .menu__link::before,
.schema > .menu__link::before {
    width: 55px;
    height: 20px;
    font-size: 12px;
    line-height: 20px;
    text-transform: uppercase;
    font-weight: 600;
    border-radius: 0.25rem;
    border: 1px solid;
    margin-right: var(--ifm-spacing-horizontal);
    text-align: center;
    flex-shrink: 0;
    border-color: transparent;
    color: white;
}

.get > .menu__link::before {
    content: "get";
    background-color: var(--ifm-color-primary);
}

.post > .menu__link::before {
    content: "post";
    background-color: var(--openapi-code-green);
}

.delete > .menu__link::before {
    content: "del";
    background-color: var(--openapi-code-red);
}

.put > .menu__link::before {
    content: "put";
    background-color: var(--openapi-code-blue);
}

.patch > .menu__link::before {
    content: "patch";
    background-color: var(--openapi-code-orange);
}

.head > .menu__link::before {
    content: "head";
    background-color: var(--ifm-color-secondary-darkest);
}

.event > .menu__link::before {
    content: "event";
    background-color: var(--ifm-color-secondary-darkest);
}

.schema > .menu__link::before {
    content: "schema";
    background-color: var(--ifm-color-secondary-darkest);
}
