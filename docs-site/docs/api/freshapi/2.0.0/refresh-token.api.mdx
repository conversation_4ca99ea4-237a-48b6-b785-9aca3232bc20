---
id: refresh-token
title: "refreshToken"
description: "refreshToken"
sidebar_label: "refreshToken"
hide_title: true
hide_table_of_contents: true
api: eJzlVktv3DYQ/ivEHFva6xjoRaemSQO4CBoj3rYHYw+z0qxEmyIZcribraD/XgylfdhOmtxz0YP85j3zkQP4QBHZeHfTQAWRNpFSt/SP5EADY5uguofXde2zY1hpiPQpU+LffLOHaoDaOybH8okhWFMXXYuH5J2spbqjHuVL5EykRtRRj8aChocdT5ZWGngfCCrw6weqGTSEKJ6xoSTSk0Q1QG/ce3Itd1C9Ogoljsa1oGHjY48M1Ywfz0x8Q3YcNWDTGPEe7e2Z8Q3aRBrYsBX8x7MMvV1+KIJMn/kHC/m82D/9/CPFLqKRUvAuTc5eX13Jq6FURxNEI1Rwl+uaUiqGEtU5Gt5DdT/AmjBShOp+Na70M6Fn09cTd16mMvhUcoQSCywwmMU8kYuPT0USxS3FVCzlaKGCjjmkarGwvkbb+cTVEHzkEZ4bfy8ANSkADVuMBtd2ClEkphg3mK1k/Jerq1eSDP3cTnHnoqHtBQZzif/mSDtaJ8OULh3xC7NvaUvWh54cH4x/TWlG/k6ld4ytce23FFqzpe/UeBt9k2v5OSpdnSp7J30/pepQ3+HQcWJSKlMgUB0Aev54d+jhP/5Zlm6RWfp4ItnfP2MfpBWPUwE5UfyVpvXL2vfno3XqcA3GbXxxZO7ldxK0eoNRvb69kRJTTFN02+sXAS87kwSnQvRb01BS5JrgjeOkNj4q7kg1nv8kVkWvQP++vlQ3/DWJHt1UlRpj0vJUNTK1Pho6/Nu8Vimvj46cr6+9fzSuTVqha5QkQWHmjhzPRFSMo7V+l8p2UuxVoCg0oY7nXFIp153CCaMitSbxtFUUW98ap5U1iU/OTiiSzCpHu3m1IUtnoDORJ5HZyXGVQ9lqiNHYpNb7p8Zd7tcUtUqEse4OagXW4yMV33rfkNWqjoQnS1/K2QvAKXk5NNPevKQSI+c0lcDVZO3/aS5eHAr5hRoo42qbG9kNmNLOx0ZFSsSpiJYWVluKZnMomnSqEFyPhbUdlimZqVAdiO1Jcx5nqxy8waJxoqWM+DDT5D1gMKCPVxf9hMrlCBIyFNwwrDHRX9GOoyx/yhSFqlcnDixsraEjbAp1D/BIe6jgzXT5uViKNwK3Wbx6cRESqjiS+e2Hu6VM/3yBkpJKvLgDXZ4VgAY/5bucpLI2gEXXZmwFO+ksR1iWUI/peEYtxdN5C93+zMNhmBAlF6OcBVNAXHIzrsZx/A+4aI1m
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"refreshToken"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/RefreshToken"}
  context={"endpoint"}
>
  
</MethodEndpoint>



refreshToken

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["email","jwtToken"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"jwtToken":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"RefreshTokenDTO"}},"text/json":{"schema":{"required":["email","jwtToken"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"jwtToken":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"RefreshTokenDTO"}},"application/*+json":{"schema":{"required":["email","jwtToken"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"jwtToken":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"RefreshTokenDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      