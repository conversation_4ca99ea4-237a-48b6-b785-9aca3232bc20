<?php

namespace Database\Factories;

use App\Models\CarCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class CarCategoryFactory extends Factory
{
    protected $model = CarCategory::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement(['Small', 'Medium', 'Large', 'SUV', 'Luxury', 'Compact', 'Executive', 'Sports', 'Electric', 'Hybrid', 'Van', 'Truck', 'Convertible', 'Coupe', 'Hatchback']),
            'description' => $this->faker->sentence(),
            'price_multiplier' => $this->faker->randomFloat(2, 1.00, 3.00),
            'is_active' => true,
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
