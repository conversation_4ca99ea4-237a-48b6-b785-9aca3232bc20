---
id: confirm-email
title: "confirmEmail"
description: "confirmEmail"
sidebar_label: "confirmEmail"
hide_title: true
hide_table_of_contents: true
api: eJzdVkuP2zYQ/ivEHFvterNAL7qlmwTYImgWXbc9LHwYS2OJWYpkOKQdV9B/L4aS/Nq8zrnYEvnNNy/OR/XgPAWM2tn7GkqonN3o0L3tUBsoIGLDUD7B66pyyUZYFRDoUyKOv7t6D2UvBpFslEf03ugqcy0+srOyxlVLHcqT2OlAtdBVriYoIDGF+1pI494TlODWH6mKUIAPElbUxGI64coeOm3fk21iC+WrgxXHoG0DQzHyfg82FIB1rSVMNA8njjZomAqIOhrB352U4s3yQzaM9Dn+rLmdtu+XX3/KJMU0EHtneQzs9uZG/mriKmgvjFDCY6oqYs6OmKoUdNxD+dTDmjBQgPJpNayKC6OLwekotk4GyjvO9UDJBRbo9WIapsXduQlT2FLg7CkFAyW0MXouFwvjKjSt41j23oU4wKXz9wJQIwEUsMWgcW3GFMVizHGDyUQo4bebm1dSjOLSzyYQt1c1ba/Q62v8LwXa0Zp1JL62FF+4fUNbMs53ZOPs/GukCeMPkj5GbLRtvkdo9JZ+kPEhuDpV8nIgXR07+ygHfCzV3N9+PnHiUjqTIVDOgGJ6eOdCh1LRP/5d5tMiQ/PXUR/ffsbOGzqdgPkQz0f95Oxru3HZ93R830me6g6Dev1wL12lwGNC29sXOS5bzYJTPritrokV2do7bSOrjQsqtqRqF/+kqDKvQP+5vVb38WsWHdqxERUGLuRXVRipcUHT/G7SWnFaHwI5XV8796xtw4VCWyvJX2GKLdk4iUx2jsa4HedtVtEpT2HjQqcOtxIrTlWrcMSoQI3mOG5lYuMabQtlNMdjsCOKpLLK0m5arcnQCejE5CwzMwauks9bNUXUhtV6f+7cpm5NoVBMGKp2phVYh8+UY+tcTaZQVSA8evpSzV4AjsVLvh73piXFEWPisQW2ImO+xZyjmBv5hR4obSuTatn1yLxzoVaBmCJnUxJpUlsKejM3TU6qaFqH+XqwmAdjUj81a9nZ4TyMU749vUFthSVPdT8p4xOg11AcPjSKM/WWG0b0T3B9v0amv4MZBln+lCiIOq+OspcFuoCWsM5q3cMz7cf7QD5VrpYSjcBNkqhefLaIOhz0++HD41IGfvrc6capDbiDIv+WAAW4sd75lpS1HgzaJmEj2JEz31pJUj2U40JNcqTTFtr9SYR9PyKW7pnsIPI/JhTlHYbVMAz/Az7Tbvc=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"confirmEmail"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/ConfirmEmail"}
  context={"endpoint"}
>
  
</MethodEndpoint>



confirmEmail

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["code","userId"],"type":"object","properties":{"userId":{"minLength":1,"type":"string"},"code":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"ConfirmEmailDTO"}},"text/json":{"schema":{"required":["code","userId"],"type":"object","properties":{"userId":{"minLength":1,"type":"string"},"code":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"ConfirmEmailDTO"}},"application/*+json":{"schema":{"required":["code","userId"],"type":"object","properties":{"userId":{"minLength":1,"type":"string"},"code":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"ConfirmEmailDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      