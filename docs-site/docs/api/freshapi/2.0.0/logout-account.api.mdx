---
id: logout-account
title: "logoutAccount"
description: "logoutAccount"
sidebar_label: "logoutAccount"
hide_title: true
hide_table_of_contents: true
api: eJyNVMFuGzkM/RWB52mcDdDL3IouCmRRbAMkuz0EPnA0tEeIRtKSlLPuYP59IY0dO07b7cVjkY/vUaL0JoiJGNXFcNtDCz5uY9YP1sYcFBpQ3Aq0j3CMrBtgkhSDkEA7wc31dfn0JJZdKjTQwn22lkRgnhsQspmd7qF9nKAjZGJoH9fzurkoulQeSYdYWkpRyjqhDtDCCpNbHVCrz7UGigrviKWKZPbQwqCapF2tfLTohyjaTimyznCp+7kAzEIADeyQHXZ+2V2pWLa3wewVWnh/ff0bzPPcXOpsmGR419PuHSZ3hd8y0zN14pTkKpC+kf2dduRjGinoUfxHpBn1F0nvFbcubP+P0Lsd/SLjHcc+27J4IV2fhnpvBxqXozqOdgLdJzpIlslUCLRHQHP48ynyiOVE//j6UC+KC5tYy536gv9UWjUfkc2Hu9syGGJZetrdvGnzYXBScCZx3LmexFDoU3RBxWwiGx3I9FH/JDWVt0D/vrkyt/qjihHDcpYWWZryaywqbSM7Oq597ozk7qWR83gX45MLW2kMht5kITaYdaCgztbnVsXR+/gsNS1Go0nEm8ijeXmUYiTbweCCMUxbJ7qkKrGPWxca453oqdkFRVwigZ4P0Z48nYHOSl7tzC+Nm5xqqidF58V0+9fiIY8dcWOEkO1wpC2wEZ+o9jbGnnxjLBOelL53Zm8Ap8PLqV9yh5ARRc2yjCBY8v5nzLWL4yC/MwPjgvW5L9mEIs+Re8MkpFJLaUTnzY7YbY5Dg7mphjRiKJc1YL3bi3eZk3m9up0vT0LpX10ljy4Umvoyp4OvPQImBw2cKA7etm6guFdBTFOHQn+xn+cS/icTF1tdn0yrOuuZdd59uX+ABsqmz9q4eInVNA8pDPvqgT6X1TQtiIf4RGEu1vlE+7KPsoZ5Pc/zf9HuN+0=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"logoutAccount"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/Logout"}
  context={"endpoint"}
>
  
</MethodEndpoint>



logoutAccount

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      