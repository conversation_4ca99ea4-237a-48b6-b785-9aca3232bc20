<?php

namespace Tests\Feature;

use App\Models\Car;
use App\Models\CarCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CarClubSubscriptionIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $testUser;
    protected Car $testCar;

    protected function setUp(): void
    {
        parent::setUp();

        $this->testUser = User::factory()->create();

        $category = CarCategory::factory()->create();
        $this->testCar = Car::factory()->create([
            'user_id' => $this->testUser->id,
            'category_id' => $category->id,
        ]);

        Sanctum::actingAs($this->testUser);
    }

    public function test_complete_subscription_lifecycle()
    {
        // 1. Create a subscription
        $subscriptionData = [
            'customerCarID' => $this->testCar->id,
            'frequency' => 2,
            'cleans' => 1,
            'resourceId' => 123,
            'resourceName' => 'Premium Wash Service',
            'category' => 'premium',
            'packageGroupId' => 456,
            'customerName' => 'John Doe',
        ];

        $createResponse = $this->postJson('/api/v3/car-club/subscriptions', $subscriptionData);
        $createResponse->assertStatus(201);

        $subscriptionId = $createResponse->json('data.id');
        $this->assertNotNull($subscriptionId);

        // 2. List subscriptions and verify it appears
        $listResponse = $this->getJson('/api/v3/car-club/subscriptions');
        $listResponse->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $subscriptionId)
            ->assertJsonPath('data.0.status', 'active');

        // 3. Pause the subscription with reason
        $pauseData = [
            'reason' => 'Going on holiday',
            'pauseUntil' => now()->addWeeks(3)->toDateString(),
        ];

        $pauseResponse = $this->postJson("/api/v3/car-club/subscriptions/{$subscriptionId}/pause", $pauseData);
        $pauseResponse->assertStatus(200);

        // 4. Verify subscription is paused
        $listAfterPause = $this->getJson('/api/v3/car-club/subscriptions');
        $listAfterPause->assertStatus(200)
            ->assertJsonPath('data.0.status', 'paused')
            ->assertJsonPath('data.0.pauseResume.pauseReason', 'Going on holiday')
            ->assertJsonPath('data.0.actions.canResume', true)
            ->assertJsonPath('data.0.actions.canPause', false);

        // 5. Resume the subscription with reason
        $resumeData = [
            'reason' => 'Back from holiday',
            'resumeDate' => now()->toDateString(),
        ];

        $resumeResponse = $this->postJson("/api/v3/car-club/subscriptions/{$subscriptionId}/resume", $resumeData);
        $resumeResponse->assertStatus(200);

        // 6. Verify subscription is active again
        $listAfterResume = $this->getJson('/api/v3/car-club/subscriptions');
        $listAfterResume->assertStatus(200)
            ->assertJsonPath('data.0.status', 'active')
            ->assertJsonPath('data.0.pauseResume.resumeReason', 'Back from holiday')
            ->assertJsonPath('data.0.actions.canPause', true)
            ->assertJsonPath('data.0.actions.canResume', false);

        // 7. Cancel the subscription
        $cancelData = [
            'cancellationReasonId' => 1,
            'cancellationReasonNotes' => 'No longer needed',
        ];

        $cancelResponse = $this->deleteJson("/api/v3/car-club/subscriptions/{$subscriptionId}", $cancelData);
        $cancelResponse->assertStatus(200);

        // 8. Verify subscription is cancelled
        $listAfterCancel = $this->getJson('/api/v3/car-club/subscriptions');
        $listAfterCancel->assertStatus(200)
            ->assertJsonPath('data.0.status', 'cancelled')
            ->assertJsonPath('data.0.cancellation.reasonId', 1)
            ->assertJsonPath('data.0.cancellation.reasonNotes', 'No longer needed')
            ->assertJsonPath('data.0.actions.canPause', false)
            ->assertJsonPath('data.0.actions.canResume', false)
            ->assertJsonPath('data.0.actions.canCancel', false);
    }

    public function test_subscription_validation_errors()
    {
        // Test missing required fields
        $response = $this->postJson('/api/v3/car-club/subscriptions', []);
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'customerCarID',
                'frequency',
                'cleans',
                'resourceId',
                'packageGroupId',
            ]);

        // Test invalid car ID (car that doesn't belong to user)
        $otherUser = User::factory()->create();
        $otherCategory = CarCategory::factory()->create();
        $otherCar = Car::factory()->create([
            'user_id' => $otherUser->id,
            'category_id' => $otherCategory->id,
        ]);

        $response = $this->postJson('/api/v3/car-club/subscriptions', [
            'customerCarID' => $otherCar->id,
            'frequency' => 2,
            'cleans' => 1,
            'resourceId' => 123,
            'packageGroupId' => 456,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['customerCarID']);
    }

    public function test_subscription_authorization()
    {
        // Create subscription for another user
        $otherUser = User::factory()->create();
        $otherCategory = CarCategory::factory()->create();
        $otherCar = Car::factory()->create([
            'user_id' => $otherUser->id,
            'category_id' => $otherCategory->id,
        ]);

        $subscription = \App\Models\CarClubSubscription::factory()->create([
            'user_id' => $otherUser->id,
            'car_id' => $otherCar->id,
        ]);

        // Try to access other user's subscription
        $response = $this->deleteJson("/api/v3/car-club/subscriptions/{$subscription->id}");
        $response->assertStatus(404);

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$subscription->id}/pause");
        $response->assertStatus(404);

        $response = $this->postJson("/api/v3/car-club/subscriptions/{$subscription->id}/resume");
        $response->assertStatus(404);
    }

    public function test_pagination_works()
    {
        // Create multiple subscriptions
        \App\Models\CarClubSubscription::factory()->count(25)->create([
            'user_id' => $this->testUser->id,
            'car_id' => $this->testCar->id,
        ]);

        // Test default pagination
        $response = $this->getJson('/api/v3/car-club/subscriptions');
        $response->assertStatus(200)
            ->assertJsonCount(15, 'data') // Default per page
            ->assertJsonStructure([
                'data',
                'links',
                'meta' => [
                    'current_page',
                    'per_page',
                    'total',
                ],
            ]);

        // Test custom per page
        $response = $this->getJson('/api/v3/car-club/subscriptions?perPage=10');
        $response->assertStatus(200)
            ->assertJsonCount(10, 'data')
            ->assertJsonPath('meta.per_page', 10);

        // Test second page
        $response = $this->getJson('/api/v3/car-club/subscriptions?page=2&perPage=10');
        $response->assertStatus(200)
            ->assertJsonCount(10, 'data')
            ->assertJsonPath('meta.current_page', 2);
    }
}
