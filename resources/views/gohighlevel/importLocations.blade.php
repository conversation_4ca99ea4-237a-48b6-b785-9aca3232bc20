<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('gohighlevel.import_locations') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <section>
                        <header>
                            <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                {{ __('gohighlevel.import_locations') }}
                            </h2>

                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                {{ __('gohighlevel.import_locations_description') }}
                            </p>
                        </header>

                        <div class="mt-6">
                            <p class="mb-4 text-sm text-gray-600 dark:text-gray-400">
                                {{ __('gohighlevel.using_env_token') }}
                            </p>
                            
                            <form method="post" action="{{ route('admin.gohighlevel.locations.import') }}" class="mt-6">
                                @csrf
                                @method('post')

                                <div class="flex items-center gap-4">
                                    <x-primary-button>{{ __('gohighlevel.import') }}</x-primary-button>
                                </div>
                            </form>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
