---
id: get-account-information
title: "getAccountInformation"
description: "getAccountInformation"
sidebar_label: "getAccountInformation"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v5DYM/SsCz9pMGqAX3xZtt8hiUSyQtD0Ec6Blji1EllSRmnRq+L8Xkj1f2aTdy4wlPj4+idSbIERKKDb4+w4a6Ek+GhOyl3u/C2msEdAg2DM0T7AGYashEcfgmRiaCe5ub8tfR2ySjTWpgYdsDDHDPGtgMjlZOUDzNEFLmChB87Sdt/pV0nsKRpIhrBJBQ0QZoIENRrtZ8Zs385jSnhLXujk5aGAQidxsNi4YdENgaaYYkszwWsqXAlALAWjYY7LYuuXAJWM58Q6zE2jgx9vbH2CeZ/26zi4RDx862n/AaG/wn5zohVq2Qnzj62Guy/5Me3IhjuTlWPw90ozynaQPgr31/f8ROrun72T8mkKXTVmcSLfnPj+Ygcblqo7dnkAOkdaSpTMVAs0RoNePT7V70MDnPx/r7Fi/CzXdiiv4T0Wq+gmT+vj1vjSGEi+a9nffyHwcLBeciinsbUesyHcxWC+sdiEpGUh1QX4jUZW3QP+4u1H38l7GiH65S4OJdflVBoX6kCwd1y63inN7EnK534bwbH3PWqHvVGZKCrMM5MWaOrW1ODoXXriGWUlQkVIZa3V6r6w4m0HhglGJesuyhCqxC731WjnLcha7oCiVHU8v625Hji5AFylXJ3OLcJVjDXUkaB2r9nBd3OexpaQVEyYzHGkLbMRnqtrG0JHTyiTCc6W37uwbwPnycuyW2LqlWFAyLy3whpz7L+aq4tjIN3qgrDcudyUakfklpE4lYhKuqTSidWpPye6OTYNZQwwsI/oyrB7rbPckavUldW1MV2N6ehtCf8smOrS+8NUnOq1m9wQYLeiTB5++Lnm3GoqlFfQ0tcj0e3LzXLb/ypSK/W7PTlYd+MJaf/3lETSUi7hQ9Op1ViNdQ+gP1RddLqtpWhCP4Zn8XOz0mQ7lSGUN83ae538BrY5KVw==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAccountInformation"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/Account/AccountInformation"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAccountInformation

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      