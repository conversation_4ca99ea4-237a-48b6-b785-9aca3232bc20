---
id: update-admin-allbookings-notes-byid
title: "updateAdminAllbookingsNotesByid"
description: "updateAdminAllbookingsNotesByid"
sidebar_label: "updateAdminAllbookingsNotesByid"
hide_title: true
hide_table_of_contents: true
api: eJzVVk1z3DYM/SscnPohex13etHNdpqpO5nUE2/ag2cPWAkrMaZIhoTW2Wr03zMgtdr1R5pcc7FXJPAeCAIPHICxiVDewYUxl87da9vEi7rTFlYFeAzYEVMQiwEsdgQlrLPZdQ0FaAsleOQWCgj0qdeBaig59FRArFrqEMoBeOfFUVumhgIUsHGhQ85Lv53DOK6yO0W+dPVOfCpnmSzLT/Te6ApZO7v4GJ2VtQP4gfYOrGOKEvnE6NYfqWIowAfnKbCmKC7ZrByg0/Yt2YZbKF/NTpGDtg2MYwFY11po0dwcAWzQRCqANRux/+BrZJqS906gXy//Tu5Mn/nHivg41b/8+gOFLgCBonc2ZrbzszP5V1OsgvaCCyXc9lVFMSa6SFUfNO9Saa8JAwUo71ZSisKecnBdQwl9okxNcWHMVP0xcV/utHTBY5Jv23fEratz51TSOqmDSlig1wsUxwUePBcpg4th7rsRJPqw3fdlHwyU0DL7WC4WxlVoWhe5HLwLPD6L760YqAwABWwxaFybnDXxyGnbYG+kQ38/O3sl+S2e8mwCxfakpu0Jen2K//WBHmgdNVM8tcTPaF/TlozzHVnek38NtEf+TtBbxkbb5luARm/pOxFvgqv7Sj5m0NWhWG6lE3Kq9iUzy5tQwiR7SSezQTH9eLPXvL/+XaYClO56fxC9Pz5j56XG55aZm0N0duMS1dQGb+RY6gqDuri5lkukEHP82/NnR1q2Ooqd8sFtdU1Rka2905aj2riguCVVO35HrBKumP5zfqqu+WseHdqc9wpDLOSvqpCpcUHT/tv0axX79RzI8fq+tAuFtlZ9pKCw55YsT+KTyNEY9xDTdlTslKcgg0PN7RlV7KtWYbZRgRodOW8lYOMabQtldORDsNmKJLPK0sO0WpOhI6Mjl0cnMzlw1fu0VROjNlGtd4/Jbd+tKRQqEoaq3cOKWYf3lGLrXE2mUFUgPDC9lLNnBofkJaWRvWlJRUbuY74CW5Ex/4ecothf5At3oLStTF/LrscYH1yoVaBIHJMrdaiN2lLQm/2lSaV6F7nDNDam90LWQ5UEUR0pokqSqF7S0Lmn0gT1BrUV7NTaw6SWd4Beg4wNea0UcKSYUEwtVEB5eK2sChBZFM9hWGOkD8GMoyx/6inIHFgd1DAJa62j/K7nKfQkyPmNAj+9nwbjzwqKl4OfFtHukuiaXr6ggHvaPXpUjauxgJawTvNomPavMtXJUlAO/s+eRiJW83i5uVhe/SkCNL2ppOaghIAP8lzDhxyAywWRxrusDWDQNj02YptB03juJevzzTxRtxTqi0cchmyxdPdkx3E+Mcu3nHYcvwAzCLZ2
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminAllbookingsNotesByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/allbookings/notes/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminAllbookingsNotesByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["notes"],"type":"object","properties":{"notes":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"UpdateBookingNotesDTO"}},"text/json":{"schema":{"required":["notes"],"type":"object","properties":{"notes":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"UpdateBookingNotesDTO"}},"application/*+json":{"schema":{"required":["notes"],"type":"object","properties":{"notes":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"UpdateBookingNotesDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      