---
id: create-admin-customers-login-byid
title: "createAdminCustomersLoginByid"
description: "createAdminCustomersLoginByid"
sidebar_label: "createAdminCustomersLoginByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVd9v2zYQ/leIe9oANk4z7EVvXYcCKYotWLLtIfDDWTpbRChS5R2deYL+9+EoS3Fqd+2LLd7P7453HwcQ3DFUj/A+s8SOEr9rOhdgbaHHhB0JJdUPELAjqKA+2t02YMEFqKBHacFCos/ZJWqgkpTJAtctdQjVAHLo1dMFoR0lsLCNqUOZRD/dwDiu1Z37GJhYPW6ur/WvIa6T68VFzXOf65qYYRwtMNU5OTkUZBvCRAmqx7UGij0lVJfbRuEmQqFS01Lhp7hz4ZeD0wpep/iWdUfSRg3bRxawU+kVrLB3K1Sv1dweXnn1Ww0v/RpBcaf93NCcPFTQivRcrVY+1ujbyFINfUwynmH7pAZmCgAW9pgcbvzUL/WYGrbF7LWzP19fv4VxHO2XebaJuH3T0P4N9u4K/82JnmnDToivAslZ2l9pTz72HQWZk38taEb5zqD3gjsXdt8K6N2evjPiXYpNrvWwBF2/jMm9zuLUqnlYlrHUlHAcVz0fDezx48M8qx//fiij58I2FncnXu0/KFTzHpN5d3erF0OJJ0z7mzOYD61jtTN9invXEBsKTR9dEDbbmIy0ZJoov5GYEldN/7q5MrfyNY8Ow9TLGhNb/TU1Cu1icjSffd4YzpsFyKl8E+OTCzu2BkNjMlMymKWlIK4ua1SSo/fxmYuajUTTU9IlNsuyseFctwYnG5No51gmVQlctsEa71hewE5WlFQS6PkobcjTidGJy6vK/ATc5L6oGhJ0ns3m8Dp5yN2GkjVMmOp2DqtmHT5RwdbFhrw1ZfmXTJd6dmbw0rzcN5PuKDIsKJmnKwg1ef9/kQuK+SIv3IFxofa5UW2PzM8xNSYRk3BxpQ6dN3tKbjtfGoy2kFSHQYd1Ju/Cb6YQnFkYzhSKM5cYcdkSoX9k1Xt0QSOXZR2O9PcI2DuwUAgQ7PJCMFgo1w4WqpNnY21BeU4dh2GDTH8mP44q/pwpKaWvX+itMGXjWL8bqLbomc4w1jEIBV3SH/44vkI/GrCXsR+FGA6FRX3WE1h4osPr521cjyeMf/f7/YMWmbXkpS1fkEVBezHBMEwWD/GJwjgu+UTPmmoc/wNte6uI
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAdminCustomersLoginByid"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/admin/customers/login/{customerId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAdminCustomersLoginByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      