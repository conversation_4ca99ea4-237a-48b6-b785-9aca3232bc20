#!/bin/bash

# Artisan Cloud - Simple wrapper for running Laravel Artisan commands in Cloud Run Jobs
# Usage: ./artisan-cloud <command> [arguments]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JOBS_SCRIPT="$SCRIPT_DIR/scripts/cloud-run-jobs.sh"
ENVIRONMENT=${ENVIRONMENT:-"staging"}  # Default to staging for development safety

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if jobs script exists
if [ ! -f "$JOBS_SCRIPT" ]; then
    echo "Error: Cloud Run Jobs script not found at $JOBS_SCRIPT"
    echo "Please run the setup script first: ./scripts/setup-cloud-run-jobs.sh"
    exit 1
fi

# Make sure the script is executable
chmod +x "$JOBS_SCRIPT"

# Handle special cases and shortcuts
case "${1:-help}" in
    "migrate")
        log_info "Running database migrations in Cloud Run ($ENVIRONMENT)..."
        ENVIRONMENT="$ENVIRONMENT" "$JOBS_SCRIPT" migrate
        ;;
    "migrate:fresh")
        log_warning "migrate:fresh is not supported in production. Use migrate instead."
        exit 1
        ;;
    "migrate:seed")
        log_info "Running database migrations with seeders in Cloud Run ($ENVIRONMENT)..."
        ENVIRONMENT="$ENVIRONMENT" "$JOBS_SCRIPT" migrate:seed
        ;;
    "queue:work")
        log_info "Starting queue worker in Cloud Run ($ENVIRONMENT)..."
        ENVIRONMENT="$ENVIRONMENT" "$JOBS_SCRIPT" queue:work "${@:2}"
        ;;
    "cache:clear")
        log_info "Clearing caches in Cloud Run ($ENVIRONMENT)..."
        ENVIRONMENT="$ENVIRONMENT" "$JOBS_SCRIPT" cache:clear
        ;;
    "optimize:clear")
        log_info "Clearing all optimization caches in Cloud Run ($ENVIRONMENT)..."
        ENVIRONMENT="$ENVIRONMENT" "$JOBS_SCRIPT" cache:clear true
        ;;
    "config:cache"|"route:cache"|"view:cache")
        log_info "Rebuilding caches in Cloud Run ($ENVIRONMENT)..."
        ENVIRONMENT="$ENVIRONMENT" "$JOBS_SCRIPT" cache:rebuild
        ;;
    "tinker")
        echo "Error: tinker is not supported in Cloud Run Jobs (interactive commands not allowed)"
        echo "Use 'gcloud run services proxy' to connect to the main service for interactive commands"
        exit 1
        ;;
    "serve")
        echo "Error: serve is not supported in Cloud Run Jobs"
        echo "The application is already running on Cloud Run service"
        exit 1
        ;;
    "help"|"--help"|"-h")
        echo "Artisan Cloud - Run Laravel Artisan commands in Google Cloud Run Jobs"
        echo ""
        echo "Usage: $0 <command> [arguments]"
        echo ""
        echo "Current Environment: $ENVIRONMENT"
        echo ""
        echo "Common Commands:"
        echo "  migrate                    Run database migrations"
        echo "  migrate:seed              Run migrations with seeders"
        echo "  queue:work [queue]        Start queue worker"
        echo "  cache:clear               Clear all caches"
        echo "  optimize:clear            Clear optimization caches"
        echo "  config:cache              Rebuild configuration cache"
        echo "  user:create               Create a new user (interactive)"
        echo "  search:reindex [model]    Reindex search data"
        echo ""
        echo "Custom Commands:"
        echo "  Any other artisan command will be executed in Cloud Run"
        echo ""
        echo "Environment Control:"
        echo "  ENVIRONMENT=staging $0 migrate     Run in staging"
        echo "  ENVIRONMENT=production $0 migrate  Run in production (default)"
        echo ""
        echo "Examples:"
        echo "  $0 migrate"
        echo "  $0 queue:work emails"
        echo "  $0 user:create"
        echo "  $0 app:gohighlevel:refresh-tokens"
        echo "  ENVIRONMENT=staging $0 migrate"
        echo ""
        echo "Monitoring:"
        echo "  Use './scripts/job-monitor.sh' for advanced monitoring"
        echo ""
        ;;
    *)
        # For any other command, pass it through to the jobs script
        log_info "Running custom artisan command in Cloud Run ($ENVIRONMENT): $*"
        ENVIRONMENT="$ENVIRONMENT" "$JOBS_SCRIPT" artisan "$@"
        ;;
esac
