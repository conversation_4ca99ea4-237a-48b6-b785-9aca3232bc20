---
id: update-consent
title: "updateConsent"
description: "updateConsent"
sidebar_label: "updateConsent"
hide_title: true
hide_table_of_contents: true
api: eJztVk1v3DYQ/SvEHFvZ6xjoRbfUaQAHRWPU6/Zg7GFWml0xpkiFM1xnK+i/F0NpP+20AXLNRbscPr75Ih/ZQ+gootjgb2soIXU1Ct0Ez+QFChBcM5SP8LaqQvICiwIifU7E8muot1D2UAUvii17wK5ztspks08cvNq4aqhF/afrbKRa6ahF6w5euib4I6ddYMGjaW55N1gUINuOoISw/ERVRkdNQSyxejlhLvsdehmCI/QwnDl7FXHk73WGk/hegQwFYF1bLQS6u6P4VuiYChArThc8jNVOLKGlOBG+m3/MDEJf5EcZv7uMx5vyp59/1PP76qkckbhTW07s+upKf2riKtpOqaGE+1RVxJw9MlUpWtlC+djDkjBShPJxMSyKs0Xn0tOSNEE1SfPSgqI0UMIMOzub5Gj2cLaGKW4ocvaVooMSGpGOy9nMhQpdE1jKvgtRBjh3/7sCzEgABWwwWly6MUldMWa5wuQESvjl6uqNlqM497OKxM1FTZsL7Owl/pMiPdOSrRBfepIXbt/RhlzoWvKyc/410oTyjaT3gmvr1/9H6OyGvpHxLoY6VTrYky4Ovb3X0zSWatfh/e5Tl9qZDNHdOAKK6c/7EFvUin74e573i57QPw9XzG9fsO10V56fIYmJzk/NaDs+JxPq9GSocSjA+lXIgU7b/r0WxdxgNG/vbnULUOQx+831i4LMG8uKM10MG1sTG/J1F6wXNqsQjTRk6iB/kJjMq9C/ri/NrXxtRYt+7FqFkQv9mgqF1iFa2o1dWhpOy30gx/ZlCE/Wr7kw6GuTmKLBJA15meQvO0fnwjPnaTYSTEdxFWJr9s8ANpyqxuCIMZHWlmWcysQurK0vjLMsh2BHFEW1eHqerDU5OgIdLTnJzI2Bm9TlqZoErWOz3J4696ldUiwME8aq2dEqrMUnyrG1oSZXmCoSHjy9VrMXgEPxsgzp3GQyLCiJxxb4ipz7L+Ycxa6Rr/TAWF+5VOtsh8zPIdYmEpNwXpq3uNlQtKtd03bK3mK+uDzmUzSKpTko38nu3B++/IboHNp8QWQN6CchfQTsLBT7l10Bp2K6KEDlUoF9v0Smh+iGQc2fE0WV88VBJbOiF9AQ1lnee3iiLZRwMz4OL+YajsJd0rBePBRVTPZ6f/fxfq76MD0wtalQQsRnKPK3BCggjBXPN7jaenDo1wnXih05832XNNfDVXgqPjnSaQr99ijCvh8R8/BEftDbYkxIdAzDYhiGfwGwgP8W
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateConsent"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/UpdateConsent"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateConsent

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["emailConsent","phoneConsent","postalConsent","smsConsent"],"type":"object","properties":{"emailConsent":{"type":"boolean"},"phoneConsent":{"type":"boolean"},"smsConsent":{"type":"boolean"},"postalConsent":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateCustomerConsentDTO"}},"text/json":{"schema":{"required":["emailConsent","phoneConsent","postalConsent","smsConsent"],"type":"object","properties":{"emailConsent":{"type":"boolean"},"phoneConsent":{"type":"boolean"},"smsConsent":{"type":"boolean"},"postalConsent":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateCustomerConsentDTO"}},"application/*+json":{"schema":{"required":["emailConsent","phoneConsent","postalConsent","smsConsent"],"type":"object","properties":{"emailConsent":{"type":"boolean"},"phoneConsent":{"type":"boolean"},"smsConsent":{"type":"boolean"},"postalConsent":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateCustomerConsentDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      