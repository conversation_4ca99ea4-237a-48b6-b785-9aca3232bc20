---
id: get-package-information
title: "getPackage-information"
description: "getPackage-information"
sidebar_label: "getPackage-information"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v5DYM/SsCz04mDbAX3xZtt8iiKAIk2x6COdA2xxZGlrQkNenU8H8vJM9kJl+7uYxH5OPjE0lxAsVeoH6AW2y32BOsK2CSGLyQQD3B9dVV/nQkLduoNnio4S61LYnAPFcg1Ca2uof6YYKGkImhfljP6wpCJMYcctNBDT3pIcmF9ZvAY3FB9YL7XdhIOoQDEVQQUQeoYYXRruKbAUK8I5YiLLGDGgbVKPVq5UKLbgii9RQD6/xKxJ8ZYBYCqGCHbLFxS0VyxFKSDSanUMOnq6tfYJ7n6mWeDZMMFx3tLjDaS/wvMT1SI1ZJLn25xfO0v9GOXIgjeT0mf480oX6Q9E6xt77/GaGzO/og4y2HLrX58ES6Pg3CXTvQuJTqOA4T6D7SIWXuTIFAfQRUhz9fSveghq//3Jfhyg0t4VZdxn/JUs2vyObz7U1uDLEsmnbXr2TeD1YyzkQOO9uRGPJdDNarmE1gowOZLuhfpKbwZujf15fmRt+LGNEvtWyRpcq/pkWlPrCl49mlxkhqnoSc25sQttb3Uhn0nUlCbDDpQF5tW6a2JEfnwqMUtxgNJhLnsTZPz0mMpHYwuGAMU29FF1chdqG3vjLOip7ELijibPH0eLB25OgMdBby7GZuEW5SLK6OFK0T0+yfJ/dpbIgrI4TcDkfaDBtxS0XbGDpylWmZ8JTprZq9ApyKl2K3+A4mI4qaZGmBb8m5HzEXFcdGvtEDY33rUpe9EUUeA3eGSUilhNKI1pkdsd0cmwZzBTGIjujzsHoss92Tmg8svKe3ofSvrqJD6zNfeaLTYcs9AEZbdt5runUFeZNl0DQ1KPSN3Txn8/dEnNfy+rTAymY+W6V//H4PFeT7nwl58SjL/jy40O/LOnQpn6ZpQdyHLfk5b9Et7fNN8hnm9TzP/wN7GkT5
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getPackage-information"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/package-information"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getPackage-information

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      