---
id: get-admin-valeters
title: "getAdminValeters"
description: "getAdminValeters"
sidebar_label: "getAdminValeters"
hide_title: true
hide_table_of_contents: true
api: eJyNVU1v3DgM/SsCz04mG2AvvhXtdpGiWBTItD0Ec+DYHFuILKki5ezswP99QXk+87Gby3gkPj5SpPi0A8GOoX6AD+1g/Q90JJQYVhVETDjMq/phBx4HghpYMMknFIIKrIcafmVKW6iAm54GhHoHso0zMlnfQQWbkAYUqKFFoSuxA8E0VUdGjDFYLwN5WW4j3bX8XuppWlWQiGPwTKz225sb/bTETbJRbFCa+9w0xKxBganJycq2HGlNmChB/bBSohApobrctVBDR3JZkeoZ6yuAgaQPe2fQAkoPNSww2gUqdDGesExpPJQ2Jwc19CKR68XChQZdH1jqXQxJpheRvyrAzARQwYjJ4trNBVCPuQIbzE6L/vvNzW8wlYJfxtkk4v6qpfEKo73Gf3KiJ1qzFeJrXw5wGfYTjeRC1D4dgr9FmlHeSXov2Fnf/R+hsyO9k/FbCm1udHEkXZ36fq9XaS7VofvHW6UhD7dN13tAtf/z+XCNv/xclrtk/SYUdytO8Z81VfMRk/nw7U4bQ4nnnMbbF2kue8uKMzGF0bbEhnxbBoHNJiQjPZk2yF8kpvAq9MfttbmTtzwG9HMtG0xc6a9pUKgLydJh7fLacF4fEznfX4fwaH3HlUHfmsyUDGbpyYttylyU4OhceOJiZiPBREo63+Y4PWw4N73BGWMSdZZlNhViFzrrK+MsyynZGUU61cbT0363JUdnoDOXi5O5OXGTYzG1JGgdm/X2MrjPw5pSZZgwNf2BVmEDPlLJbQgtuco0ifAU6bWavQCcipdjO9v2W4YFJfPcAt+Qc//FXLI4NPKVHhjrG5dbtUZkfgqpNYmYhIsrDWidGSnZzaFpMFUQA8uAXi/rXnQ7ElPky7wlcMexEPpbFtGh9UpVpnO317YHwGihgqJuRYpOz4cqmCJ2uzUyfU9umnR71nRVvdayylYL9QYdUwWPtH32wIzosqZQdOEN/KvPx8lxdRLIIvRnKv3nH0vNPetJjqd9NvQl070J/faMerebEcvwSH5SlZ7zEV3DtJqm6V9MaZkC
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminValeters"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/valeters"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminValeters

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"startDate","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"appointmentTypeIds","in":"query","schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      