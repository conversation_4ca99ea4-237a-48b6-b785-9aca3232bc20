#!/bin/bash

# Fresh Car Core Documentation Build Script
# This script builds the Docusaurus documentation and copies it to Laravel's public directory

set -e  # Exit on any error

echo "🔨 Building Fresh Car Core Documentation..."

# Check if docs-site directory exists
if [ ! -d "docs-site" ]; then
    echo "❌ Error: docs-site directory not found!"
    echo "Please run this script from the project root directory."
    exit 1
fi

# Check if docusaurus.config.ts exists
if [ ! -f "docs-site/docusaurus.config.ts" ]; then
    echo "❌ Error: docusaurus.config.ts not found!"
    echo "Please ensure Docusaurus is properly configured."
    exit 1
fi

# Navigate to docs-site and build
echo "🏗️  Building Docusaurus site..."
cd docs-site

# Install dependencies if node_modules doesn't exist
# if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --legacy-peer-deps
# fi

# Generate OpenAPI documentation
echo "🔄 Generating OpenAPI documentation..."
npm run openapi:clean
npm run openapi:generate

# Build the documentation
npm run build

# Go back to project root
cd ..

# Create public/docs directory if it doesn't exist
echo "📁 Copying built files to Laravel public directory..."
mkdir -p public/docs

# Copy built files to Laravel public directory
cp -r docs-site/build/* public/docs/

echo "✅ Documentation build complete!"
echo "📖 Documentation is now available at /admin/docs"
echo ""
echo "To develop documentation locally:"
echo "  npm run docs:dev"
echo ""
echo "To build documentation:"
echo "  npm run docs:build"
echo "  or"
echo "  ./scripts/build-docs.sh"
