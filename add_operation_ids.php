<?php

// <PERSON>ript to add operationId to all paths in OpenAPI spec
$jsonFile = 'openapi/freshcar_v2.json';
$content = file_get_contents($jsonFile);
$spec = json_decode($content, true);

if (!$spec) {
    die("Failed to parse JSON file\n");
}

// Function to generate operationId from path and method
function generateOperationId($path, $method) {
    // Remove /api/ prefix and clean up path
    $cleanPath = preg_replace('/^\/api\//', '', $path);
    
    // Replace path parameters with generic names
    $cleanPath = preg_replace('/\{[^}]+\}/', 'ById', $cleanPath);
    
    // Split by / and camelCase
    $parts = explode('/', $cleanPath);
    $parts = array_filter($parts); // Remove empty parts
    
    // Convert to camelCase
    $operationId = '';
    foreach ($parts as $i => $part) {
        if ($i === 0) {
            $operationId .= strtolower($part);
        } else {
            $operationId .= ucfirst(strtolower($part));
        }
    }
    
    // Add method prefix
    $methodPrefixes = [
        'get' => 'get',
        'post' => 'create',
        'put' => 'update',
        'patch' => 'update',
        'delete' => 'delete'
    ];
    
    $prefix = $methodPrefixes[$method] ?? $method;
    
    // Special cases for common patterns
    if (strpos($operationId, 'ById') !== false && $method === 'get') {
        $prefix = 'get';
    } elseif (strpos($operationId, 'ById') !== false && in_array($method, ['patch', 'put'])) {
        $prefix = 'update';
    } elseif (strpos($operationId, 'ById') !== false && $method === 'delete') {
        $prefix = 'delete';
    }
    
    return $prefix . ucfirst($operationId);
}

// Add operationId to all paths
foreach ($spec['paths'] as $path => &$pathItem) {
    foreach ($pathItem as $method => &$operation) {
        if (is_array($operation) && !isset($operation['operationId'])) {
            $operation['operationId'] = generateOperationId($path, $method);
        }
    }
}

// Write back to file
$newContent = json_encode($spec, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
file_put_contents($jsonFile, $newContent);

echo "Successfully added operationIds to all paths in $jsonFile\n";
