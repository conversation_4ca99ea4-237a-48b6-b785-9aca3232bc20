{"openapi": "3.0.1", "info": {"title": "Fresh Car Services", "version": "3.0.0", "description": "|This API provides endpoints for managing cars, car categories, car club subscriptions, car club bookings, and user authentication. It allows users to perform operations such as user registration and login, listing cars, registering new cars, deleting cars, listing car categories, looking up car details by registration number, searching cars by make and model, creating car club subscriptions, creating car club bookings, updating booking statuses, cancelling car club subscriptions, and managing user authentication including password resets and email verification.", "contact": {"name": "FounderAndLightning", "url": "https://github.com/founderandlightning"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "termsOfService": "https://github.com/founderandlightning/freshcar-lbp-core/blob/main/LICENSE", "x-rate-limit": {"description": "API rate limiting is enforced to ensure service stability and fair usage.", "default": {"rate": 60, "per": "minute", "by": "IP address or user ID when authenticated"}, "login": {"rate": 5, "per": "minute", "by": "IP address and email combination"}}}, "servers": [{"url": "https://localhost:1010", "description": "Local server", "variables": {"port": {"default": "1010"}}}, {"url": "https://development.freshcar.co.uk", "description": "Development server"}, {"url": "https://api-service-staging-*************.europe-west1.run.app/", "description": "Staging server"}, {"url": "https://api.freshcar.co.uk", "description": "Production server"}], "paths": {"/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "description": "Register a new user account. Can register with email or invitation key.", "operationId": "register", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegistrationResponse"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}}}, "security": []}}, "/auth/login": {"post": {"tags": ["Authentication"], "summary": "Authenticate user", "description": "Login with email and password to receive authentication token", "operationId": "login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "422": {"description": "Invalid credentials or validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}}, "429": {"description": "Too many login attempts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": []}}, "/auth/logout": {"post": {"tags": ["Authentication"], "summary": "Logout user", "description": "Logout the authenticated user and invalidate the token", "operationId": "logout", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearer": []}]}}, "/auth/forgot-password": {"post": {"tags": ["Authentication"], "summary": "Request password reset", "description": "Send password reset link to user's email", "operationId": "forgotPassword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "Password reset link sent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetLinkResponse"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}}, "429": {"description": "Too many requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": []}}, "/auth/reset-password": {"post": {"tags": ["Authentication"], "summary": "Reset password", "description": "Reset user password using token from email", "operationId": "resetPassword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "Password reset successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetResponse"}}}}, "422": {"description": "Invalid token or validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}}}, "security": []}}, "/auth/email/verification-notification": {"post": {"tags": ["Authentication"], "summary": "Resend email verification", "description": "Resend email verification notification to authenticated user", "operationId": "resendEmailVerification", "responses": {"200": {"description": "Verification link sent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailVerificationResponse"}}}}, "422": {"description": "User already verified", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "429": {"description": "Too many requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearer": []}]}}, "/auth/verify-email-resend/{id}/{hash}": {"post": {"tags": ["Authentication"], "summary": "Resend email verification for specific user", "description": "Resend email verification for a specific user using signed URL", "operationId": "resendEmailVerificationForUser", "parameters": [{"name": "id", "in": "path", "required": true, "description": "User ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "hash", "in": "path", "required": true, "description": "Email hash for verification", "schema": {"type": "string"}}], "responses": {"200": {"description": "Verification link sent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailVerificationResponse"}}}}, "422": {"description": "User already verified", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid signature", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "429": {"description": "Too many requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": []}}, "/v3/api/cars": {"get": {"tags": ["Car"], "operationId": "listAllCarsForAuthenticatedUser", "summary": "List user cars", "description": "This endpoint allows you to retrieve a list of all cars associated with the authenticated user. The cars are returned in an array format.", "responses": {"200": {"description": "List of cars retrieved successfully"}, "401": {"description": "Unauthorized - Authentication required"}}, "security": [{"bearer": []}]}, "post": {"tags": ["Car"], "summary": "Register a new car", "operationId": "registerNewCar", "description": "This endpoint allows you to register a new car for the authenticated user. The car details are provided in the request body.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerCarDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerCarDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerCarDTO"}}}}, "responses": {"201": {"description": "Car created successfully"}}, "security": [{"bearer": []}]}}, "/v3/api/cars/{id}": {"get": {"tags": ["Car"], "summary": "Get car details", "operationId": "getCarDetails", "description": "This endpoint allows you to retrieve the details of a specific car associated with the authenticated user. The car is identified by its unique ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Car details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerCarDTO"}}}}, "401": {"description": "Unauthorized - Authentication required"}, "404": {"description": "Car not found"}}, "security": [{"bearer": []}]}, "delete": {"tags": ["Car"], "summary": "Delete a car", "operationId": "deleteCar", "description": "This endpoint allows you to delete a specific car associated with the authenticated user. The car is identified by its unique ID. This is a soft delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Car deleted successfully"}, "401": {"description": "Unauthorized - Authentication required"}, "404": {"description": "Car not found"}}, "security": [{"bearer": []}]}}, "/v3/api/car-categories": {"get": {"tags": ["Car Categories"], "summary": "List all car categories", "description": "This endpoint retrieves all available car categories in the system.", "operationId": "getCarCategories", "responses": {"200": {"description": "Car categories retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CarCategoryDTO"}}}}}, "401": {"description": "Unauthorized - Authentication required"}}, "security": [{"bearer": []}]}}, "/v3/api/car-lookup/registration/{registrationNumber}": {"get": {"operationId": "lookupCarByRegistration", "tags": ["Car Lookup"], "summary": "Look up car details by registration number", "description": "Public endpoint that doesn't require authentication. Checks the DVLA for car details.", "parameters": [{"name": "registrationNumber", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/CarLookupSuccessResponse"}, "404": {"$ref": "#/components/responses/CarLookupNotFoundResponse"}}, "security": []}}, "/v3/api/car-lookup/make-model": {"get": {"operationId": "searchCarsByMakeModel", "tags": ["Car Lookup"], "summary": "Search by make and model", "description": "Public endpoint that doesn't require authentication, Searches for cars using the make and the model of the vehicle", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Search results retrieved successfully"}, "404": {"description": "Car not found"}}, "security": []}}, "/v3/api/car-club/subscriptions": {"get": {"tags": ["Car Club"], "summary": "List subscriptions", "description": "This endpoint retrieves all car club subscriptions associated with the authenticated user.", "operationId": "listCarClubSubscriptions", "responses": {"200": {"description": "Subscriptions retrieved successfully"}}, "security": [{"bearer": []}]}, "post": {"tags": ["Car Club"], "summary": "Create a new car club subscription", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarClubRegisterDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CarClubRegisterDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CarClubRegisterDTO"}}}}, "responses": {"201": {"description": "Subscription created successfully"}}, "security": [{"bearer": []}]}}, "/v3/api/car-club/subscriptions/{subscriptionId}": {"delete": {"tags": ["Car Club"], "summary": "Cancel a subscription", "description": "This endpoint cancels an active car club subscription. The subscription is identified by its unique ID. The cancellation reason and notes are optional.", "operationId": "cancelCarClubSubscription", "parameters": [{"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarClubSubscriptionCancellationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CarClubSubscriptionCancellationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CarClubSubscriptionCancellationDTO"}}}}, "responses": {"200": {"description": "Subscription cancelled successfully"}, "404": {"description": "Subscription not found"}}, "security": [{"bearer": []}]}}, "/v3/api/car-club/subscriptions/{subscriptionId}/pause": {"post": {"tags": ["Car Club"], "summary": "Pause a subscription", "description": "This endpoint pauses an active car club subscription. The subscription is identified by its unique ID. The pause reason and date are optional.", "operationId": "pauseCarClubSubscription", "parameters": [{"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Subscription paused successfully"}, "404": {"description": "Subscription not found"}}, "security": [{"bearer": []}]}}, "/v3/api/car-club/subscriptions/{subscriptionId}/resume": {"post": {"tags": ["Car Club"], "summary": "Resume a subscription", "description": "This endpoint resumes a paused car club subscription.", "operationId": "resumeCarClubSubscription", "parameters": [{"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Subscription resumed successfully"}, "404": {"description": "Subscription not found"}}, "security": [{"bearer": []}]}}, "/v3/api/car-club/bookings": {"post": {"tags": ["Car Club"], "summary": "Create Car club booking", "description": "This endpoint allows you to create a new car club booking. The booking details are provided in the request body.", "operationId": "createCarClubBooking", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCarClubBookingRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCarClubBookingRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCarClubBookingRequestDTO"}}}}, "responses": {"201": {"description": "Booking created successfully"}}, "security": [{"bearer": []}]}}, "/v3/api/car-club/bookings/{bookingId}": {"patch": {"tags": ["Car Club"], "summary": "Update booking status", "description": "This endpoint updates the status of a car club booking.", "operationId": "updateBookingStatus", "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingEnquiryStatusDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingEnquiryStatusDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBookingEnquiryStatusDTO"}}}}, "responses": {"200": {"description": "Booking status updated successfully"}, "404": {"description": "Booking not found"}}, "security": [{"bearer": []}]}, "get": {"tags": ["Car Club"], "summary": "Get cancellation information for a booking", "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Cancellation information retrieved successfully"}, "404": {"description": "Booking not found"}}, "security": [{"bearer": []}]}}, "/v3/api/car-club/prices": {"get": {"tags": ["Car Club"], "summary": "Get car club prices by package and category", "description": "This endpoint retrieves the prices for a specific car club package and category.", "operationId": "getCarClubPrices", "parameters": [{"name": "packageId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "carCategory", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Prices retrieved successfully"}}, "security": [{"bearer": []}]}}, "/v3/api/car-club/cancellation-reasons": {"get": {"tags": ["Car Club"], "summary": "Get Cancellation reasons", "description": "This endpoint retrieves all available cancellation reasons in the system.", "operationId": "getCarClubCancellationReasons", "responses": {"200": {"description": "Cancellation reasons retrieved successfully"}}, "security": [{"bearer": []}]}}}, "components": {"schemas": {"RegisterRequest": {"type": "object", "required": ["password"], "properties": {"email": {"type": "string", "format": "email", "maxLength": 255, "description": "User email address (required if invitation_key not provided)"}, "invitation_key": {"type": "string", "minLength": 64, "maxLength": 64, "description": "64-character invitation key (required if email not provided)"}, "password": {"type": "string", "format": "password", "minLength": 8, "description": "User password"}, "password_confirmation": {"type": "string", "format": "password", "description": "Password confirmation (must match password)"}, "role": {"type": "array", "items": {"type": "string"}, "description": "Optional array of role names"}}, "oneOf": [{"required": ["email", "password", "password_confirmation"]}, {"required": ["invitation_key", "password", "password_confirmation"]}]}, "LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "format": "password", "description": "User password"}, "remember": {"type": "boolean", "description": "Remember user login", "default": false}}}, "ForgotPasswordRequest": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}}}, "ResetPasswordRequest": {"type": "object", "required": ["token", "email", "password", "password_confirmation"], "properties": {"token": {"type": "string", "description": "Password reset token from email"}, "email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "format": "password", "minLength": 8, "description": "New password"}, "password_confirmation": {"type": "string", "format": "password", "description": "Password confirmation (must match password)"}}}, "UserData": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "User ID"}, "email": {"type": "string", "format": "email", "description": "User email address"}, "email_verified_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Email verification timestamp"}, "first_name": {"type": "string", "nullable": true, "description": "User first name"}, "last_name": {"type": "string", "nullable": true, "description": "User last name"}, "birth_date": {"type": "string", "format": "date", "nullable": true, "description": "User birth date"}, "created_at": {"type": "string", "format": "date-time", "description": "Account creation timestamp"}, "full_name": {"type": "string", "description": "User full name"}, "initials": {"type": "string", "description": "User initials"}, "image": {"type": "string", "nullable": true, "description": "User profile image URL"}, "image_thumbnail": {"type": "string", "nullable": true, "description": "User profile image thumbnail URL"}, "onboarded": {"type": "boolean", "description": "Whether user has completed onboarding"}}}, "UserRegistrationResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UserData"}, "message": {"type": "string", "description": "Success message"}}}, "LoginResponse": {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/components/schemas/UserData"}, {"type": "object", "properties": {"token": {"type": "string", "description": "Authentication token"}}, "required": ["token"]}]}, "message": {"type": "string", "description": "Success message"}}}, "SuccessResponse": {"type": "object", "properties": {"data": {"type": "object", "description": "Response data"}, "message": {"type": "string", "description": "Success message"}}}, "PasswordResetLinkResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "Password reset status message"}}}, "PasswordResetResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "Password reset status message"}}}, "EmailVerificationResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"status": {"type": "string", "description": "Verification status"}}}, "message": {"type": "string", "description": "Success message"}}}, "ErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Error message"}, "data": {"type": "string", "description": "Additional error data", "nullable": true}}}, "ValidationErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Validation error message"}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "Field-specific validation errors"}}}, "AddPreferredValeterDTO": {"required": ["resourceID", "resourceName"], "type": "object", "properties": {"resourceID": {"type": "integer", "format": "int32"}, "resourceName": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "AdminManualBookingDto": {"required": ["bookingId", "endDateTime", "resourceId", "startDateTime"], "type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "startDateTime": {"type": "string", "format": "date-time"}, "endDateTime": {"type": "string", "format": "date-time"}, "resourceId": {"type": "integer", "format": "int32"}, "muteConfirmationEmail": {"type": "boolean"}}, "additionalProperties": false}, "BookingCustomerCarPackageDTO": {"type": "object", "properties": {"bookingCustomerCarId": {"type": "integer", "format": "int32"}, "bookingId": {"type": "integer", "format": "int32"}, "customerCarId": {"type": "integer", "format": "int32"}, "packageGroupId": {"type": "integer", "format": "int32"}, "registrationNumber": {"type": "string", "nullable": true}, "makeAndModel": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "colour": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "optionalExtras": {"type": "array", "items": {"$ref": "#/components/schemas/BookingPackageItemOptionalExtraDTO"}, "nullable": true}}, "additionalProperties": false}, "BookingDTO": {"required": ["addressId", "bookingStatusId", "requestedDate"], "type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32", "nullable": true}, "requestedDate": {"type": "string", "format": "date-time"}, "confirmedDate": {"type": "string", "format": "date-time", "nullable": true}, "addressId": {"type": "integer", "format": "int32"}, "additionalComments": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "preferredTime": {"type": "string", "format": "date-time", "nullable": true}, "bookingStatusId": {"type": "integer", "format": "int32"}, "totalCost": {"type": "number", "format": "double"}, "enquiryStatus": {"type": "string", "nullable": true}, "resourceId": {"type": "integer", "format": "int32"}, "resourceName": {"type": "string", "nullable": true}, "timeOfDay": {"type": "string", "nullable": true}, "bookingReferenceNumber": {"type": "string", "nullable": true}, "bookingHubDurationMinutes": {"type": "string", "nullable": true}, "overridePrice": {"type": "number", "format": "double", "nullable": true}, "bookingCustomerCars": {"type": "array", "items": {"$ref": "#/components/schemas/BookingCustomerCarPackageDTO"}, "nullable": true}, "isPrepaid": {"type": "boolean"}, "isRefunded": {"type": "boolean"}, "customerCarClubPackageId": {"type": "integer", "format": "int32", "nullable": true}, "currency": {"$ref": "#/components/schemas/Currency"}}, "additionalProperties": false}, "BookingPackageItemOptionalExtraDTO": {"type": "object", "properties": {"packageItemId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CarBookingBookableSlot": {"type": "object", "properties": {"carCategory": {"type": "string", "nullable": true}, "packageGroupId": {"type": "integer", "format": "int32"}, "optionalExtraIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "customerCarClubPackageId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CarClubBookingCustomerCarPackageDTO": {"type": "object", "properties": {"customerCarId": {"type": "integer", "format": "int32"}, "customerCarCategory": {"type": "string", "nullable": true}, "optionalExtras": {"type": "array", "items": {"$ref": "#/components/schemas/BookingPackageItemOptionalExtraDTO"}, "nullable": true}, "customerCarClubPackageId": {"type": "integer", "format": "int32"}, "packageGroupId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CarClubRegisterDTO": {"type": "object", "properties": {"customerCarID": {"type": "integer", "format": "int32"}, "frequency": {"type": "integer", "format": "int32"}, "cleans": {"type": "integer", "format": "int32"}, "resourceId": {"type": "integer", "format": "int32"}, "resourceName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "packageGroupId": {"type": "integer", "format": "int32"}, "customerName": {"type": "string", "nullable": true}, "paymentDetails": {"$ref": "#/components/schemas/PaymentDetailsDTO"}}, "additionalProperties": false}, "CarClubSubscriptionCancellationDTO": {"type": "object", "properties": {"cancellationReasonId": {"type": "integer", "format": "int32", "nullable": true}, "cancellationReasonNotes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CheckPostcodeDTO": {"required": ["postcode"], "type": "object", "properties": {"postcode": {"minLength": 1, "pattern": "^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$", "type": "string"}}, "additionalProperties": false}, "ConfirmEmailDTO": {"required": ["code", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "code": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "Country": {"enum": [0, 1], "type": "integer", "format": "int32"}, "CreateBookingDTO": {"type": "object", "properties": {"booking": {"$ref": "#/components/schemas/BookingDTO"}, "paymentDetails": {"$ref": "#/components/schemas/PaymentDetailsDTO"}}, "additionalProperties": false}, "CreateCarClubBookingDTO": {"type": "object", "properties": {"addressId": {"type": "integer", "format": "int32"}, "customerCarId": {"type": "integer", "format": "int32"}, "customerCarCategory": {"type": "string", "nullable": true}, "optionalExtras": {"type": "array", "items": {"$ref": "#/components/schemas/BookingPackageItemOptionalExtraDTO"}, "nullable": true}, "confirmedDate": {"type": "string", "format": "date-time"}, "preferredTime": {"type": "string", "format": "date-time", "nullable": true}, "additionalComments": {"type": "string", "nullable": true}, "resourceId": {"type": "integer", "format": "int32"}, "isPrepaid": {"type": "boolean"}, "customerCarClubPackageId": {"type": "integer", "format": "int32"}, "packageGroupId": {"type": "integer", "format": "int32"}, "bookingHubDurationMinutes": {"type": "string", "nullable": true}, "carClubBookingCustomerCars": {"type": "array", "items": {"$ref": "#/components/schemas/CarClubBookingCustomerCarPackageDTO"}, "nullable": true}}, "additionalProperties": false}, "CreateCarClubBookingRequestDTO": {"type": "object", "properties": {"carClubBooking": {"$ref": "#/components/schemas/CreateCarClubBookingDTO"}, "paymentDetails": {"$ref": "#/components/schemas/PaymentDetailsDTO"}}, "additionalProperties": false}, "CreateCustomerDTO": {"required": ["address", "contactNumber", "email", "firstName", "lastName"], "type": "object", "properties": {"customerId": {"type": "integer", "format": "int32", "nullable": true}, "title": {"maxLength": 10, "type": "string", "nullable": true}, "firstName": {"maxLength": 50, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 50, "minLength": 1, "type": "string"}, "contactNumber": {"maxLength": 50, "minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string"}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "customerReferenceNumber": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/CustomerAddressDTO"}, "sanitisedEmail": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Currency": {"enum": [0, 1], "type": "integer", "format": "int32"}, "CustomerAddressDTO": {"required": ["addressLine1", "postcode", "town"], "type": "object", "properties": {"addressId": {"type": "integer", "format": "int32", "nullable": true}, "addressLine1": {"maxLength": 150, "minLength": 1, "type": "string"}, "addressLine2": {"maxLength": 150, "type": "string", "nullable": true}, "addressLine3": {"maxLength": 150, "type": "string", "nullable": true}, "town": {"maxLength": 50, "minLength": 1, "type": "string"}, "postcode": {"maxLength": 12, "minLength": 1, "type": "string"}, "country": {"$ref": "#/components/schemas/Country"}}, "additionalProperties": false}, "CustomerCarDTO": {"required": ["makeAndModel", "registrationNumber"], "type": "object", "properties": {"customerCarId": {"type": "integer", "format": "int32", "nullable": true}, "registrationNumber": {"maxLength": 50, "minLength": 1, "type": "string"}, "makeAndModel": {"maxLength": 150, "minLength": 1, "type": "string"}, "category": {"maxLength": 10, "type": "string", "nullable": true}, "colour": {"maxLength": 50, "type": "string", "nullable": true}, "year": {"maxLength": 10, "type": "string", "nullable": true}}, "additionalProperties": false}, "EmailAvailabilityDTO": {"required": ["email"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ForgotPasswordRequestDTO": {"required": ["email"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}}, "additionalProperties": false}, "LoginDTO": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string", "format": "password"}}, "additionalProperties": false}, "MbhPatchBookingDto": {"type": "object", "properties": {"notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NewCustomerBookingDTO": {"type": "object", "properties": {"registerDTO": {"$ref": "#/components/schemas/RegisterDTO"}, "bookingDTO": {"$ref": "#/components/schemas/NewCustomerCarBookingDTO"}, "paymentDetails": {"$ref": "#/components/schemas/PaymentDetailsDTO"}}, "additionalProperties": false}, "NewCustomerCarBookingDTO": {"required": ["addressId", "bookingStatusId", "requestedDate"], "type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32", "nullable": true}, "requestedDate": {"type": "string", "format": "date-time"}, "confirmedDate": {"type": "string", "format": "date-time", "nullable": true}, "addressId": {"type": "integer", "format": "int32"}, "additionalComments": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "preferredTime": {"type": "string", "format": "date-time", "nullable": true}, "bookingStatusId": {"type": "integer", "format": "int32"}, "totalCost": {"type": "number", "format": "double"}, "enquiryStatus": {"type": "string", "nullable": true}, "resourceId": {"type": "integer", "format": "int32"}, "resourceName": {"type": "string", "nullable": true}, "timeOfDay": {"type": "string", "nullable": true}, "bookingReferenceNumber": {"type": "string", "nullable": true}, "bookingHubDurationMinutes": {"type": "string", "nullable": true}, "overridePrice": {"type": "number", "format": "double", "nullable": true}, "bookingCustomerCars": {"type": "array", "items": {"$ref": "#/components/schemas/BookingCustomerCarPackageDTO"}, "nullable": true}, "isPrepaid": {"type": "boolean"}, "isRefunded": {"type": "boolean"}, "customerCarClubPackageId": {"type": "integer", "format": "int32", "nullable": true}, "currency": {"$ref": "#/components/schemas/Currency"}}, "additionalProperties": false}, "PaymentDetailsDTO": {"type": "object", "properties": {"isPayNow": {"type": "boolean"}, "paymentMethodId": {"type": "string", "nullable": true}, "paymentIntentId": {"type": "string", "nullable": true}, "paymentIntentClientSecret": {"type": "string", "nullable": true}, "requiresAction": {"type": "boolean"}}, "additionalProperties": false}, "PostcodeAddressesLookupDTO": {"required": ["postcode"], "type": "object", "properties": {"postcode": {"minLength": 1, "pattern": "^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$", "type": "string"}}, "additionalProperties": false}, "QueryBookableSlotsDTO": {"required": ["date", "postcode"], "type": "object", "properties": {"carBookings": {"type": "array", "items": {"$ref": "#/components/schemas/CarBookingBookableSlot"}, "nullable": true}, "postcode": {"minLength": 1, "pattern": "^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$", "type": "string"}, "date": {"type": "string", "format": "date-time"}, "resourceId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "RefreshTokenDTO": {"required": ["email", "jwtToken"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "jwtToken": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "RefundBookingDTO": {"type": "object", "properties": {"isRefunded": {"type": "boolean"}}, "additionalProperties": false}, "RegisterDTO": {"required": ["address", "contactNumber", "email", "firstName", "lastName", "password"], "type": "object", "properties": {"customerId": {"type": "integer", "format": "int32", "nullable": true}, "title": {"maxLength": 10, "type": "string", "nullable": true}, "firstName": {"maxLength": 50, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 50, "minLength": 1, "type": "string"}, "contactNumber": {"maxLength": 50, "minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string"}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "customerReferenceNumber": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/CustomerAddressDTO"}, "sanitisedEmail": {"type": "string", "nullable": true, "readOnly": true}, "password": {"minLength": 1, "type": "string", "format": "password"}, "confirmPassword": {"type": "string", "format": "password", "nullable": true}}, "additionalProperties": false}, "ResetPasswordDTO": {"required": ["code", "email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string", "format": "password"}, "confirmPassword": {"type": "string", "format": "password", "nullable": true}, "code": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "SoftDeleteEntityDTO": {"type": "object", "properties": {"isDeleted": {"type": "boolean"}}, "additionalProperties": false}, "UpdateBookingCustomerCarsDTO": {"required": ["bookingCustomerCars"], "type": "object", "properties": {"bookingCustomerCars": {"type": "array", "items": {"$ref": "#/components/schemas/BookingCustomerCarPackageDTO"}}, "overridePrice": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "UpdateBookingEnquiryStatusDTO": {"required": ["enquiryStatus"], "type": "object", "properties": {"enquiryStatus": {"minLength": 1, "type": "string"}, "confirmedDate": {"type": "string", "format": "date-time", "nullable": true}, "cancellationReasonId": {"type": "integer", "format": "int32", "nullable": true}, "cancellationReasonNotes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateBookingNotesDTO": {"required": ["notes"], "type": "object", "properties": {"notes": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "UpdateCarClubCleansDTO": {"required": ["cleansRemaining"], "type": "object", "properties": {"cleansRemaining": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateCustomerConsentDTO": {"required": ["emailConsent", "phoneConsent", "postalConsent", "smsConsent"], "type": "object", "properties": {"emailConsent": {"type": "boolean"}, "phoneConsent": {"type": "boolean"}, "smsConsent": {"type": "boolean"}, "postalConsent": {"type": "boolean"}}, "additionalProperties": false}, "UpdateCustomerDetailsDTO": {"required": ["contactNumber", "firstName", "lastName"], "type": "object", "properties": {"customerId": {"type": "integer", "format": "int32", "nullable": true}, "title": {"maxLength": 10, "type": "string", "nullable": true}, "firstName": {"maxLength": 50, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 50, "minLength": 1, "type": "string"}, "contactNumber": {"maxLength": 50, "minLength": 1, "type": "string"}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UpdatePasswordDTO": {"required": ["confirmNewPassword", "email", "newPassword", "oldPassword"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "oldPassword": {"minLength": 1, "type": "string", "format": "password"}, "newPassword": {"minLength": 1, "type": "string", "format": "password"}, "confirmNewPassword": {"minLength": 1, "type": "string", "format": "password"}}, "additionalProperties": false}, "CarLookupResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/CarData"}, "meta": {"$ref": "#/components/schemas/ResponseMeta"}}}, "CarData": {"type": "object", "properties": {"registrationNumber": {"type": "string", "description": "Vehicle registration number"}, "makeAndModel": {"type": "string", "description": "Combined make and model of the vehicle"}, "make": {"type": "string", "description": "Make of the vehicle"}, "model": {"type": "string", "description": "Model of the vehicle"}, "category": {"type": "string", "description": "Vehicle category"}, "colour": {"type": "string", "description": "Vehicle colour"}, "year": {"type": "string", "description": "Year of manufacture"}, "fuelType": {"type": "string", "description": "Type of fuel used by the vehicle"}, "engineSize": {"type": "string", "description": "Engine size in cc"}, "transmission": {"type": "string", "description": "Transmission type"}, "motExpiryDate": {"type": "string", "format": "date", "description": "Date when the MOT expires"}, "taxDueDate": {"type": "string", "format": "date", "description": "Date when the vehicle tax is due"}, "co2Emissions": {"type": "string", "description": "CO2 emissions in g/km"}, "motHistory": {"$ref": "#/components/schemas/MotHistoryData"}}}, "MotHistoryData": {"type": "object", "description": "MOT history data for the vehicle", "properties": {"registration": {"type": "string", "description": "Vehicle registration number"}, "make": {"type": "string", "description": "Make of the vehicle"}, "model": {"type": "string", "description": "Model of the vehicle"}, "firstUsedDate": {"type": "string", "format": "date", "description": "Date when the vehicle was first used"}, "fuelType": {"type": "string", "description": "Type of fuel used by the vehicle"}, "primaryColour": {"type": "string", "description": "Primary colour of the vehicle"}, "registrationDate": {"type": "string", "format": "date", "description": "Date when the vehicle was registered"}, "manufactureDate": {"type": "string", "format": "date", "description": "Date when the vehicle was manufactured"}, "engineSize": {"type": "string", "description": "Engine size in cc"}, "hasOutstandingRecall": {"type": "string", "description": "Indicates if the vehicle has an outstanding recall"}, "tests": {"type": "array", "description": "List of MOT tests", "items": {"$ref": "#/components/schemas/MotTest"}}}}, "MotTest": {"type": "object", "properties": {"testDate": {"type": "string", "format": "date", "description": "Date when the test was completed"}, "testResult": {"type": "string", "description": "Result of the test (PASSED or FAILED)"}, "expiryDate": {"type": "string", "format": "date", "description": "Date when the MOT expires"}, "odometerValue": {"type": "string", "description": "Odometer reading at the time of the test"}, "odometerUnit": {"type": "string", "description": "Unit of the odometer reading (km or mi)"}, "motTestNumber": {"type": "string", "description": "MOT test number"}, "failures": {"type": "array", "description": "List of reasons for failure", "items": {"$ref": "#/components/schemas/MotTestFailure"}}}}, "MotTestFailure": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of failure"}, "text": {"type": "string", "description": "Description of the failure"}, "dangerous": {"type": "boolean", "description": "Indicates if the failure is dangerous"}}}, "ResponseMeta": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time", "description": "Timestamp of the response"}, "source": {"type": "string", "description": "Source of the data"}}}, "CarCategoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "description": "Unique identifier for the car category"}, "name": {"type": "string", "description": "Name of the car category (e.g., 'Small', 'Medium', 'Large', 'SUV')"}, "description": {"type": "string", "description": "Detailed description of the car category"}, "priceMultiplier": {"type": "number", "format": "float", "description": "Price multiplier factor for this category"}}, "required": ["id", "name"]}}, "securitySchemes": {"bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: 'Authorization: Bearer {token}'"}}, "responses": {"CarLookupSuccessResponse": {"description": "Car details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarLookupResponse"}}}}, "CarLookupNotFoundResponse": {"description": "Car not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Car not found"}}}}}}}}, "security": [{"bearer": []}], "tags": [{"name": "Authentication", "description": "Endpoints for managing users and authentication."}, {"name": "Car", "description": "Endpoints for looking up car details by registration number and searching cars by make and model."}, {"name": "Car Club", "description": "Endpoints for managing pets and pet stores."}, {"name": "Car Lookup", "description": "Endpoints for managing pets and pet stores."}, {"name": "Car Categories", "description": "Endpoints for managing pets and pet stores."}]}