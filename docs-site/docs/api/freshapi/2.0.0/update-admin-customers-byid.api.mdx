---
id: update-admin-customers-byid
title: "updateAdminCustomersByid"
description: "updateAdminCustomersByid"
sidebar_label: "updateAdminCustomersByid"
hide_title: true
hide_table_of_contents: true
api: eJztV99z2zYM/ld4eNoPJXa85brprWnXu+56ba51t7vl/ABLsM2GIlUSdOrp9L/vQMmSnbhd+p4XWySBDyAAfiAbYFwHyG/gRQzsKvLheVlpC4sMavRYEZOX9QYsVgQ5FL3c6xIy0BZyqJE3kIGnz1F7KiFnHymDUGyoQsgb4F0tmtoyrclDBivnK+Ru6pcZtO2iU6fAV67ciU7hLJNl+cS6NrpA1s5OPgVnZW4EH83eJCUs+G2slp0d7QO/Fb8zMNh/LrK9Q275iQqGDGrvavKsKSTT4w4f4XwGNhqDS0PdxtsMWLOMGqjwyxuya95AfjEdzAb22q5PKY7+HitfTjOotB2w7kO1B9v7bs3joH23eolM71ZX2otA82CPQ7RE7ox1ysW9jbcZYFlqSTCa64NcrNAEGuIJH2sB2RfqS2LUJrycvwNBYPrCT+XxVB5fKY9DEvnp56c6eaqTU3UiGJ5C7WzokjibTuWvpFB4XQs05PAhFgWFkOoqUBG95l1qkUtCL5u/WUhHEwdSwUkFQExWU3MdWu3VTksXPUb/hmBFvHECVsdUb9J4c5hgrSco8pN9zYVJM5ZfC+Km3+4befQGctgw1yGfTIwr0Gxc4Lypnef2gT9vREB1AJDBFr2WoKfwiEYXnxVGI/m5nE4vJJDZfTsrT2FzVtL2DGt9jv9GT3e0DJopnFviB2Zf0paMqyuyvDf+NdCI/EjQD4xrbdf/B2j0lh6JeO1dGQsZDKCLsSo+CL90odrXxlDbYhL6a5KMe4Gs/3i1r/g//56nShPOej9ekv74glXd8cMh00zHOh8Pz8gH4+R40se5e2d4XDg6nTCbzn49mz47m/0+v7jMLy/y2W/n02cX/8g51nbl0iZ7J15JQNUL9Or59WspH/Khi9x29iCY840OIqdq77a6pKDIlrXTloNaOa94Q6p0/JZYJVwR/Wt2rl7z1zQqtF3GC/Qhk19VINPaeU37sYlLFeJycORwfuncrbbrkCm0pYqBvMLIG7LcN5NkHI1xdyEtB8VO1eSFsNTAAEGFWGwUdjLK01oH7pYSsHFrbTNldODR2U6KJAXK0l0/W5KhA6EDlaOdmc5xFeu0VHYsp5a7Y+M2pTpTgdAXmz2siFV4S8m3ypVkMlV4wtHSqZg9EBiDlzhN1vopFRg5hi4FtiBjvoWcvNgn8kQOlLaFiaWs1hjCnfOl8hSIQ1KlCrVRW/J6tU+aVGrtAleYrgH906ZjXpWoVw3cq06x9HCK07WzNqitYCYyaXpivgGsNUhTkgdVNhzTABnkB2d2kYHwryg0zRIDffSmbWX6cyQvnWUx0m5i8FIH+S6H1nbPt+HxBD+87+81PyrITvvcT6LdJXY3UUaQwS3tjp977aLNYENYphbX9AIvOltnc4EZAR482oQWh/51/XEuRNe/9aTCIAePd/KMxLvOvuvSny5nMteAQbuOuBbZDjK1/Hh0Y7jHosnRkztsmk5i7m7Jtu2wYZax7LVt/wOHU0Ii
sidebar_class_name: "put api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminCustomersByid"}
>
</Heading>

<MethodEndpoint
  method={"put"}
  path={"/api/admin/customers/{customerId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminCustomersByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["contactNumber","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true}},"additionalProperties":false,"title":"UpdateCustomerDetailsDTO"}},"text/json":{"schema":{"required":["contactNumber","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true}},"additionalProperties":false,"title":"UpdateCustomerDetailsDTO"}},"application/*+json":{"schema":{"required":["contactNumber","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true}},"additionalProperties":false,"title":"UpdateCustomerDetailsDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      