---
id: login-account
title: "loginAccount"
description: "loginAccount"
sidebar_label: "loginAccount"
hide_title: true
hide_table_of_contents: true
api: eJzlVk1z2zYQ/SuYPbaw5XimF56aJs2MO57GU6vtwaPDilyRiEEAwQJyVA7/e2dBUZLlpPU9F4lcvH37veAAPlDEZLy7aaAC61vj3ta1zy6BhoQtQ/UAs2SlIdLnTJx+8c0OqgFq7xK5JI8YgjV14Vp8Yu9ExnVHPcqT6JlIjdBRj8aChoDMTz42wpt2gaACv/5EtZgOUTxLhli0J41qgN64W3Jt6qB6c1DiFI1rQcPGxx4TVHv8eGLi9boHlXHUgE1jJCK0dycObdAyaUgmWeG4lay9X34sGom+pO85/tM2+OHH7zYRohOJg3c8eX59dSV/DXEdTRAqqOA+1zUxFwtMdY4m7aB6GGBNGClC9bAaV/pM6WxIe0qdl+ENnkvCUAKDBQaz2KMWxTEQG3FLkYuJHC1U0KUUuFosrK/Rdp5TNQQf0wjnVm8FoCYC0LDFaHBtp9hEYwpug9lK7n66unojWdDndjaRuLtoaHuBwVziPznSE63ZJOJLR+mF2fe0JetDTy7Nxr9FmjG9kvQ+YWtc+3+E1mzplYx30Te5lpcD6epY0nvp/ilVc2GHue/EpFSmQKCaAXr/8GHuxt/+XpY2kYn647iEf/2CfZDmO8wGZKb4M03yy9r3pwN26PNRg3EbXxzZd+8HCVq9w6je3t1IiSnyFN32+kXAy86w4FSIfmsaYkWuCd64xGrjo0odqcan3ympwivQv64v1U36lkaPbqpKjZG1/KoaE7U+GprfbV4rzuuDI6fytfePxrWsFbpGSRIU5tSRS/t1VIyjtf6JyzGr5FWgKAOvDvcgK851p3DCqEit4TQdFeIyfVpZw+no7IQiyaxy9LSXNmTpBHSi8iwyOzmucihHDSU0ltV699y4y/2aolZMGOtuphVYj49UfOt9Q1arOhIeLX0tZy8Ax+Tl0Exne5HihCnzVAJXk7X/xVy8mAv5lRoo42qbGzmde1JFYkpcVEsLqy1Fs5mLVpa359RjuUgclikpVVDHJfisOQ+zVe7iYNE4YSkjPuz34wNgMKDhSDHtyJUG2YICGIY1Mv0Z7TiK+HOmKMt5dVx+ZT9r6AibsqwHeKQdVPBu+iq6WIobArdZ3HnxhSQ74rC+7z7eL2Xs919WUkuoIOIT6PJbAWjwU6LLRSqyASy6NmMr2Imz3FZZYjzk4WynFE/3R+h2Jx4Ow4RY+kdyo1wCU0BJ3mFcjeP4Lz2slJ4=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"loginAccount"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/Login"}
  context={"endpoint"}
>
  
</MethodEndpoint>



loginAccount

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["email","password"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"password":{"minLength":1,"type":"string","format":"password"}},"additionalProperties":false,"title":"LoginDTO"}},"text/json":{"schema":{"required":["email","password"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"password":{"minLength":1,"type":"string","format":"password"}},"additionalProperties":false,"title":"LoginDTO"}},"application/*+json":{"schema":{"required":["email","password"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"password":{"minLength":1,"type":"string","format":"password"}},"additionalProperties":false,"title":"LoginDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      