---
sidebar_position: 2
---

# Migrating Existing Documentation

This guide helps you migrate existing documentation from the `docs/` directory to the new Docusaurus-based system.

## Overview

The Fresh Car Core project previously stored documentation as standalone Markdown files in the `docs/` directory. The new system integrates these docs into a searchable, navigable Docusaurus site served through <PERSON><PERSON> with authentication.

## Migration Status

### ✅ Already Migrated

- **README.md** → `intro.md` (Getting Started)
- **DEPLOYMENT_GUIDE.md** → `deployment/overview.md` and `deployment/rollback.md`
- **car-api-v3.md** → `api/car-management.md`

### 📋 Remaining Files to Migrate

The following files from the original `docs/` directory still need to be migrated:

```bash
docs/
├── README.md                    # ✅ Migrated to intro.md
├── DEPLOYMENT_GUIDE.md          # ✅ Migrated to deployment/
├── car-api-v3.md               # ✅ Migrated to api/car-management.md
├── other-files.md              # 📋 Need migration
└── legacy-docs/                # 📋 Need review
```

## Migration Process

### Step 1: Identify Content

1. **Review existing files**
   ```bash
   ls -la docs/
   ```

2. **Categorize content**
   - API documentation → `docs-site/docs/api/`
   - Setup guides → `docs-site/docs/setup/`
   - Deployment info → `docs-site/docs/deployment/`
   - General guides → `docs-site/docs/guides/`

### Step 2: Convert Format

1. **Add frontmatter**
   ```markdown
   ---
   sidebar_position: 1
   title: "Custom Title"
   ---
   
   # Your existing content
   ```

2. **Update internal links**
   ```markdown
   # Old format
   [Link](./other-file.md)
   
   # New format
   [Link](/docs/category/other-file)
   ```

3. **Fix image paths**
   ```markdown
   # Old format
   ![Image](../images/screenshot.png)
   
   # New format (place in docs-site/static/img/)
   ![Image](/img/screenshot.png)
   ```

### Step 3: Update Navigation

Add new pages to `docs-site/sidebars.ts`:

```typescript
const sidebars: SidebarsConfig = {
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: 'Your Category',
      items: [
        'category/new-page',
        'category/another-page',
      ],
    },
    // ... existing categories
  ],
};
```

### Step 4: Test and Build

1. **Test locally**
   ```bash
   npm run docs:dev
   ```

2. **Build for production**
   ```bash
   npm run docs:build
   ```

3. **Test in Laravel**
   ```bash
   # Start Laravel development server
   php artisan serve
   
   # Visit http://localhost:8000/admin/docs
   ```

## Content Enhancement Opportunities

When migrating, consider these improvements:

### 1. Add Code Syntax Highlighting

```markdown
# Before
```
php artisan migrate
```

# After
```bash
php artisan migrate
```
```

### 2. Use Admonitions

```markdown
:::tip
This is a helpful tip for users.
:::

:::warning
This is a warning about potential issues.
:::

:::danger
This is critical information.
:::
```

### 3. Add Mermaid Diagrams

```markdown
```mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
```
```

### 4. Create Tabbed Content

```markdown
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs>
  <TabItem value="staging" label="Staging">
    Commands for staging environment
  </TabItem>
  <TabItem value="production" label="Production">
    Commands for production environment
  </TabItem>
</Tabs>
```

## Legacy File Cleanup

After migration is complete:

### 1. Archive Old Files

```bash
# Create archive directory
mkdir docs/archived

# Move migrated files
mv docs/DEPLOYMENT_GUIDE.md docs/archived/
mv docs/car-api-v3.md docs/archived/
```

### 2. Update References

Search for any references to old documentation paths:

```bash
# Search codebase for old doc references
grep -r "docs/" --include="*.php" --include="*.md" .
```

### 3. Add Redirect Notice

Create `docs/README.md`:

```markdown
# Documentation Moved

📖 **Documentation has moved to the integrated documentation system.**

## Access Documentation

- **Authenticated Users**: Visit `/admin/docs` in the application
- **Local Development**: Run `npm run docs:dev`

## Old Documentation

Legacy documentation files have been migrated to the new system.
Archived files can be found in `docs/archived/` if needed.

## Contributing

To update documentation:

1. Edit files in `docs-site/docs/`
2. Run `npm run docs:build` to build
3. Documentation will be available at `/admin/docs`

See [Documentation System Guide](/admin/docs/guides/documentation-system) for details.
```

## Migration Checklist

Use this checklist when migrating documentation:

### Pre-Migration

- [ ] Inventory existing documentation files
- [ ] Identify content categories
- [ ] Plan navigation structure
- [ ] Review content for updates needed

### During Migration

- [ ] Add proper frontmatter to each file
- [ ] Update internal links
- [ ] Fix image and asset paths
- [ ] Add to sidebar navigation
- [ ] Test local build

### Post-Migration

- [ ] Test authentication and access
- [ ] Verify all links work
- [ ] Check responsive design
- [ ] Update any external references
- [ ] Archive old files

### Quality Assurance

- [ ] Content is accurate and up-to-date
- [ ] Navigation is logical and complete
- [ ] Search functionality works
- [ ] Mobile experience is good
- [ ] Loading performance is acceptable

## Getting Help

If you encounter issues during migration:

1. **Check Build Logs**
   ```bash
   cd docs-site && npm run build
   ```

2. **Test Individual Pages**
   ```bash
   npm run docs:dev
   ```

3. **Validate Markdown**
   - Use a Markdown linter
   - Check frontmatter syntax
   - Verify link formats

4. **Review Docusaurus Documentation**
   - [Docusaurus Docs](https://docusaurus.io/docs)
   - [Markdown Features](https://docusaurus.io/docs/markdown-features)

## Best Practices

1. **Maintain Consistency**
   - Use consistent heading levels
   - Follow naming conventions
   - Keep similar content structure

2. **Optimize for Search**
   - Use descriptive titles
   - Include relevant keywords
   - Add meta descriptions

3. **Keep Content Fresh**
   - Review during migration
   - Update outdated information
   - Remove deprecated content

4. **Test Thoroughly**
   - Verify all functionality
   - Check on different devices
   - Test with different user permissions
