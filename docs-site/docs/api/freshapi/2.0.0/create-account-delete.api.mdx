---
id: create-account-delete
title: "createAccountDelete"
description: "createAccountDelete"
sidebar_label: "createAccountDelete"
hide_title: true
hide_table_of_contents: true
api: eJyNVMFu5DYM/RWBZ20mDdCLb4suFkhR7AZI2h6COdA2ZyxEllSSmnRq+N8LyZ6ZbJJt9zIekY+PFCm+CRT3As0jfOy6mIPC1gKTpBiEBJoJbq6vy6cn6dgldTFAA/e560gE5tmCUJfZ6RGaxwlaQiaG5nE7by3ERIwl5LaHBjomVFrzfCJPSmBfEb+PGUmHWChSFAULCXWABjaY3GbFbs5gIT4QSy0ns4cGBtUkzWbjY4d+iKLNlCLr/Cb7bwVgFgKwcEB22PqlDyViacQOs1do4Ofr659gnmf7Os+OSYYPPR0+YHJX+E9meqZWnJJcBdI3aT/RgXxMIwU9Jf8eaUb9QdJ7xb0L+/8j9O5AP8h4x7HPXTmcSbeX8d93A41Lq06PYAI9JlpTlslUCDQngF3/fI48Yunor38+1Cflwi7WcKe+4D+XUs0vyObj3W0ZDLEsNR1u3pT5MDgpOJM4HlxPYij0KbqgYnaRjQ5k+qhfSE3lLdA/bq7MrX4vYsSw9LJDFlt+TYdK+8iOTmefWyO5PRfy0t7G+OTCXqzB0JssxAazDhTUdXU9anL0Pj5LdYvRaBLxLvJozkskRnI3GFwwhmnvRBdXJfZx74I13oleil1QxMUS6Hm19mVZLqAXId/czC+Fm5yqqydF58W0x2+Thzy2xNYIIXfDibbARnyiWtsYe/LW1PU+Z3qvZ28Al+bl1C++1WREUbMsIwgdef9fzLWK0yDfmYFxofO5L96EIs+Re8MkpFJDaUTnzYHY7U5Dg9lWQRoxlMcasL7tRcHMKkvmfZ07b4bS37pJHl0obHVBp1XeHgGTA3sWZgsr19ZCEbGCmKYWhX5nP8/F/FcmLjq8vWhXleIXCnr39f4BLJS7vyjj1UJW7VxdGI5VCn0up2laEA/xicJcFPSJjuUe5Qzzdp7nfwGHaj5G
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAccountDelete"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/Delete"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAccountDelete

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      