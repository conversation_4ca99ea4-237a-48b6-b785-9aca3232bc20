<?php

namespace Database\Seeders;

use App;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Modules\common\Database\Seeders\ModulesSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([PermissionSeeder::class]);
        $this->call([FeatureSeeder::class]);
        $this->call([CarCategorySeeder::class]);
        $this->call([ServiceGroupSeeder::class]);
        $this->call([PackageSeeder::class]);

        if (App::environment() !== 'production' ) {
            User::updateOrCreate(['email' => '<EMAIL>'], [
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'password' => Hash::make('Password@123'),
                'email_verified_at' => now(),
                'remember_token' => Str::random(10),
            ])->assignRole('super_admin');
        }

        $this->call([ModulesSeeder::class]);
    }
}
