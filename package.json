{"private": true, "name": "freshcar-lbp-admin", "scripts": {"dev": "vite", "build": "vite build", "docs:dev": "cd docs-site && npm run start", "docs:build": "./scripts/build-docs.sh", "docs:serve": "cd docs-site && npm run serve", "build:all": "npm run build && npm run docs:build"}, "devDependencies": {"@tailwindcss/forms": "^0.5.2", "alpinejs": "^3.12.2", "autoprefixer": "^10.4.14", "axios": "^1.8.2", "laravel-vite-plugin": "^0.7.8", "postcss": "^8.4.31", "tailwindcss": "^3.3.2", "vite": "^4.5.5"}}