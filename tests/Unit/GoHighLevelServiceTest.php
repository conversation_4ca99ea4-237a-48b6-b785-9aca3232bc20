<?php

use App\Services\FreshcarApi\IFreshcarApiRepository;
use App\Services\GoHighLevel\GoHighLevelRepository;
use App\Services\GoHighLevel\GoHighLevelService;
use Illuminate\Support\Facades\Log;

beforeEach(function () {
    $this->mockGoHighLevelRepository = \Mockery::mock(GoHighLevelRepository::class);
    $this->mockFreshcarApiRepository = \Mockery::mock(IFreshcarApiRepository::class);

    $this->service = new GoHighLevelService(
        $this->mockGoHighLevelRepository,
        $this->mockFreshcarApiRepository
    );
});

describe('GoHighLevelService', function () {
    test('book method is deprecated and logs warning', function () {
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('warning')->once()->with(
            'GoHighLevelService::book() is deprecated. Use OpportunityStatusUpdateHandler directly.',
            ['data_keys' => ['payload']]
        );

        $data = [
            'payload' => [
                'status' => 'won',
                'email' => '<EMAIL>'
            ]
        ];

        // Should not throw any exceptions
        $this->service->book($data);

        expect(true)->toBeTrue();
    });

    test('book method handles empty data gracefully', function () {
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('warning')->once()->with(
            'GoHighLevelService::book() is deprecated. Use OpportunityStatusUpdateHandler directly.',
            ['data_keys' => []]
        );

        $this->service->book([]);

        expect(true)->toBeTrue();
    });

    test('book method logs data keys for debugging', function () {
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('warning')->once()->with(
            'GoHighLevelService::book() is deprecated. Use OpportunityStatusUpdateHandler directly.',
            ['data_keys' => ['payload', 'metadata', 'timestamp']]
        );

        $data = [
            'payload' => ['status' => 'won'],
            'metadata' => ['source' => 'webhook'],
            'timestamp' => '2024-01-01T00:00:00Z'
        ];

        $this->service->book($data);

        expect(true)->toBeTrue();
    });

    test('service can be instantiated with dependencies', function () {
        expect($this->service)->toBeInstanceOf(GoHighLevelService::class);
    });
});
