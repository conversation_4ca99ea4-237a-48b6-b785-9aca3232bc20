#!/bin/bash

# Clean up temporary IAM permissions for Cloud Run Jobs deployment
# This script removes conditional IAM bindings for the tlg-devops service account

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-$(gcloud config get-value project)}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo ""
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if jq is available
check_prerequisites() {
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed"
        log_info "Please install jq: brew install jq (macOS) or apt-get install jq (Ubuntu)"
        exit 1
    fi
}

# Clean up conditional IAM permissions
cleanup_conditional_iam() {
    log_header "Cleaning up conditional IAM permissions"

    local service_account="tlg-devops@$PROJECT_ID.iam.gserviceaccount.com"
    local roles=(
        "roles/cloudsql.client"
        "roles/secretmanager.secretAccessor"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
    )

    local cleaned_count=0

    for role in "${roles[@]}"; do
        log_info "Checking for conditional bindings for $role..."
        
        # Get all conditional bindings for this service account and role
        local conditions=$(gcloud projects get-iam-policy "$PROJECT_ID" --format="json" | \
            jq -r --arg sa "serviceAccount:$service_account" --arg role "$role" \
            '.bindings[] | select(.role == $role and (.members[]? == $sa) and .condition) | .condition | "expression=\(.expression),title=\(.title),description=\(.description)"')

        if [[ -n "$conditions" ]]; then
            while IFS= read -r condition; do
                if [[ -n "$condition" ]]; then
                    log_info "Removing conditional binding: $condition"
                    if gcloud projects remove-iam-policy-binding "$PROJECT_ID" \
                        --member="serviceAccount:$service_account" \
                        --role="$role" \
                        --condition="$condition" \
                        --quiet 2>/dev/null; then
                        ((cleaned_count++))
                        log_success "Removed conditional binding for $role"
                    else
                        log_warning "Failed to remove conditional binding for $role (may have already expired)"
                    fi
                fi
            done <<< "$conditions"
        else
            log_info "No conditional bindings found for $role"
        fi
    done

    if [[ $cleaned_count -gt 0 ]]; then
        log_success "Cleaned up $cleaned_count conditional IAM binding(s)"
    else
        log_info "No conditional IAM bindings found to clean up"
    fi
}

# List remaining permissions for the service account
list_remaining_permissions() {
    log_header "Remaining permissions for tlg-devops service account"

    local service_account="tlg-devops@$PROJECT_ID.iam.gserviceaccount.com"
    
    local remaining=$(gcloud projects get-iam-policy "$PROJECT_ID" --format="json" | \
        jq -r --arg sa "serviceAccount:$service_account" \
        '.bindings[] | select(.members[]? == $sa) | 
        if .condition then 
            "\(.role) (conditional: \(.condition.title))" 
        else 
            "\(.role) (permanent)" 
        end')

    if [[ -n "$remaining" ]]; then
        log_info "Remaining IAM bindings:"
        while IFS= read -r binding; do
            echo "  - $binding"
        done <<< "$remaining"
    else
        log_info "No IAM bindings found for the service account"
    fi
}

# Main function
main() {
    log_header "Cleanup Deployment Permissions"
    echo "Project: $PROJECT_ID"
    echo ""

    check_prerequisites
    cleanup_conditional_iam
    list_remaining_permissions

    log_header "Cleanup Complete!"
    echo ""
    log_success "Temporary deployment permissions have been cleaned up"
    echo ""
    log_info "To set up permissions for future deployments, run:"
    echo "  ./scripts/setup-deployment-permissions.sh"
}

# Show usage
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    echo "Usage: $0"
    echo ""
    echo "Clean up temporary IAM permissions for Cloud Run Jobs deployment"
    echo ""
    echo "Environment variables:"
    echo "  PROJECT_ID        Google Cloud project ID (default: current gcloud project)"
    echo ""
    echo "Examples:"
    echo "  $0                # Clean up all conditional permissions"
    echo "  PROJECT_ID=my-project $0  # Clean up for specific project"
    exit 0
fi

# Run main function
main
