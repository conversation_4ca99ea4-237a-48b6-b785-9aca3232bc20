---
id: lookup-car-by-registration
title: "Look up car details by registration number"
description: "Public endpoint that doesn't require authentication. Checks the DVLA for car details."
sidebar_label: "Look up car details by registration number"
hide_title: true
hide_table_of_contents: true
api: eJzFWVtvG7cS/isE85AEWEuOk6anenNlu8eFHRuWkqKIjGZ2d7TLmktuebGtCPrvB0PuyqtLIgVwcfwSeznzcTic+WaGmXNdowEntDrP+YBLre98PQTz6+wGC2FdXOMJd1BYPvjMh2DYRZDitwnP0WZG1EFmwK99KkXGUOW1FsoxV4JjuUarXjpm8B8vDDLwrkTlRBaQe2xYYnZnmSuRnXy6OGZTbVgGhuXoQEjb4wmvwUCFDg1ZMOcKKuQDbjoGfvBVioYnXJAdNbiSJ7zZMecDZzwm3GYlVsAHc+5mNSFYZ4Qq+GJxS8K21sqipfWjw0P6Z/V0wyejmEFnBN5jzqzPMrR26qWc8YRnWjlUjrShrmVzyv7fliDmmybo9G/MHJ3R0E04EQ3Iwe0htcUDG4dbv6NPWIpMIuvqMhWVFwmv4A6PVX6pc5S7wYa6SoXCnJEaA5WzihSZnob7vI97tbi78S4JZovyftZcfmPvDBwW2sz2d85Sg7S11P4HHNvILxI+Q9hD708EQ0ZXoPwUMudNMHrqUY6D4i4AkiIA0mDeYs7S2boLUBVC4Uh83QPvNMgyK74iE4plGQE4A8pWwloRA3mHSR1pFkTDLbrTx1qY2Qm4rWZMtanA8QFFP25gkhZ7KFGFs11ejRkSGtpgHjyeeHwO4MZpzMEjE5blPoaQPjptzmP3SIurI4atOPmw6N9VjQv+K6xbjcVlcq9F89WYlVGYER0EYuxe6/fY4Fl44P+Vr1NhrPtoMX/O+3wAywJwSJHnz7DaiArMbLgnW1xH8YYttjihey/P7YeIjSb6oUM8z71RBzp/FhoqwV55Zx2oXKjiBjOQewTaucqpEKNlYsXPrATLQDH9BMlMxCROQeu6yQ7GwGwD+0JYR9dH6Ro1Ei4cVnZ3+Sbx5/A44QR3Z7qqJbroa/p6g9ZLt9tBUa6NwoD36vp4NDo9Ydqws+Pzi9OT1+EC/y0C17kOHd4nkH6P4LhqxJlBCNcGLpouKuweowv9UYk9XEFSLYJe3+XVXUUOqcTrhs3HaN2+nVcbIR2WnYKQ3uD+UWYQLNUUqgWN8g/E24+RXQMfQulxD8+dPP3VOrCDkYMq0GjftTPVWiKo3fna4ISCvMRZkGXCSQylJFzFWbvflrWNj00lPqFWu7s0BBO/JbzCfdpwCjrroKp35sQBiW56vNVv3dbOImSC1d5ke1zbKMi1CPn6oW4ayEvcPG0c526WmwZHvTt8t30EUtqxqfYqf75pp0Jrodh6SHwE4rSNvRfxJ+EWM2+Em/HB59twYaWmQbbAsA0NggPev3/bh1r0MzAHccLtd8trf745SC04QZv7dub0RvIBL52r7aDflzoDWWrrBvNaG7fYzFcSYBGAJ/wejIBUxtOSRnTtFAI78zeHbw5DeK7vk+M9Sl1XqFxvatCWGZhepnv+bkv2LUXbfTfxoBYHtCgyPHhz+Mt/fnr//ujdu5/fHvbQ040cPKB1Rz3jVQ/qur8ZZA4K4sHvbbDL0Gujc58Flmhhbp+ucUQhE/2UIpgVXqVNeDPGB/qIAknzy1mbab//Md7Y9fc/xuzYu1Ib8TX2uiVCjoZ5SweinPk1oLAI32OnMfIG7OWK3qCVmzt9h2rxMmSTUFMdqSBm1Rn5gFHIHl+fUwCgiZMTf9s77B3yhD8eSF0EnejBvqiKfuu6sNarQwakkN0VhoKeekvDB/zFWfjhCQfpxoGb1zZcrB//r3EpLC2FlidFVKyJLcyZ08xgLSHDyB3afUDHAiJpfDpKwmgvbMi/GTrqx7ptcCYFhR3Ute39NVET9eIFG/mKGtyJWu5cG30vcrTL96FYxipQMagyMDYJTz/N+C2w/Vv6dKKsT5dH6iywVOs7oQobrfQWzcYb07ljIKV+sGHZTpTTrEZD3MyWD2CW3nJKBlFodTAiZKkLoRImhXVLcyeq7aXpk8KH5hQ5SnyS6uqsHE5G05mvaWmi2ueldLZtLkuYRTBZ2eKS2OrDS8Iyg0BbTdTSP+t+ayS2OdDXeVxrPjV3OTTa2oMrIwqh2A3GksRGJYRTvxpe3Yxedy7a+po4zu6lF9/7gmCuKxAqPBRSI91rdj9eucyJOpayE0LbnxWbtL4AA/co2QhU5nzVY+cqkz6PcR5Zg4U0poimb9soYjBRX758mahv0MBMe3MQuaARbAojZIHmm9fKcYnsQhSlU2TYb0Z74rJV9nx4eOi5EmUrRmlfNzS6SLgUGVKBfgK9PB9vgOgaVfR0T5ui3yjZPsmGXs5U9mo6ilWgo1cIV/q0l+mqH0osGlD50pQONaX1QaYN9lOp0z7dWP/ifHj6YXQaWM1QmyNFJdxm+0DBQessrJMfBLHBVJss0hAqS21eU6KYdZAKKdwsRPgUBNE1FNgL9N5Uzzk3YRx5f5jwmuoFr4TyYf5IZ9RMXjPIc4PWUuseUvv8JM4hnZiJM1NI8SfIn3Yikl1YgZA0e6VCxceX0JjU2roKVOe2qM9qUp19P9PXy9f8qdP61x7ZmyJLvX6/liAUOSSE1rzpoz7z+7dUdGpBEb7spfjqYwVP+GDLw/RtwqlhIpT5PAWLH41cLOjzPx5N0761fVJouXJh6fecD6YgLX7HJa9umpf+1+xbB2mnKzUL7VgYMjlP+B3Otv9XwuKWnhsCAQRrouBxlmHtOhAbbe+i24X+djrmi8X/AJaY6H4=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Look up car details by registration number"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/car-lookup/registration/{registrationNumber}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Public endpoint that doesn't require authentication. Checks the DVLA for car details.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"registrationNumber","in":"path","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Car details retrieved successfully","content":{"application/json":{"schema":{"type":"object","properties":{"data":{"type":"object","properties":{"registrationNumber":{"type":"string","description":"Vehicle registration number"},"makeAndModel":{"type":"string","description":"Combined make and model of the vehicle"},"make":{"type":"string","description":"Make of the vehicle"},"model":{"type":"string","description":"Model of the vehicle"},"category":{"type":"string","description":"Vehicle category"},"colour":{"type":"string","description":"Vehicle colour"},"year":{"type":"string","description":"Year of manufacture"},"fuelType":{"type":"string","description":"Type of fuel used by the vehicle"},"engineSize":{"type":"string","description":"Engine size in cc"},"transmission":{"type":"string","description":"Transmission type"},"motExpiryDate":{"type":"string","format":"date","description":"Date when the MOT expires"},"taxDueDate":{"type":"string","format":"date","description":"Date when the vehicle tax is due"},"co2Emissions":{"type":"string","description":"CO2 emissions in g/km"},"motHistory":{"type":"object","description":"MOT history data for the vehicle","properties":{"registration":{"type":"string","description":"Vehicle registration number"},"make":{"type":"string","description":"Make of the vehicle"},"model":{"type":"string","description":"Model of the vehicle"},"firstUsedDate":{"type":"string","format":"date","description":"Date when the vehicle was first used"},"fuelType":{"type":"string","description":"Type of fuel used by the vehicle"},"primaryColour":{"type":"string","description":"Primary colour of the vehicle"},"registrationDate":{"type":"string","format":"date","description":"Date when the vehicle was registered"},"manufactureDate":{"type":"string","format":"date","description":"Date when the vehicle was manufactured"},"engineSize":{"type":"string","description":"Engine size in cc"},"hasOutstandingRecall":{"type":"string","description":"Indicates if the vehicle has an outstanding recall"},"tests":{"type":"array","description":"List of MOT tests","items":{"type":"object","properties":{"testDate":{"type":"string","format":"date","description":"Date when the test was completed"},"testResult":{"type":"string","description":"Result of the test (PASSED or FAILED)"},"expiryDate":{"type":"string","format":"date","description":"Date when the MOT expires"},"odometerValue":{"type":"string","description":"Odometer reading at the time of the test"},"odometerUnit":{"type":"string","description":"Unit of the odometer reading (km or mi)"},"motTestNumber":{"type":"string","description":"MOT test number"},"failures":{"type":"array","description":"List of reasons for failure","items":{"type":"object","properties":{"type":{"type":"string","description":"Type of failure"},"text":{"type":"string","description":"Description of the failure"},"dangerous":{"type":"boolean","description":"Indicates if the failure is dangerous"}},"title":"MotTestFailure"}}},"title":"MotTest"}}},"title":"MotHistoryData"}},"title":"CarData"},"meta":{"type":"object","properties":{"timestamp":{"type":"string","format":"date-time","description":"Timestamp of the response"},"source":{"type":"string","description":"Source of the data"}},"title":"ResponseMeta"}},"title":"CarLookupResponse"}}}},"404":{"description":"Car not found","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","example":"Car not found"}}}}}}}}
>
  
</StatusCodes>


      