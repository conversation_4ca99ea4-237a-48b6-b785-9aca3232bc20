---
id: update-admin-allbookings-enquirystatus-byid
title: "updateAdminAllbookingsEnquirystatusByid"
description: "updateAdminAllbookingsEnquirystatusByid"
sidebar_label: "updateAdminAllbookingsEnquirystatusByid"
hide_title: true
hide_table_of_contents: true
api: eJztV0tz2zYQ/iuYPfUBW7JaT1rebCeZupNJPLbSztSjw4pciYhBgAGWclQO/3tnQerlR+pjDrlIJLD77RPfgi0wLiNkt3Bm7bn3d8Yt41lRGQczDTUGrIgpiEQLDiuCDOa92GUBGoyDDGrkEjQE+tyYQAVkHBrSEPOSKoSsBV7Xomgc05ICaFj4UCH3S79MoOtmvTpFPvfFWnRy75gcyyPWtTU5svFu9Cl6J2s78J3ZWyAnz+sbRm6iRDBY9vNPlDNoqIOvKbChKKqH4lkLlXHvyC25hOxkqxw5GLeETotPCxMqKl4j015gg8ReXAUyHbGpCDS4xlqcW+rzIjDocrI2BXRNGL27LF6SphdBvffcB/fQtwe6nQYsCiOKaK/20rJAG0kDGxZZ+FhLLENrvNlP2OvpBxAYpi/8vS7fXl32j81PP38v0DdWIAEKFGvvYm93Mh7LX0ExD6YWfMjgpslzijEVNFLeBMPrRMZzwkABstuZkKd4kWKRhECTTCcaP7N24Os4+BCTD+drI/x9aOzlehVx6Yue+3Mh/zQDMhhhbUYoACPcIYxoH2LUbidIBxJVWG0mTBMsZFAy1zEbjazP0ZY+ctbWPnD3yN93IqB6ANCwwmCkUCmbotGnc4GNlV44HY9PJO/6oZ1FoFgeFbQ6wtoc479NoHuaR8MUjx3xI7OvaUXW1xU53hh/DrRBfiHoDePSuOX/AVqzohciXgVfNLm8bEFnuya6ER7oU7VppW3ri0kYBnia+L2AHh7ebk7Xn39PU2MKt1zvxvebL1jVlp4git2hesAIMBlPfj0avzqa/D49Oc1OT7LJb8fjVyf/wHOnfvyVM7zHPMYtfIpsOJVvJYvqAoM6u7qUnqEQ+3StJo8yOC1NFDlVB78yBUVFrqi9cRzVwgfFJanC83tilXBF9K/Jsbrk5zQqdH2ZcwxRy6/KkWnpg6HNu23mKjbzrSP765sTpRW6QjWRgsKGS3I8MH0yjtb6+5i2o2KvagrCiGrLElHFJi8V9jIq0NJE7rcSsPVL47SyJvLO2V6KJLPK0f2wWpClPaE9lYPIbO+4auq0VRCjsVHN14fGXVPNKWgVCUNebmBFrMI7Sr5VviCrVR4Id5aeytkjgV3yEtHJ3rCkembqS5Ca6mvIyYtNIZ+ogTIut00huzXGeO9DoQJF4phUqUJj1YqCWWyKJp1a+8gVphk9XLR7OlaJj9UeIasDRlZPUfn2KKfLWW3ROLGRGKUdyPoWsDYg00yu+xr2CBs0HFA2aMh21/6ZBmFlQWjbOUb6GGzXyfLnhoKMp9mOjBOvFybKc7Edkg+c3V724Yfr4VbyowL9dBDDIrp14nzbyBtouKP1wddJN+s0lIRFGpPtsH/RmzqaCspO/9E3hnDldspdnU0v/hD+Gz5OpAchg4D38t2D970Dvm+QdLeStRYsumWDS5HtQdPtoZHsbyv0gFyTq0+G2La9xNTfkeu6bcQs7xJt1/0HJ3LkYw==
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminAllbookingsEnquirystatusByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/allbookings/enquirystatus/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminAllbookingsEnquirystatusByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"text/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"application/*+json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      