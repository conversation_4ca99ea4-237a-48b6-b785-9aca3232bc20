---
id: create-account-emailavailable
title: "createAccountEmailavailable"
description: "createAccountEmailavailable"
sidebar_label: "createAccountEmailavailable"
hide_title: true
hide_table_of_contents: true
api: eJzVVk1z2zYQ/SuYPba05XimF97cfMy4k2k8tdoePDqsyBWJGAQY7EKOyuF/7yxI0YqdOLnmIpHA27eL/XjgAIINQ3kHV1UVkhfYFBDpUyKW30N9gHKAKnghL/qIfe9shWKDX33k4HWNq5Y61Ce1s5FqpaMOrVMyOfQEJYTtR6oECuhj6CmKJVaTCVYO0Fn/nnwjLZSvFiOWaH0D41gA1rVVt+huTgh26JgKECtO8W+V7WqP1uHWOiuHN+sP2Vros/xUAZ8m+pdff57I1T4S98Hz5PXy4kL/auIq2l5poYTbVFXEnL0xVSlaOUB5N8CWMFKE8m4zbgpQ5zkF1zWUUEVCoblNs3uc3DuC4omDl7EdSRuUsg+c84SaC1hhb1ezzer0fNmIKe4pcg4zRQcltCI9l6uVCxW6NrCUQx+ijM+iea8AMxFAAXuMVklzftRiStAOkxMo4beLi1eayeKpn10kbs9q2p9hb8/xvxTpgbZshfjckzxz+4b25ELfkZej82+RJpQfJL0VbKxvvkfo7J5+kPEmhjpV+rKQbh7b4lZbfkrVsTmGY8+qS61MhkB5BBTzw7sQO9SM/vHvOreajtFfj9r29jN2vTbzMhvLFBRg/S5kV3O/v9NjmdcYzdXNtRaRIk/x7y+fHWndWlac6WPY25rYkK/7YL2w2YVopCVTB/mTxGRehf5zeW6u5VsWHfop7xVGLvTXVCjUhGjp+O7S1nDaLoGcrm9DuLe+4cKgr01iigaTtORlVpnsHJ0LD5y32UgwPcVdiJ1ZBpENp6o1OGFMpMayTFuZ2IXG+sI4y/IY7IQizazx9DCv1uToBHRi8sXJ3BS4SX3eqknQOjbbw5fOfeq2FAvDhLFqj7QK6/CecmxdqMkVJkvD4ulrOXsGeExe6utpb14yLCiJpxL4ipx7iTlHcSzkV2pgrK9cqnW3R+aHEGsTiUk4m+YmNXuKdncsmnaqiliH+X7wmOdgUj8zS5l5WSuXacp3ZO/QemXNQz3M0ngH2Fsolm+EAp7I46YAFUBFDsMWmf6Obhx1+VOiqNq+edS9LO8FtIR11voB7ukAJbyevjPO1hqPwl3SuJ59c6g8LBJ+8+F2rRM/f6tokaGEiA9Q5N8SQC+SXIF8ceraAA59k7BR7MSZL76kh10S8kROcqTzFvrDSYTDMCHW4Z78qPo/HUj0HcbNOI7/A/fUU/g=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAccountEmailavailable"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/EmailAvailable"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAccountEmailavailable

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["email"],"type":"object","properties":{"email":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"EmailAvailabilityDTO"}},"text/json":{"schema":{"required":["email"],"type":"object","properties":{"email":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"EmailAvailabilityDTO"}},"application/*+json":{"schema":{"required":["email"],"type":"object","properties":{"email":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"EmailAvailabilityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      