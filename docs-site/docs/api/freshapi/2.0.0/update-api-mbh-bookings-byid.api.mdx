---
id: update-api-mbh-bookings-byid
title: "updateApi-mbhBookingsByid"
description: "updateApi-mbhBookingsByid"
sidebar_label: "updateApi-mbhBookingsByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVk1z3DYM/SscnPohe113etHNdpqJO03Hk7jtwbMHSMKuGFOkQoLrbDX67xmQktb2OmmOueyK5MMDCAKPHIBxG6C8g7f7S+futd2+iRWsC+jRY0dMXlYHsNgRlFBlzHUDBWgLJfTILRTg6WPUnhoo2UcqINQtdQjlALzvxVBbpi15KGDjfIecp349h3FcZ3MKfOmavdjUzjJZlk/se6NrZO3s6kNwVuaOyF31gWqGAnrvevKsKciqdZw/Jlhgr+0WCrDRGKwM5WjHsQBsGi0+0Nw8otigCVQAaxYsvK3aG+S6nRL1ih2ILdMn/l5je5y+n37+LoMUa0+hdzZkZ+dnZ/LXUKi97oUUSngf65pCSJsKVEeveZ8KsyL05KG8W0shieu02+sGSoh9g0wXvT7pqtlpuNxrqd6n9F9DdsSta3Kt11LsqeZLWGGGr6amCKthaY8RJEy/m9snegMltMx9KFcr42o0rQtcDr3zPB6F86cAVCaAAnbotaQ7pUcscn42GI000m9nZ79IIovnfjaeQnvS0O4Ee32K/0VPD1QFzRROLfGR21e0I+P6jizPzr9EGpG/kfQ941bb7f8RGr2jb2S88a6JtQwW0vWhKt5LcedUzbWxFLC4hEmdkpxlQDF9vJ6l6Y9/b1OlScO8O2jT75+w66WSl9aYW2IUOdy45Goq9teyLXWFXl3cXMshkg85/t350ZZuWx0Ep3rvdrqhoMg2vdOWg9o4r7gl1Tj+i1glXoH+c36qrvlLFh3anPcafSjkV9XItHVe0zw2sVIhVksgj+fnoi4U2kbFQF5h5JYsT3qSnKMx7iGk5aDYqZ686Lta+jCoEOtWYcYoT1sdOC8lYuO22hbK6MCHYDOKJLPK0sM025ChR6BHJk92ZnLgKvZpqSFGbYKq9k+d29hV5AsVCH3dzrQC6/CeUmyda8gUqvaEB08v5ewIcEheEhZZm6ZUYOQY8hHYmoz5GnOKYj7IF85AaVub2MhqjyE8ON8oT4E4JFPqUBu1I68386FJpfYucIfpJpiu9Sx/atI/NQugekkrl2ZK915vUFshTT09TNp4B5M2SmtNZFBAeXg+rAsQARToMFQY6G9vxlGmP0byIu3rg+4lCW10kO9muVWeRbU8GuCHd9Nr5EcFxcvRTpNo90leTZQRFHBP+yevnHE9FtASNumKGab1q+zq5FZYDvZHbxWRpeX2uLm4vXqT8pEfOVJdUILHB3k/4UMOwOWjF0CaG8Cg3UbcCjaTpus2SpqXo3imYynUF7c4DBlx6+7JjuOyY5ax7HYcPwOyz44c
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateApi-mbhBookingsByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api-mbh/bookings/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateApi-mbhBookingsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"notes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"MbhPatchBookingDto"}},"text/json":{"schema":{"type":"object","properties":{"notes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"MbhPatchBookingDto"}},"application/*+json":{"schema":{"type":"object","properties":{"notes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"MbhPatchBookingDto"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      