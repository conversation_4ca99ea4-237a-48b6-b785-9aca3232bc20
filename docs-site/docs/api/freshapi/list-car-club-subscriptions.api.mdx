---
id: list-car-club-subscriptions
title: "List subscriptions"
description: "This endpoint retrieves all car club subscriptions associated with the authenticated user."
sidebar_label: "List subscriptions"
hide_title: true
hide_table_of_contents: true
api: eJy1Vktz2zYQ/isY5JB2hiIVu0lb3lzHySjjJp7IbQ6WJwHBFYkxCCALQLKq4X/vLEhZkp02vdQXmdj369vd8iAaz8sbfi6QnetY8duM1+AlKheUNbzk163yDEztrDKBIQRUsALPhNZMCmRSx4r5WD3IeCa8t1KJADVbq9Cy0AITMbRggpLpOXrAnGfcOkBBQrOal1wrH84Fkh/zQ4U84wjeWePB83LLT6ZT+jn280jiwc+a+SgleL+MWm9432fcg4yowoaXN1tegUBAXt7c9rcZ7yC0ljxpIPCMOxFaXvJidVoIpwopcELRFv6Rcx5wBeiTwoial7wNwfmyKLSVQrfWh3LrLIaeP87uJTGwQQHP+EqgEpUewiSJIc6liDrwkr+Yvpjyvu+zx3ZqWIG2rgMT8iWCb6XAXNo83j2x+HrPurP7VJ9wakJEJWHyYvrrLy9fvTr56aefT6c5RLQOJmvw4STHaHLhXPHEyDyIRpnmXw18z9ErtHWU9PGg5nZfvblsoRvytKvhloeNg9EIlSWx8HLHkI3/vLHYCUrnu0/XT6y++3TNzmJoLaq/UmeyFkQNyKKngKiVf0ta2KA+Zxf3onMaSvb8SK7c8W2DvQPTP0/Np8zSJk9V0OTaG8oBo+k7u5pRAwD6wZHTfJpPecbvJ9o2SWbIYKG6ptilLtFyZxoKTsi7Bm009bnVFnnJn71JfzzjQodruA9PDPaPw/+cxv3sasZa4VkFYNjYW1CzYBmC00JCykNtw3sILGkkiT9PMiZMzZRnxga2gcCUoVGvWbVJElIrajvhnM8/L8zCPHvG5rHrBG4W5sGyQ7tSNexRx7OlRdYJMzSVFOizAXtEgMaigt23jtXCHI3nnsAqa++UafzgJUHQISopa3I2C4Rrdu0T2S9MsMwBLi127AGrPGFKy8TAxBAa5cNASpq1bZTJGKHZg7sLM7AB0pOB9RhFDRr2XIcyR8HpwXUWHZEWpoYglPaU1yPzJnYVYMY8CJTtTi+xdeIOknedrUFnTCIIMrUw3wbxPce3EhhdPdDGp7GW52i9n3xA1SjDPoK3ESWweStS1D+cf/g4//Gg0D46wjj/n+RSD8jEWNtOKMMQvkbwweej9bOjYi7MmdYHLUTcCuFRycexvhQoVqDZXBgZYpezmZE61kOfD6jB0hhTR9PbtyCiXJgvX74szD/AwMZGnAxYMDLyjEtrgpAJ5o1IYHXdArtUTRsMOfYWbSQsO0bP9Xqdhxb0jo3G3o0w2mdcKwnGw4HS32fXT5RYB2bIdG6xKUYhXxBvn/EA2PkPy/mwBQ7kGhXaWOXSdsWS0AZQmPrBlQNoqtxEWoSi0rYqqGLF5ez84v38IqEaigATrToVnu5yag6is0SnPKQbZGlRDjAExkcENq4o5oOolFZhkzp8KRTBtWggT/A+bs8tJ5W8fDXNuKN9wTtlYgCCzg0v+eyKibpG8J5ZHEZ79pqtWzDHx0vKMI34XuXL72okv6ATSjNpu0qZ1BtplXNnfeiEOajWpfKBPT4yjjK0TY0DJvzfB9q4UQPch8JpoQxFn/poO55HN3x1ShvGKWrn8USi7Xvk/23G6Qwi9u22Eh7+QN339Pw1AtItdru/ftI5dnCNvb2g7iUHD5b8o6Wejq+RJMwm3VI60td2O3Bcp9GjE+wOqDppFHl/2/f93+om5do=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"List subscriptions"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/car-club/subscriptions"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint retrieves all car club subscriptions associated with the authenticated user.

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Subscriptions retrieved successfully"}}}
>
  
</StatusCodes>


      