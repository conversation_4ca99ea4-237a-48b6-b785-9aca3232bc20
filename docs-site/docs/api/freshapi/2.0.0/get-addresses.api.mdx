---
id: get-addresses
title: "getAddresses"
description: "getAddresses"
sidebar_label: "getAddresses"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v3DgM/SsCz24mDdCLb8XutkhRFAWStodgDrTNsYXIkkpSk04N//eF5PlK0rS9jEfi43uUKL4JFHuB+g7edh2TCKwrYJIYvJBAPcHV5WX+dCQt26g2eKjhJrVtBs9zBUJtYqs7qO8maAiZGOq79byuIERizCnXHdTQk+5FSKB6wvgkOJIOYZ8EFUTUAWpYYbQrPIMJ8ZZYinRiBzUMqlHq1cqFFt0QROspBtb5meDHDDALAVSwRbbYuOXMOWM59AaTU6jhzeXla5jnuXqqs2GS4VVH21cY7QX+TEwP1IhVkgtfan8s+y9tyYU4kteD+EukCfUvSW8Ue+v7PxE6u6W/ZPzMoUttXhxJ16dW37QDjctVHRo+ge4i7SVzZwoE6gOg2v95F3jEfKMfvt2W52P9JpR0qy7j3+VSzT/I5u3n69wYYllq2l49K/N2sJJxJnLY2o7EkO9isF7FbAIbHch0QT+RmsKboV+vLsy1vpQxol/uskWWKv+aFpX6wJYOa5caI6k5FnK+34Rwb30vlUHfmSTEBpMO5NW2ZRSKODoXHqSExWgwkXgTeDTHgREjqR0MLhjD1FvRJVSIXeitr4yzoqdiFxRx3vH0sN/tyNEZ6Czl0cncUrhJsYQ6UrROTLN7LO7T2BBXRgi5HQ60GTbiPZXaxtCRq0zLhCelX93ZM8Dp8lLslth+y4iiJlla4Fty7nfMpYpDI3/RA2N961KXoxFFHgJ3hklIpaTSiNaZLbHdHJoGcwUxiI7o82P1WN52T2petLTjRCj90FV0aH1mKYM57R3tDjBaqODkaesKsmvl0DQ1KPSF3Tzn7e+JOJvs+mRWxWfPzPL9f7eZLGXqo/yTASxeuQ+h3xXrcymvpmlB3IZ78nN2zHva5frzGub1PM//A09mMa4=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAddresses"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/addresses"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAddresses

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      