#!/bin/bash

# Setup temporary IAM permissions for Cloud Run Jobs deployment
# This script grants time-limited permissions to the tlg-devops service account

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-$(gcloud config get-value project)}"
DURATION="${DURATION:-1}"  # Duration in hours

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo ""
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if service account exists
check_service_account() {
    local service_account="tlg-devops@$PROJECT_ID.iam.gserviceaccount.com"

    if ! gcloud iam service-accounts describe "$service_account" --project="$PROJECT_ID" >/dev/null 2>&1; then
        log_error "Service account $service_account does not exist"
        log_info "Please create the service account first or run the full setup script"
        exit 1
    fi

    log_success "Service account $service_account exists"
}

# Set up temporary IAM permissions
setup_temporary_iam() {
    log_header "Setting up temporary IAM permissions"

    local service_account="tlg-devops@$PROJECT_ID.iam.gserviceaccount.com"

    # Calculate expiration time (compatible with both Linux and macOS)
    local expiration_time
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        expiration_time=$(date -u -v+${DURATION}H '+%Y-%m-%dT%H:%M:%SZ')
    else
        # Linux
        expiration_time=$(date -u -d "+${DURATION} hour" '+%Y-%m-%dT%H:%M:%SZ')
    fi
    log_info "Setting IAM permissions to expire at: $expiration_time (${DURATION} hour(s) from now)"

    # Required roles for Cloud Run Jobs deployment
    local roles=(
        "roles/cloudsql.client"
        "roles/secretmanager.secretAccessor"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/cloudbuild.builds.builder"
        "roles/run.admin"
        "roles/storage.admin"
    )

    for role in secretmanager.secretAccessor cloudsql.client; do
  gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member=serviceAccount:tlg-devops@$PROJECT_ID.iam.gserviceaccount.com \
    --role=roles/$role
done"
    )

    for role in "${roles[@]}"; do
        log_info "Adding temporary $role to service account..."
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$service_account" \
            --role="$role" \
            --condition="expression=request.time < timestamp('$expiration_time'),title=Deployment-Access,description=Temporary access for Cloud Run Jobs deployment" \
            --quiet
    done

    log_success "Temporary IAM permissions configured (expires in ${DURATION} hour(s))"
    log_warning "Remember to run cleanup-deployment-permissions.sh after deployment"
}

# Main function
main() {
    log_header "Temporary IAM Permissions Setup"
    echo "Project: $PROJECT_ID"
    echo "Duration: $DURATION hour(s)"
    echo ""

    check_service_account
    setup_temporary_iam

    log_header "Setup Complete!"
    echo ""
    log_success "Temporary permissions are now active"
    echo ""
    log_info "You can now deploy Cloud Run Jobs with:"
    echo "  ./scripts/deploy-jobs.sh deploy"
    echo ""
    log_warning "Don't forget to clean up permissions after deployment:"
    echo "  ./scripts/cleanup-deployment-permissions.sh"
}

# Show usage
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    echo "Usage: $0 [DURATION_HOURS]"
    echo ""
    echo "Set up temporary IAM permissions for Cloud Run Jobs deployment"
    echo ""
    echo "Arguments:"
    echo "  DURATION_HOURS    Duration in hours for permissions (default: 1)"
    echo ""
    echo "Environment variables:"
    echo "  PROJECT_ID        Google Cloud project ID (default: current gcloud project)"
    echo ""
    echo "Examples:"
    echo "  $0                # 1 hour permissions"
    echo "  $0 2              # 2 hour permissions"
    echo "  DURATION=3 $0     # 3 hour permissions"
    exit 0
fi

# Parse arguments
if [[ $# -gt 0 ]]; then
    DURATION="$1"
fi

# Validate duration
if ! [[ "$DURATION" =~ ^[0-9]+$ ]] || [[ "$DURATION" -lt 1 ]] || [[ "$DURATION" -gt 24 ]]; then
    log_error "Duration must be a number between 1 and 24 hours"
    exit 1
fi

# Run main function
main
