<?php

namespace Tests\Unit;

use App\Models\CarCategory;
use App\Services\CarCategoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CarCategoryServiceTest extends TestCase
{
    use RefreshDatabase;

    private CarCategoryService $carCategoryService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->carCategoryService = new CarCategoryService();
    }

    public function test_get_all_active_categories_returns_only_active_categories(): void
    {
        CarCategory::factory()->create(['name' => 'Active1', 'is_active' => true]);
        CarCategory::factory()->create(['name' => 'Active2', 'is_active' => true]);
        CarCategory::factory()->create(['name' => 'Inactive', 'is_active' => false]);

        $result = $this->carCategoryService->getAllActiveCategories();

        $this->assertCount(2, $result);
        $this->assertTrue($result->every(fn ($category) => $category->is_active));
    }

    public function test_get_all_active_categories_returns_ordered_by_name(): void
    {
        CarCategory::factory()->create(['name' => 'Zebra', 'is_active' => true]);
        CarCategory::factory()->create(['name' => 'Alpha', 'is_active' => true]);
        CarCategory::factory()->create(['name' => 'Beta', 'is_active' => true]);

        $result = $this->carCategoryService->getAllActiveCategories();

        $names = $result->pluck('name')->toArray();
        $this->assertEquals(['Alpha', 'Beta', 'Zebra'], $names);
    }

    public function test_get_all_active_categories_returns_empty_collection_when_no_active_categories(): void
    {
        CarCategory::factory()->create(['is_active' => false]);

        $result = $this->carCategoryService->getAllActiveCategories();

        $this->assertCount(0, $result);
    }
}
