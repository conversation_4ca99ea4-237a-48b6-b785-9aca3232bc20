APP_NAME="Fresh Car Core"
APP_ENV=local
APP_KEY=base64:4opjLpi5m3V8xLssngSfVTZ/oCdqEf29yrymJ1qIdu8=
APP_DEBUG=true
APP_URL=http://*********
FRONTEND_URL=http://*********:3000
ALLOWED_ORIGINS=http://*********:3000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=pg
DB_PORT=5432
DB_DATABASE=postgres
DB_USERNAME=postgres
DB_PASSWORD=password

ELASTICSEARCH_ENABLED=false
ELASTICSEARCH_HOST=es
ELASTICSEARCH_PORT=9200

# MongoDB Atlas Data API Configuration
MONGODB_ATLAS_API_KEY=your_mongodb_atlas_api_key_here
MONGODB_ATLAS_APP_ID=your_mongodb_atlas_app_id_here
MONGODB_ATLAS_BASE_URL=https://data.mongodb-api.com/app
MONGODB_ATLAS_CLUSTER_NAME=your_cluster_name_here
MONGODB_ATLAS_DATABASE_NAME=freshcar_logs

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=public
TEMP_FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120
TOKEN_LIFETIME=120
USER_INVITATION_LIFETIME=2880

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

POSTMARK_TOKEN=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=eu-west-2
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_REGION=eu-west-2

AWS_BUCKET_S3=
AWS_KEY=
AWS_S3_FILE_ACCESS_AS=
AWS_S3_URL_EXPIRY_TIME=
AWS_SECRET=
AWS_BUCKET_URL=
AWS_BUCKET_S3_BACKUP=
S3_BUCKET_PATH=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

IGNITION_THEME=dark

STRIPE_KEY=your-stripe-key
STRIPE_SECRET=your-stripe-secret
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
CASHIER_CURRENCY=eur

API_RATE_LIMIT=60
BUGSNAG_API_KEY=your-bugsnag-api-key

GOHIGHLEVEL_CLIENT_ID=
GOHIGHLEVEL_CLIENT_SECRET=
GOHIGHLEVEL_REDIRECT_URI=
GOHIGHLEVEL_BASE_URL_AUTHORIZATION=https://marketplace.gohighlevel.com/oauth/chooselocation
GOHIGHLEVEL_BASE_URL_API=https://services.leadconnectorhq.com
GOHIGHLEVEL_PRIVATE_INTEGRATION_TOKEN=

FRESHCARAPI_URL=
FRESHCARAPI_ADMIN_USER=
FRESHCARAPI_ADMIN_PASSWORD=

DVLA_MOT_HISTORY_API_CLIENT_ID=<your-client-id>
DVLA_MOT_HISTORY_API_CLIENT_SECRET=<your-client-secret>
DVLA_MOT_HISTORY_API_KEY=</your-history-api-key>
DVLA_MOT_HISTORY_API_SCOPE_URL=https://tapi.dvsa.gov.uk/.default
DVLA_MOT_HISTORY_API_TOKEN_URL=<your-token-url>
DVLA_MOT_HISTORY_API_TENANT_ID=</your-tenant-id>
DVLA_MOT_HISTORY_API_URL=https://history.mot.api.gov.uk
DVLA_VES_API_KEY=<your-ves-api-key>
DVLA_VES_API_URL=https://driver-vehicle-licensing.api.gov.uk/vehicle-enquiry/v1/vehicles
