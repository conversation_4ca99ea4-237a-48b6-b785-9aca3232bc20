steps:
  # - name: 'curlimages/curl:7.85.0'
  #   id: 'Reviewee Check'
  #   entrypoint: 'sh'
  #   args:
  #     - -c
  #     - |
  #       echo "Checking for rejected commits..."
  #       result=$(curl -s https://api.reviewee.it/repository/rehuman-api/haveRejectedCommits)
  #       echo "Reviewee Response: $result"
  #       echo "$result" | grep -q '"success":true' || (echo "❌ Rejected commits found. Failing build." && exit 1)
  - name: "gcr.io/cloud-builders/docker"
    id: "build-image"
    args:
      [
        "build",
        "-t",
        "gcr.io/$PROJECT_ID/api:$COMMIT_SHA",
        "-t",
        "gcr.io/$PROJECT_ID/api:production-latest",
        "-f",
        "CloudBuild.Dockerfile",
        ".",
      ]

  # Push the container image to Container Registry
  - name: "gcr.io/cloud-builders/docker"
    id: "push-image"
    args: ["push", "gcr.io/$PROJECT_ID/api:$COMMIT_SHA"]

  # Push the production-latest tag
  - name: "gcr.io/cloud-builders/docker"
    id: "push-latest-tag"
    args: ["push", "gcr.io/$PROJECT_ID/api:production-latest"]

  # Deploy container image to Cloud Run
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    id: "deploy-api-service"
    entrypoint: gcloud
    secretEnv:
      [
        "APP_KEY",
        "DB_PASSWORD",
        "DVLA_MOT_HISTORY_API_CLIENT_ID",
        "DVLA_MOT_HISTORY_API_CLIENT_SECRET",
        "DVLA_MOT_HISTORY_API_KEY",
        "DVLA_MOT_HISTORY_API_TENANT_ID",
        "DVLA_MOT_HISTORY_API_TOKEN_URL",
        "DVLA_VES_API_KEY",
        "GOHIGHLEVEL_PRIVATE_INTEGRATION_TOKEN",
        "FRESHCARAPI_ADMIN_USER",
        "FRESHCARAPI_ADMIN_PASSWORD",
        "STRIPE_KEY",
        "STRIPE_SECRET",
        "STRIPE_WEBHOOK_SECRET",


      ]
    args:
      - "run"
      - "deploy"
      - "api-service-production"  
      - "--image=gcr.io/$PROJECT_ID/api:production-latest"
      - "--region=europe-west1"
      - "--platform=managed"
      - "--set-env-vars=APP_ENV=production,APP_DEBUG=true,LOG_CHANNEL=stderr"
      - "--set-env-vars=APP_URL=https://admin.freshcarvaleting.com"

      - "--set-env-vars=CASHIER_CURRENCY=gbp"
      - "--set-env-vars=CACHE_DRIVER=database"     

      - "--set-env-vars=DB_CONNECTION=pgsql"
      - "--set-env-vars=DB_HOST=/cloudsql/${_CLOUD_SQL_CONNECTION_NAME}"
      - "--set-env-vars=DB_PORT=5432"
      - "--set-env-vars=DB_DATABASE=postgres"
      - "--set-env-vars=DB_USERNAME=postgres"

      - "--set-env-vars=DVLA_MOT_HISTORY_API_URL=https://history.mot.api.gov.uk"
      - "--set-env-vars=DVLA_MOT_HISTORY_API_SCOPE_URL=https://tapi.dvsa.gov.uk/.default"
      - "--set-env-vars=DVLA_VES_API_URL=https://driver-vehicle-licensing.api.gov.uk/vehicle-enquiry/v1/vehicles"

      - "--set-env-vars=FRESHCARAPI_URL=${_FRESHCARAPI_URL}"

      - "--set-env-vars=GOHIGHLEVEL_BASE_URL_AUTHORIZATION=https://marketplace.gohighlevel.com/oauth/chooselocation"
      - "--set-env-vars=GOHIGHLEVEL_BASE_URL_API=https://services.leadconnectorhq.com"
      - "--set-env-vars=GOHIGHLEVEL_WEBHOOK_SECRET_PATH=gohighlevel_key.pub"

      - "--add-cloudsql-instances=${_CLOUD_SQL_CONNECTION_NAME}"

      - "--set-secrets=APP_KEY=production-app-key:latest"
      

      - "--set-secrets=DB_PASSWORD=production-db-password:latest"

      - "--set-secrets=DVLA_MOT_HISTORY_API_CLIENT_ID=production-dvla-mot-history-api-client-id:latest"
      - "--set-secrets=DVLA_MOT_HISTORY_API_CLIENT_SECRET=production-dvla-mot-history-api-client-secret:latest"
      - "--set-secrets=DVLA_MOT_HISTORY_API_KEY=production-dvla-mot-history-api-key:latest"
      - "--set-secrets=DVLA_MOT_HISTORY_API_TOKEN_URL=production-dvla-mot-history-api-token-url:latest"
      - "--set-secrets=DVLA_MOT_HISTORY_API_TENANT_ID=production-dvla-mot-history-api-tenant-id:latest"
      - "--set-secrets=DVLA_VES_API_KEY=production-dvla-ves-api-key:latest"

      - "--set-secrets=FRESHCARAPI_ADMIN_USER=production-freshapi-admin-user:latest"
      - "--set-secrets=FRESHCARAPI_ADMIN_PASSWORD=production-freshapi-admin-password:latest"

      - "--set-secrets=GOHIGHLEVEL_PRIVATE_INTEGRATION_TOKEN=production-gohighlevel-private-integration-key:latest"

      - "--set-secrets=STRIPE_KEY=production-stripe-key:latest"
      - "--set-secrets=STRIPE_SECRET=production-stripe-secret:latest"
      - "--set-secrets=STRIPE_WEBHOOK_SECRET=production-stripe-webhook-secret:latest"

      - "--min-instances=1"
      - "--allow-unauthenticated"

  # Deploy queue worker service using gcloud run deploy (same pattern as API service)
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    id: "deploy-queue-worker-service"
    entrypoint: gcloud
    secretEnv:
      [
        "APP_KEY",
        "DB_PASSWORD",
      ]
    args:
      - "run"
      - "deploy"
      - "queue-worker-service-production"  # Changed from queue-worker-service
      - "--image=gcr.io/$PROJECT_ID/api:production-latest"
      - "--region=europe-west1"
      - "--platform=managed"
      - "--set-env-vars=APP_ENV=production,APP_DEBUG=true,LOG_CHANNEL=stderr"
      - "--set-env-vars=APP_URL=https://admin.freshcarvaleting.com"

      - "--set-env-vars=CACHE_DRIVER=database"
      - "--set-env-vars=DB_CONNECTION=pgsql"
      - "--set-env-vars=DB_HOST=/cloudsql/${_CLOUD_SQL_CONNECTION_NAME}"
      - "--set-env-vars=DB_PORT=5432"
      - "--set-env-vars=DB_DATABASE=postgres"
      - "--set-env-vars=DB_USERNAME=postgres"

      - "--set-env-vars=QUEUE_CONNECTION=database"
      - "--set-env-vars=QUEUE_NAME=default"

      - "--set-env-vars=LOG_WEBHOOK_DRIVER=stderr"
      - "--set-env-vars=LOG_FRESHCAR_DRIVER=stderr"
      - "--set-env-vars=LOG_GOHIGHLEVEL_DRIVER=stderr"

      - "--add-cloudsql-instances=${_CLOUD_SQL_CONNECTION_NAME}"

      - "--set-secrets=APP_KEY=production-app-key:latest"
      - "--set-secrets=DB_PASSWORD=production-db-password:latest"
      - "--command=/var/www/html/cloud-run-services/entrypoints/queue-worker-laravel-serve-entrypoint"
      - "--no-allow-unauthenticated"
      - "--min-instances=1"

  # Deploy Cloud Run Jobs
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    id: "deploy-cloud-run-jobs"
    entrypoint: bash
    args:
      - "-c"
      - |
        # Perform variable substitution in YAML files
        for file in cloud-run-jobs/production/*.yaml cloud-run-services/*.yaml; do
          sed -i "s/PROJECT_ID/$PROJECT_ID/g" $file
          sed -i "s/CLOUD_SQL_CONNECTION_NAME/${_CLOUD_SQL_CONNECTION_NAME}/g" $file
        done

        # Deploy production jobs
        gcloud run jobs replace cloud-run-jobs/production/migrate-job.yaml --region=europe-west1
        gcloud run jobs replace cloud-run-jobs/production/cache-clear-job.yaml --region=europe-west1
        gcloud run jobs replace cloud-run-jobs/production/artisan-command-job.yaml --region=europe-west1

      
# Available secrets from Secret Manager
availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/production-app-key/versions/latest
      env: "APP_KEY"
    - versionName: projects/$PROJECT_ID/secrets/production-db-password/versions/latest
      env: "DB_PASSWORD"

    - versionName: projects/$PROJECT_ID/secrets/production-dvla-mot-history-api-client-id/versions/latest
      env: "DVLA_MOT_HISTORY_API_CLIENT_ID"

    - versionName: projects/$PROJECT_ID/secrets/production-dvla-mot-history-api-client-secret/versions/latest
      env: "DVLA_MOT_HISTORY_API_CLIENT_SECRET"

    - versionName: projects/$PROJECT_ID/secrets/production-dvla-mot-history-api-key/versions/latest
      env: "DVLA_MOT_HISTORY_API_KEY"

    - versionName: projects/$PROJECT_ID/secrets/production-dvla-mot-history-api-tenant-id/versions/latest
      env: "DVLA_MOT_HISTORY_API_TENANT_ID"

    - versionName: projects/$PROJECT_ID/secrets/production-dvla-mot-history-api-token-url/versions/latest
      env: "DVLA_MOT_HISTORY_API_TOKEN_URL"

    - versionName: projects/$PROJECT_ID/secrets/production-dvla-ves-api-key/versions/latest
      env: "DVLA_VES_API_KEY"
   
    - versionName: projects/$PROJECT_ID/secrets/production-freshapi-admin-user/versions/latest
      env: "FRESHCARAPI_ADMIN_USER"

    - versionName: projects/$PROJECT_ID/secrets/production-freshapi-admin-password/versions/latest
      env: "FRESHCARAPI_ADMIN_PASSWORD"

    - versionName: projects/$PROJECT_ID/secrets/production-gohighlevel-private-integration-key/versions/latest
      env: "GOHIGHLEVEL_PRIVATE_INTEGRATION_TOKEN"

    - versionName: projects/$PROJECT_ID/secrets/production-stripe-key/versions/latest
      env: "STRIPE_KEY"

    - versionName: projects/$PROJECT_ID/secrets/production-stripe-secret/versions/latest
      env: "STRIPE_SECRET"

    - versionName: projects/$PROJECT_ID/secrets/production-stripe-webhook-secret/versions/latest
      env: "STRIPE_WEBHOOK_SECRET"

images:
  - "gcr.io/$PROJECT_ID/api:$COMMIT_SHA"
  - "gcr.io/$PROJECT_ID/api:production-latest"

substitutions:
  _CLOUD_SQL_CONNECTION_NAME: fresh-car-test:europe-west2:freshcar-production-core

options:
  logging: "CLOUD_LOGGING_ONLY"
serviceAccount: projects/$PROJECT_ID/serviceAccounts/tlg-devops@$PROJECT_ID.iam.gserviceaccount.com
