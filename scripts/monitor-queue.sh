#!/bin/bash

# Queue Monitoring Script for GoHighLevel Webhooks

echo "🔍 Queue Monitor - Press Ctrl+C to stop"
echo "========================================"

while true; do
    clear
    echo "🔍 Queue Monitor - $(date)"
    echo "========================================"
    
    # Get queue counts
    PENDING=$(docker exec -it api php artisan tinker --execute="echo DB::table('jobs')->count();" 2>/dev/null | tail -1 | tr -d '\r\n')
    FAILED=$(docker exec -it api php artisan tinker --execute="echo DB::table('failed_jobs')->count();" 2>/dev/null | tail -1 | tr -d '\r\n')
    
    echo "📊 Queue Status:"
    echo "   Pending Jobs: $PENDING"
    echo "   Failed Jobs:  $FAILED"
    echo ""
    
    if [ "$PENDING" -gt 0 ]; then
        echo "⏳ Pending Jobs:"
        docker exec -it api php artisan tinker --execute="
            DB::table('jobs')->get(['id', 'queue', 'created_at'])->each(function(\$job) {
                echo '   ID: ' . \$job->id . ' | Queue: ' . \$job->queue . ' | Created: ' . \$job->created_at . PHP_EOL;
            });
        " 2>/dev/null
        echo ""
        echo "💡 Run: docker exec -it api php artisan queue:work --once"
    else
        echo "✅ No pending jobs"
    fi
    
    if [ "$FAILED" -gt 0 ]; then
        echo ""
        echo "❌ Failed Jobs:"
        docker exec -it api php artisan queue:failed 2>/dev/null | head -10
        echo ""
        echo "💡 Run: docker exec -it api php artisan queue:retry all"
    fi
    
    echo ""
    echo "🔄 Refreshing in 5 seconds... (Ctrl+C to stop)"
    sleep 5
done
