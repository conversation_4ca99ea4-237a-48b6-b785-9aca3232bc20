<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Environment Testing Routes
|--------------------------------------------------------------------------
|
| These routes are for testing environment variable configuration
| in Cloud Run. Remove or secure these routes in production.
|
*/

Route::get('/env-check', function () {
    // Only allow in non-production environments or with specific header
    if (app()->environment('production') && !request()->hasHeader('X-Debug-Token')) {
        abort(404);
    }
    
    $envVars = [
        'APP_NAME' => config('app.name'),
        'APP_ENV' => config('app.env'),
        'APP_DEBUG' => config('app.debug'),
        'APP_URL' => config('app.url'),
        'DB_CONNECTION' => config('database.default'),
        'DB_HOST' => config('database.connections.pgsql.host'),
        'DB_PORT' => config('database.connections.pgsql.port'),
        'DB_DATABASE' => config('database.connections.pgsql.database'),
        'DB_USERNAME' => config('database.connections.pgsql.username'),
        'LOG_CHANNEL' => config('logging.default'),
        'CACHE_DRIVER' => config('cache.default'),
        'SESSION_DRIVER' => config('session.driver'),
        'QUEUE_CONNECTION' => config('queue.default'),
    ];
    
    // Check if sensitive variables are set (without exposing values)
    $sensitiveVars = [
        'APP_KEY' => !empty(config('app.key')),
        'DB_PASSWORD' => !empty(config('database.connections.pgsql.password')),
        'STRIPE_SECRET' => !empty(config('services.stripe.secret')),
        'STRIPE_WEBHOOK_SECRET' => !empty(config('cashier.webhook.secret')),
    ];
    
    return response()->json([
        'status' => 'Environment check',
        'timestamp' => now()->toISOString(),
        'environment_variables' => $envVars,
        'sensitive_variables_set' => $sensitiveVars,
        'database_connection' => [
            'can_connect' => false, // Will be set below
            'error' => null
        ]
    ]);
})->name('env.check');

Route::get('/health', function () {
    try {
        // Test database connection
        \DB::connection()->getPdo();
        $dbStatus = true;
        $dbError = null;
    } catch (\Exception $e) {
        $dbStatus = false;
        $dbError = $e->getMessage();
    }
    
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()->toISOString(),
        'app_name' => config('app.name'),
        'environment' => config('app.env'),
        'database' => [
            'connected' => $dbStatus,
            'error' => $dbError
        ],
        'cache' => [
            'driver' => config('cache.default')
        ]
    ]);
})->name('health.check');
