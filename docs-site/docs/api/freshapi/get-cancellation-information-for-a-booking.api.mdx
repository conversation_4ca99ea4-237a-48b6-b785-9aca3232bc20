---
id: get-cancellation-information-for-a-booking
title: "Get cancellation information for a booking"
description: "Get cancellation information for a booking"
sidebar_label: "Get cancellation information for a booking"
hide_title: true
hide_table_of_contents: true
api: eJyVVtty2zYQ/RXM5iHJDEUqtpO2fHMcJ+OOm2Qit3mIPAkIrkiMQQDBRbaq4b93FqRkyXKa1g8ySewNu2fP7hoCbzyUX+CMO3amYgXXGVjueIcBHZ2sQfMOoYTKmBupm4saMpAaSrA8tJCBw+9ROqyhDC5iBl602HEo1xBWlhSlDtiggwwWxnU8DJ+Oj6Dvr0ndW6M9etI4mk7pX41eOGmDNOTnjGuBSnF6ZVIPRujZYXASl1gzH4VA7xdRqRX0GZxMTw7tvB4uwLQJbGGirqHvM/AoopNhla5aIXfooPxyTaHtq7/DwMSPQlkYxzgbUwQZdBhaU0MJDQbIhlSVUCyPC25lIbibCBWrYlTwxXqb3R4oJrfcZD86BSW0IVhfFoUygqvW+FCurXGBhPejvCQBNhiADJbcSV6pIbukMaRlwaOiOryYvphC3/fZQz81LlEZ26EO+cKhbwV3uTB5vDnw+OZedOP30B63ckKHUuDkxfS3X1++enV0cvLL8TTH6IzFyS36cJS7qHNubXHgZBZ4Q7X7Nwc/C/SjM3UUqVwbM9f39Z8RbIc8bVCwRTA5gRHZqRUGgWx8eLuB9e+frw68/v75ip3G0Bon/x6w0iKv0bHo6UKhRfY6WWGD+Zyd3/HOKizZ0z29ciO3DuYGdf80wZdAmCKVQVFobykHjLr59OMFAQCdHwI5zqf5FDK4myjTJJ0hg4XsmmKTunSW24ThioubxlGjnBllHJTw5G36gwy4Cld4Fw4c9g+v//WqlZ6OWMs9qxA1G7GFNQuGObSKC0x5qE14j4Eli6Tx11HGuK6Z9KlnVxiY1Cx6rFm1ShpCSYIdt9bnX+d6rp88YbPYddyt5nrr2TqzlDV6hrq2RurgU792XA+gEtz5jH6Z4AEb4yRu3lWs5trHanulnYNNu/shyujRMR5DizpIkUqWs4vAuFLm1qdjP9fBMIuOiIMZiy6JeeKvlvFBiDlspA/DUbKsTCN1xpT0YRvuXA9i6BKl4e14ixoV3kvt6uxdTo1cGC0dzXWNgUvlKa977nXsKnQZ88idaDd2SazjN5ii60yNKmPCISdXc73Nz8O8jRKPJTDaejgbP421PHPG+8kHJxup2Sf0JjqBbNbydOtnZx8+zZ7vFNpHSxzn/5NewoBIgrXpuKR58j2iDz4fvZ/uFXOuT5XagdA49x6UfGzrS+74EhWbcS1C7HJ2oYWK9YDzgTVYamNCNH17jCLKuf727dtc/4AGVia6ycAFoyBkIIwOXCSaH+f2VYvsUjZt0BTYO2cicdk+e97e3uahRbURo7a3I432GSgpUHvcMfrHxdWBEWNRD5nOjWuKUckXJNtnENB1/sNiNkyBHb1GhjZWuTBdkcYyOq7rbSg71FTZiTAOi0qZqqCKFZcXZ+fvZ+eJ1RwPOFGyk+Fw9BM46Jylc8qDJDZYGCcGGkLto0M2jijmA6+kkmGVEL7gkuiaN5gneh+n5xrIJJSvphlYmhfQSR0DEnWuoISLj4zXtUPvmXFDa1+8Ybct6l3MYJ0yTC1+b/LlTy1SXNhxqZgwXSV1wkYa5WCNDx3XO9X6X6vLXua2IzDgXSis4lJTuKnw63Gt+QLLYxoJVhL+xtWGQh67GzIo75fH6wxofyG19briHv90qu/p8/eIjtaw6/u1JW1AtfT0XEO54MrjQYQEedQ0i559GnfR5wyyxyMfP3K9StuRivQGGdzgam/H7a/7nS3u3TnBnaq2k5IHW0CK9VHz6/UgcZV6td96S71Lnvr+H+OmCbw=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get cancellation information for a booking"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/car-club/bookings/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get cancellation information for a booking

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Cancellation information retrieved successfully"},"404":{"description":"Booking not found"}}}
>
  
</StatusCodes>


      