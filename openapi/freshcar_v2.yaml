openapi: 3.0.1
info:
  title: Fresh Car API
  version: v2
  description:
    This API provides endpoints for the dotNet FreshAPI V2. It provides
    endpoints for managing cars, car categories, car club subscriptions, car club
    bookings, and user authentication. It allows users to perform operations such
    as user registration and login, listing cars, registering new cars, deleting cars,
    listing car categories, looking up car details by registration number, searching
    cars by make and model, creating car club subscriptions, creating car club bookings,
    updating booking statuses, cancelling car club subscriptions, and managing user
    authentication including password resets and email verification.

servers:
  - url: https://localhost:{port}
    description: Local server
    variables:
      port:
        default: "5001"
  - url: https://fresh-dev-api.azurewebsites.net
    description: Development server
  - url: https://fresh-uat-api.azurewebsites.net
    description: Staging server
  - url: https://fresh-live-api.azurewebsites.net
    description: Production server
paths:
  /api/Account/Register:
    post:
      operationId: registerAccount
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RegisterDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/RegisterDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/RegisterDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/Login:
    post:
      operationId: loginAccount
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoginDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/LoginDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/LoginDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/ConfirmEmail:
    post:
      operationId: confirmEmail
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmEmailDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/ConfirmEmailDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/ConfirmEmailDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/AccountInformation:
    get:
      operationId: getAccountInformation
      tags:
        - Account
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/ForgotPassword:
    post:
      operationId: forgotPassword
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ForgotPasswordRequestDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/ForgotPasswordRequestDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/ForgotPasswordRequestDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/ResetPassword:
    post:
      operationId: resetPassword
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ResetPasswordDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/ResetPasswordDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/ResetPasswordDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/Logout:
    post:
      operationId: logoutAccount
      tags:
        - Account
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/RefreshToken:
    post:
      operationId: refreshToken
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefreshTokenDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/RefreshTokenDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/RefreshTokenDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/UpdatePassword:
    post:
      operationId: updatePassword
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdatePasswordDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdatePasswordDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdatePasswordDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/UpdateConsent:
    post:
      operationId: updateConsent
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerConsentDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerConsentDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerConsentDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
  /api/Account/UpdateUserDetails:
    post:
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerDetailsDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerDetailsDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerDetailsDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAccountUpdateuserdetails
  /api/Account/EmailAvailable:
    post:
      tags:
        - Account
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmailAvailabilityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/EmailAvailabilityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/EmailAvailabilityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAccountEmailavailable
  /api/Account/Delete:
    post:
      tags:
        - Account
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAccountDelete
  /api/addresses:
    get:
      tags:
        - Address
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAddresses
    post:
      tags:
        - Address
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerAddressDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CustomerAddressDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CustomerAddressDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAddresses
  /api/addresses/{addressId}:
    patch:
      tags:
        - Address
      parameters:
        - name: addressId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAddressesByid
  /api/addresses/search:
    post:
      tags:
        - Address
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PostcodeAddressesLookupDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/PostcodeAddressesLookupDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/PostcodeAddressesLookupDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAddressesSearch
  /api/admin/valeters:
    get:
      tags:
        - AdminValeters
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            format: date-time
        - name: appointmentTypeIds
          in: query
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminValeters
  /api/admin/valeters/get-all-valeters:
    get:
      tags:
        - AdminValeters
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminValetersGet-all-valeters
  /api/admin/allbookings:
    get:
      tags:
        - AllBookingsAdmin
      parameters:
        - name: freshStatuses
          in: query
          schema:
            type: array
            items:
              type: string
        - name: pageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminAllbookings
    post:
      tags:
        - AllBookingsAdmin
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminManualBookingDto"
          text/json:
            schema:
              $ref: "#/components/schemas/AdminManualBookingDto"
          application/*+json:
            schema:
              $ref: "#/components/schemas/AdminManualBookingDto"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAdminAllbookings
  /api/admin/allbookings/{bookingId}:
    get:
      tags:
        - AllBookingsAdmin
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminAllbookingsByid
  /api/admin/allbookings/enquirystatus/{bookingId}:
    patch:
      tags:
        - AllBookingsAdmin
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminAllbookingsEnquirystatusByid
  /api/admin/allbookings/notes/{bookingId}:
    patch:
      tags:
        - AllBookingsAdmin
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingNotesDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingNotesDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateBookingNotesDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminAllbookingsNotesByid
  /api/admin/allbookings/customercars/{bookingId}:
    patch:
      tags:
        - AllBookingsAdmin
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingCustomerCarsDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingCustomerCarsDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateBookingCustomerCarsDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminAllbookingsCustomercarsByid
  /api/admin/allbookings/refund/{bookingId}:
    patch:
      tags:
        - AllBookingsAdmin
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefundBookingDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/RefundBookingDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/RefundBookingDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminAllbookingsRefundByid
  /api/admin/allbookings/statusCount:
    get:
      tags:
        - AllBookingsAdmin
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminAllbookingsStatuscount
  /api/bookableSlots/query:
    post:
      tags:
        - BookableSlots
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QueryBookableSlotsDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/QueryBookableSlotsDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/QueryBookableSlotsDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createBookableslotsQuery
  /api/paid-bookings:
    post:
      tags:
        - Booking
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBookingDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CreateBookingDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CreateBookingDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createPaid-bookings
  /api/bookings:
    post:
      tags:
        - Booking
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BookingDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/BookingDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/BookingDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createBookings
    get:
      tags:
        - Booking
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getBookings
  /api/bookings/{bookingId}:
    patch:
      tags:
        - Booking
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateBookingsByid
    get:
      tags:
        - Booking
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getBookingsByid
  /api/bookings/enquirystatus/{bookingId}:
    patch:
      tags:
        - Booking
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateBookingsEnquirystatusByid
  /api/bookings/cancellationreasons:
    get:
      tags:
        - Booking
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getBookingsCancellationreasons
  /api/cars:
    get:
      tags:
        - Car
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCars
    post:
      tags:
        - Car
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerCarDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CustomerCarDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CustomerCarDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createCars
  /api/cars/{id}:
    get:
      tags:
        - Car
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarsByid
  /api/cars/{carId}:
    patch:
      tags:
        - Car
      parameters:
        - name: carId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateCarsByid
  /api/carcategories:
    get:
      tags:
        - CarCategory
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarcategories
  /api/carclub/subscribe:
    post:
      tags:
        - CarClub
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CarClubRegisterDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CarClubRegisterDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CarClubRegisterDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createCarclubSubscribe
  /api/carclub/book:
    post:
      tags:
        - CarClub
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCarClubBookingRequestDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CreateCarClubBookingRequestDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CreateCarClubBookingRequestDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createCarclubBook
  /api/carclub/enquirystatus/{bookingId}:
    patch:
      tags:
        - CarClub
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateBookingEnquiryStatusDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateCarclubEnquirystatusByid
  /api/carclub/cancel/{customerCarClubPackageId}:
    patch:
      tags:
        - CarClub
      parameters:
        - name: customerCarClubPackageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CarClubSubscriptionCancellationDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CarClubSubscriptionCancellationDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CarClubSubscriptionCancellationDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateCarclubCancelByid
  /api/carclub/pause/{customerCarClubPackageId}:
    patch:
      tags:
        - CarClub
      parameters:
        - name: customerCarClubPackageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateCarclubPauseByid
  /api/carclub/resume/{customerCarClubPackageId}:
    patch:
      tags:
        - CarClub
      parameters:
        - name: customerCarClubPackageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateCarclubResumeByid
  /api/carclub:
    get:
      tags:
        - CarClub
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarclub
  /api/carclub/prices/{packageId}/{carCategory}:
    get:
      tags:
        - CarClub
      parameters:
        - name: packageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: carCategory
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarclubPricesByidByid
  /api/carclub/cancellationinformation/{bookingId}:
    get:
      tags:
        - CarClub
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarclubCancellationinformationByid
  /api/carclub/subscriptioncancellationreasons:
    get:
      tags:
        - CarClub
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarclubSubscriptioncancellationreasons
  /api/carLookup/registration-lookup/{registrationNumber}:
    get:
      tags:
        - CarLookup
      parameters:
        - name: registrationNumber
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarlookupRegistration-lookupByid
  /api/carLookup/make-model-filter/{searchFilter}:
    get:
      tags:
        - CarLookup
      parameters:
        - name: searchFilter
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getCarlookupMake-model-filterByid
  /api/admin/customers/{customerId}/addresses:
    post:
      tags:
        - CustomerAddressesAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerAddressDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CustomerAddressDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CustomerAddressDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAdminCustomersByidAddresses
  /api/admin/customers/{customerId}/addresses/{addressId}:
    patch:
      tags:
        - CustomerAddressesAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: addressId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomersByidAddressesByid
  /api/admin/customers/{customerId}/bookings:
    post:
      tags:
        - CustomerBookingAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BookingDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/BookingDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/BookingDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAdminCustomersByidBookings
  /api/admin/customers/{customerId}/bookings/{bookingId}:
    patch:
      tags:
        - CustomerBookingAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomersByidBookingsByid
  /api/admin/customercarclub/{customerId}:
    get:
      tags:
        - CustomerCarClubAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminCustomercarclubByid
  /api/admin/customercarclub/cancel/{customerCarClubPackageId}:
    patch:
      tags:
        - CustomerCarClubAdmin
      parameters:
        - name: customerCarClubPackageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomercarclubCancelByid
  /api/admin/customercarclub/pause/{customerCarClubPackageId}:
    patch:
      tags:
        - CustomerCarClubAdmin
      parameters:
        - name: customerCarClubPackageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomercarclubPauseByid
  /api/admin/customercarclub/resume/{customerCarClubPackageId}:
    patch:
      tags:
        - CustomerCarClubAdmin
      parameters:
        - name: customerCarClubPackageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomercarclubResumeByid
  /api/admin/customercarclub/cleans/{customerCarClubPackageId}:
    patch:
      tags:
        - CustomerCarClubAdmin
      parameters:
        - name: customerCarClubPackageId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCarClubCleansDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCarClubCleansDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCarClubCleansDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomercarclubCleansByid
  /api/admin/customers/{customerId}/cars:
    post:
      tags:
        - CustomerCarsAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerCarDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CustomerCarDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CustomerCarDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAdminCustomersByidCars
  /api/admin/customers/{customerId}/cars/{carId}:
    patch:
      tags:
        - CustomerCarsAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: carId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomersByidCarsByid
  /api/admin/customerpreferredvaleter/{customerId}:
    get:
      tags:
        - CustomerPreferredValeterAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminCustomerpreferredvaleterByid
    post:
      tags:
        - CustomerPreferredValeterAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddPreferredValeterDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/AddPreferredValeterDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/AddPreferredValeterDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAdminCustomerpreferredvaleterByid
  /api/admin/customerpreferredvaleter/{customerPreferredValeterId}:
    patch:
      tags:
        - CustomerPreferredValeterAdmin
      parameters:
        - name: customerPreferredValeterId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomerpreferredvaleterByid
  /api/admin/customers:
    get:
      tags:
        - CustomersAdmin
      parameters:
        - name: searchTerm
          in: query
          schema:
            type: string
        - name: sortBy
          in: query
          schema:
            type: string
        - name: pageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminCustomers
    post:
      tags:
        - CustomersAdmin
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCustomerDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CreateCustomerDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CreateCustomerDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAdminCustomers
  /api/admin/customers/{customerId}:
    get:
      tags:
        - CustomersAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getAdminCustomersByid
    put:
      tags:
        - CustomersAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerDetailsDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerDetailsDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/UpdateCustomerDetailsDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateAdminCustomersByid
  /api/admin/customers/login/{customerId}:
    post:
      tags:
        - CustomersAdmin
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createAdminCustomersLoginByid
  /api-mbh/bookings/{bookingId}:
    patch:
      tags:
        - MyBookingHub
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MbhPatchBookingDto"
          text/json:
            schema:
              $ref: "#/components/schemas/MbhPatchBookingDto"
          application/*+json:
            schema:
              $ref: "#/components/schemas/MbhPatchBookingDto"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updateApi-mbhBookingsByid
  /api/newcustomerbookings:
    post:
      tags:
        - NewCustomerBooking
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NewCustomerBookingDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/NewCustomerBookingDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/NewCustomerBookingDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createNewcustomerbookings
  /api/Notification/SendToCustomer:
    post:
      tags:
        - Notification
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createNotificationSendtocustomer
  /api/package-information:
    get:
      tags:
        - Package
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getPackage-information
  /api/package-information/{postcode}:
    get:
      tags:
        - Package
      parameters:
        - name: postcode
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getPackage-informationByid
  /api/package-information/all:
    get:
      tags:
        - Package
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getPackage-informationAll
  /api/package-information/carclub:
    get:
      tags:
        - Package
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getPackage-informationCarclub
  /api/Postcode/PostcodeCoverage:
    post:
      tags:
        - Postcode
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckPostcodeDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/CheckPostcodeDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/CheckPostcodeDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createPostcodePostcodecoverage
  /api/Postcode/PostcodeResource/{postcode}:
    get:
      tags:
        - Postcode
      parameters:
        - name: postcode
          in: path
          required: true
          schema:
            type: string
        - name: additionalPostcodes
          in: query
          schema:
            type: string
        - name: commaSeparatedPackageGroupIds
          in: query
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getPostcodePostcoderesourceByid
  /api/PreferredValeter:
    post:
      tags:
        - PreferredValeter
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddPreferredValeterDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/AddPreferredValeterDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/AddPreferredValeterDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createPreferredvaleter
    get:
      tags:
        - PreferredValeter
      parameters:
        - name: postcodes
          in: query
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getPreferredvaleter
  /api/PreferredValeter/{customerPreferredValeterId}:
    patch:
      tags:
        - PreferredValeter
      parameters:
        - name: customerPreferredValeterId
          in: path
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          text/json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
          application/*+json:
            schema:
              $ref: "#/components/schemas/SoftDeleteEntityDTO"
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: updatePreferredvaleterByid
  /api/StripeWebhook:
    post:
      tags:
        - StripeWebhook
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: createStripewebhook
  /api/valeters/{valeterId}:
    get:
      tags:
        - Valeter
      parameters:
        - name: valeterId
          in: path
          required: true
          schema:
            type: integer
            format: int32
        - name: postcodes
          in: query
          schema:
            type: string
      responses:
        "200":
          description: Success
      security:
        - bearer: []
      operationId: getValetersByid
components:
  schemas:
    AddPreferredValeterDTO:
      required:
        - resourceID
        - resourceName
      type: object
      properties:
        resourceID:
          type: integer
          format: int32
        resourceName:
          minLength: 1
          type: string
      additionalProperties: false
    AdminManualBookingDto:
      required:
        - bookingId
        - endDateTime
        - resourceId
        - startDateTime
      type: object
      properties:
        bookingId:
          type: integer
          format: int32
        startDateTime:
          type: string
          format: date-time
        endDateTime:
          type: string
          format: date-time
        resourceId:
          type: integer
          format: int32
        muteConfirmationEmail:
          type: boolean
      additionalProperties: false
    BookingCustomerCarPackageDTO:
      type: object
      properties:
        bookingCustomerCarId:
          type: integer
          format: int32
        bookingId:
          type: integer
          format: int32
        customerCarId:
          type: integer
          format: int32
        packageGroupId:
          type: integer
          format: int32
        registrationNumber:
          type: string
          nullable: true
        makeAndModel:
          type: string
          nullable: true
        category:
          type: string
          nullable: true
        colour:
          type: string
          nullable: true
        year:
          type: string
          nullable: true
        optionalExtras:
          type: array
          items:
            $ref: "#/components/schemas/BookingPackageItemOptionalExtraDTO"
          nullable: true
      additionalProperties: false
    BookingDTO:
      required:
        - addressId
        - bookingStatusId
        - requestedDate
      type: object
      properties:
        bookingId:
          type: integer
          format: int32
          nullable: true
        requestedDate:
          type: string
          format: date-time
        confirmedDate:
          type: string
          format: date-time
          nullable: true
        addressId:
          type: integer
          format: int32
        additionalComments:
          type: string
          nullable: true
        notes:
          type: string
          nullable: true
        preferredTime:
          type: string
          format: date-time
          nullable: true
        bookingStatusId:
          type: integer
          format: int32
        totalCost:
          type: number
          format: double
        enquiryStatus:
          type: string
          nullable: true
        resourceId:
          type: integer
          format: int32
        resourceName:
          type: string
          nullable: true
        timeOfDay:
          type: string
          nullable: true
        bookingReferenceNumber:
          type: string
          nullable: true
        bookingHubDurationMinutes:
          type: string
          nullable: true
        overridePrice:
          type: number
          format: double
          nullable: true
        bookingCustomerCars:
          type: array
          items:
            $ref: "#/components/schemas/BookingCustomerCarPackageDTO"
          nullable: true
        isPrepaid:
          type: boolean
        isRefunded:
          type: boolean
        customerCarClubPackageId:
          type: integer
          format: int32
          nullable: true
        currency:
          $ref: "#/components/schemas/Currency"
      additionalProperties: false
    BookingPackageItemOptionalExtraDTO:
      type: object
      properties:
        packageItemId:
          type: integer
          format: int32
      additionalProperties: false
    CarBookingBookableSlot:
      type: object
      properties:
        carCategory:
          type: string
          nullable: true
        packageGroupId:
          type: integer
          format: int32
        optionalExtraIds:
          type: array
          items:
            type: integer
            format: int32
          nullable: true
        customerCarClubPackageId:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    CarClubBookingCustomerCarPackageDTO:
      type: object
      properties:
        customerCarId:
          type: integer
          format: int32
        customerCarCategory:
          type: string
          nullable: true
        optionalExtras:
          type: array
          items:
            $ref: "#/components/schemas/BookingPackageItemOptionalExtraDTO"
          nullable: true
        customerCarClubPackageId:
          type: integer
          format: int32
        packageGroupId:
          type: integer
          format: int32
      additionalProperties: false
    CarClubRegisterDTO:
      type: object
      properties:
        customerCarID:
          type: integer
          format: int32
        frequency:
          type: integer
          format: int32
        cleans:
          type: integer
          format: int32
        resourceId:
          type: integer
          format: int32
        resourceName:
          type: string
          nullable: true
        category:
          type: string
          nullable: true
        packageGroupId:
          type: integer
          format: int32
        customerName:
          type: string
          nullable: true
        paymentDetails:
          $ref: "#/components/schemas/PaymentDetailsDTO"
      additionalProperties: false
    CarClubSubscriptionCancellationDTO:
      type: object
      properties:
        cancellationReasonId:
          type: integer
          format: int32
          nullable: true
        cancellationReasonNotes:
          type: string
          nullable: true
      additionalProperties: false
    CheckPostcodeDTO:
      required:
        - postcode
      type: object
      properties:
        postcode:
          minLength: 1
          pattern: ^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$
          type: string
      additionalProperties: false
    ConfirmEmailDTO:
      required:
        - code
        - userId
      type: object
      properties:
        userId:
          minLength: 1
          type: string
        code:
          minLength: 1
          type: string
      additionalProperties: false
    Country:
      enum:
        - 0
        - 1
      type: integer
      format: int32
    CreateBookingDTO:
      type: object
      properties:
        booking:
          $ref: "#/components/schemas/BookingDTO"
        paymentDetails:
          $ref: "#/components/schemas/PaymentDetailsDTO"
      additionalProperties: false
    CreateCarClubBookingDTO:
      type: object
      properties:
        addressId:
          type: integer
          format: int32
        customerCarId:
          type: integer
          format: int32
        customerCarCategory:
          type: string
          nullable: true
        optionalExtras:
          type: array
          items:
            $ref: "#/components/schemas/BookingPackageItemOptionalExtraDTO"
          nullable: true
        confirmedDate:
          type: string
          format: date-time
        preferredTime:
          type: string
          format: date-time
          nullable: true
        additionalComments:
          type: string
          nullable: true
        resourceId:
          type: integer
          format: int32
        isPrepaid:
          type: boolean
        customerCarClubPackageId:
          type: integer
          format: int32
        packageGroupId:
          type: integer
          format: int32
        bookingHubDurationMinutes:
          type: string
          nullable: true
        carClubBookingCustomerCars:
          type: array
          items:
            $ref: "#/components/schemas/CarClubBookingCustomerCarPackageDTO"
          nullable: true
      additionalProperties: false
    CreateCarClubBookingRequestDTO:
      type: object
      properties:
        carClubBooking:
          $ref: "#/components/schemas/CreateCarClubBookingDTO"
        paymentDetails:
          $ref: "#/components/schemas/PaymentDetailsDTO"
      additionalProperties: false
    CreateCustomerDTO:
      required:
        - address
        - contactNumber
        - email
        - firstName
        - lastName
      type: object
      properties:
        customerId:
          type: integer
          format: int32
          nullable: true
        title:
          maxLength: 10
          type: string
          nullable: true
        firstName:
          maxLength: 50
          minLength: 1
          type: string
        lastName:
          maxLength: 50
          minLength: 1
          type: string
        contactNumber:
          maxLength: 50
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
        customerReferenceNumber:
          type: string
          nullable: true
        address:
          $ref: "#/components/schemas/CustomerAddressDTO"
        sanitisedEmail:
          type: string
          nullable: true
          readOnly: true
      additionalProperties: false
    Currency:
      enum:
        - 0
        - 1
      type: integer
      format: int32
    CustomerAddressDTO:
      required:
        - addressLine1
        - postcode
        - town
      type: object
      properties:
        addressId:
          type: integer
          format: int32
          nullable: true
        addressLine1:
          maxLength: 150
          minLength: 1
          type: string
        addressLine2:
          maxLength: 150
          type: string
          nullable: true
        addressLine3:
          maxLength: 150
          type: string
          nullable: true
        town:
          maxLength: 50
          minLength: 1
          type: string
        postcode:
          maxLength: 12
          minLength: 1
          type: string
        country:
          $ref: "#/components/schemas/Country"
      additionalProperties: false
    CustomerCarDTO:
      required:
        - makeAndModel
        - registrationNumber
      type: object
      properties:
        customerCarId:
          type: integer
          format: int32
          nullable: true
        registrationNumber:
          maxLength: 50
          minLength: 1
          type: string
        makeAndModel:
          maxLength: 150
          minLength: 1
          type: string
        category:
          maxLength: 10
          type: string
          nullable: true
        colour:
          maxLength: 50
          type: string
          nullable: true
        year:
          maxLength: 10
          type: string
          nullable: true
      additionalProperties: false
    EmailAvailabilityDTO:
      required:
        - email
      type: object
      properties:
        email:
          minLength: 1
          type: string
      additionalProperties: false
    ForgotPasswordRequestDTO:
      required:
        - email
      type: object
      properties:
        email:
          minLength: 1
          type: string
          format: email
      additionalProperties: false
    LoginDTO:
      required:
        - email
        - password
      type: object
      properties:
        email:
          minLength: 1
          type: string
          format: email
        password:
          minLength: 1
          type: string
          format: password
      additionalProperties: false
    MbhPatchBookingDto:
      type: object
      properties:
        notes:
          type: string
          nullable: true
      additionalProperties: false
    NewCustomerBookingDTO:
      type: object
      properties:
        registerDTO:
          $ref: "#/components/schemas/RegisterDTO"
        bookingDTO:
          $ref: "#/components/schemas/NewCustomerCarBookingDTO"
        paymentDetails:
          $ref: "#/components/schemas/PaymentDetailsDTO"
      additionalProperties: false
    NewCustomerCarBookingDTO:
      required:
        - addressId
        - bookingStatusId
        - requestedDate
      type: object
      properties:
        bookingId:
          type: integer
          format: int32
          nullable: true
        requestedDate:
          type: string
          format: date-time
        confirmedDate:
          type: string
          format: date-time
          nullable: true
        addressId:
          type: integer
          format: int32
        additionalComments:
          type: string
          nullable: true
        notes:
          type: string
          nullable: true
        preferredTime:
          type: string
          format: date-time
          nullable: true
        bookingStatusId:
          type: integer
          format: int32
        totalCost:
          type: number
          format: double
        enquiryStatus:
          type: string
          nullable: true
        resourceId:
          type: integer
          format: int32
        resourceName:
          type: string
          nullable: true
        timeOfDay:
          type: string
          nullable: true
        bookingReferenceNumber:
          type: string
          nullable: true
        bookingHubDurationMinutes:
          type: string
          nullable: true
        overridePrice:
          type: number
          format: double
          nullable: true
        bookingCustomerCars:
          type: array
          items:
            $ref: "#/components/schemas/BookingCustomerCarPackageDTO"
          nullable: true
        isPrepaid:
          type: boolean
        isRefunded:
          type: boolean
        customerCarClubPackageId:
          type: integer
          format: int32
          nullable: true
        currency:
          $ref: "#/components/schemas/Currency"
      additionalProperties: false
    PaymentDetailsDTO:
      type: object
      properties:
        isPayNow:
          type: boolean
        paymentMethodId:
          type: string
          nullable: true
        paymentIntentId:
          type: string
          nullable: true
        paymentIntentClientSecret:
          type: string
          nullable: true
        requiresAction:
          type: boolean
      additionalProperties: false
    PostcodeAddressesLookupDTO:
      required:
        - postcode
      type: object
      properties:
        postcode:
          minLength: 1
          pattern: ^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$
          type: string
      additionalProperties: false
    QueryBookableSlotsDTO:
      required:
        - date
        - postcode
      type: object
      properties:
        carBookings:
          type: array
          items:
            $ref: "#/components/schemas/CarBookingBookableSlot"
          nullable: true
        postcode:
          minLength: 1
          pattern: ^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$
          type: string
        date:
          type: string
          format: date-time
        resourceId:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    RefreshTokenDTO:
      required:
        - email
        - jwtToken
      type: object
      properties:
        email:
          minLength: 1
          type: string
          format: email
        jwtToken:
          minLength: 1
          type: string
      additionalProperties: false
    RefundBookingDTO:
      type: object
      properties:
        isRefunded:
          type: boolean
      additionalProperties: false
    RegisterDTO:
      required:
        - address
        - contactNumber
        - email
        - firstName
        - lastName
        - password
      type: object
      properties:
        customerId:
          type: integer
          format: int32
          nullable: true
        title:
          maxLength: 10
          type: string
          nullable: true
        firstName:
          maxLength: 50
          minLength: 1
          type: string
        lastName:
          maxLength: 50
          minLength: 1
          type: string
        contactNumber:
          maxLength: 50
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
        customerReferenceNumber:
          type: string
          nullable: true
        address:
          $ref: "#/components/schemas/CustomerAddressDTO"
        sanitisedEmail:
          type: string
          nullable: true
          readOnly: true
        password:
          minLength: 1
          type: string
          format: password
        confirmPassword:
          type: string
          format: password
          nullable: true
      additionalProperties: false
    ResetPasswordDTO:
      required:
        - code
        - email
        - password
      type: object
      properties:
        email:
          minLength: 1
          type: string
          format: email
        password:
          minLength: 1
          type: string
          format: password
        confirmPassword:
          type: string
          format: password
          nullable: true
        code:
          minLength: 1
          type: string
      additionalProperties: false
    SoftDeleteEntityDTO:
      type: object
      properties:
        isDeleted:
          type: boolean
      additionalProperties: false
    UpdateBookingCustomerCarsDTO:
      required:
        - bookingCustomerCars
      type: object
      properties:
        bookingCustomerCars:
          type: array
          items:
            $ref: "#/components/schemas/BookingCustomerCarPackageDTO"
        overridePrice:
          type: number
          format: double
          nullable: true
      additionalProperties: false
    UpdateBookingEnquiryStatusDTO:
      required:
        - enquiryStatus
      type: object
      properties:
        enquiryStatus:
          minLength: 1
          type: string
        confirmedDate:
          type: string
          format: date-time
          nullable: true
        cancellationReasonId:
          type: integer
          format: int32
          nullable: true
        cancellationReasonNotes:
          type: string
          nullable: true
      additionalProperties: false
    UpdateBookingNotesDTO:
      required:
        - notes
      type: object
      properties:
        notes:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateCarClubCleansDTO:
      required:
        - cleansRemaining
      type: object
      properties:
        cleansRemaining:
          type: integer
          format: int32
      additionalProperties: false
    UpdateCustomerConsentDTO:
      required:
        - emailConsent
        - phoneConsent
        - postalConsent
        - smsConsent
      type: object
      properties:
        emailConsent:
          type: boolean
        phoneConsent:
          type: boolean
        smsConsent:
          type: boolean
        postalConsent:
          type: boolean
      additionalProperties: false
    UpdateCustomerDetailsDTO:
      required:
        - contactNumber
        - firstName
        - lastName
      type: object
      properties:
        customerId:
          type: integer
          format: int32
          nullable: true
        title:
          maxLength: 10
          type: string
          nullable: true
        firstName:
          maxLength: 50
          minLength: 1
          type: string
        lastName:
          maxLength: 50
          minLength: 1
          type: string
        contactNumber:
          maxLength: 50
          minLength: 1
          type: string
        dateOfBirth:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    UpdatePasswordDTO:
      required:
        - confirmNewPassword
        - email
        - newPassword
        - oldPassword
      type: object
      properties:
        email:
          minLength: 1
          type: string
          format: email
        oldPassword:
          minLength: 1
          type: string
          format: password
        newPassword:
          minLength: 1
          type: string
          format: password
        confirmNewPassword:
          minLength: 1
          type: string
          format: password
      additionalProperties: false
  securitySchemes:
    bearer:
      type: http
      scheme: bearer
      bearerFormat: JWT
