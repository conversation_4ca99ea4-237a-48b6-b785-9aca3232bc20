---
id: get-admin-customerpreferredvaleter-byid
title: "getAdminCustomerpreferredvaleterByid"
description: "getAdminCustomerpreferredvaleterByid"
sidebar_label: "getAdminCustomerpreferredvaleterByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVU1v4zYQ/SvEnFqAG6cpetFtu90tUhRF0KTbQ+DDWBpLRCiSOxw6dQ3992Ik+WsTd3OxxeG8Nx8cPu5AsM1QPcKHkiX2xHdMa2Km5jN6EuL3Te8CLC0kZOzVou47CNgTVFDPsNsGLLgAFSSUDiwwfSmOqYFKuJCFXHfUI1Q7kG1SpAtCLTFYWEfuUSbTjzcwDEuF5xRDpqyIm+tr/Wso1+ySuKhx7ktdU84wDBYy1YWdbMfMVoRMDNXjUoliIkaF3DZQQUsyFrSvNu2r3UzV/rx1Wsh5pDeCepIuzkHATn2oYIHJLVDhi/oCfrE7dnEArYY3+zYX9lBBJ5JytVj4WKPvYpZqlyLL8CLV39XBTARgYYPscOWnLipiauMai9d+/3R9/QMMw2C/jrNmyt27hjbvMLkr/LcwPdMqO6F8FcbqzsP+QhvyMfUUZB/8EmlBeSPpvWDrQvstQu829EbGO45NqXVxIF0eh+deJ3Rq1X6EDsOqIWEeYl3PDnb++LSf4N/+fhgH0oV1HOFOvPp/0lTNB2Tz/u5WD4Y4Tzltbl6k+dC5rH4mcdy4hrKh0KTogmSzjmykI9NE+YPEjLzq+vnmytzKJUSPYepljZyt/poahdrIjvZrX1Yml9UhkVP7KsYnF9psDYbGlExssEhHQVw9Xq4xOHofn/O4nY1Ek4j1apvDFcwml7ozOPkYptZlmbZGYh9bF6zxLssx2cmLWC2BnmdrQ55OnE4gZ5X5KXFT0rjVkKDz2ay258FD6VfE1mRCrrs9rbr1+ERjbn1syFtTM+Ex0ms9e+FwbF5JzbQ3m0wWlJKnIwg1ef9/zGMW+4N85QyMC7Uvje4mzPk5cmOYMkkeodSj82ZD7Nb7Q4PBQopZegw6rLOktyRm1DtzSfDMazJ5uCtC/8gieXRB+ccru5vV8BEwObAw6iFYuKSIYKE6eVmWFlT0FL/brTDTX+yHQc1fCrGq/vKodaNsNi7rdwPVGn2mF6nWMQgFvbHf/Tk/VN8bsK+XMBsxbEdJ9UVXYOGJtucv4LAcTp6BXz8+aKlFCz805yvhGJN9lX+3mzwe4hOFYTiEE11rpGH4D9HUwV4=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminCustomerpreferredvaleterByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/customerpreferredvaleter/{customerId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminCustomerpreferredvaleterByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      