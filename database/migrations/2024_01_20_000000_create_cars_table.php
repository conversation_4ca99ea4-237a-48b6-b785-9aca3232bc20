<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('car_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->unique();
            $table->text('description')->nullable();
            $table->decimal('price_multiplier', 5, 2)->default(1.00);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            $table->index('is_active');
        });

        Schema::create('cars', function (Blueprint $table) {
            $table->id();
            $table->string('registration_number', 15)->unique();
            $table->string('make_and_model', 150);
            $table->string('make', 50);
            $table->string('model', 100);
            $table->foreignId('category_id')->nullable()->constrained('car_categories');
            $table->string('colour', 50)->nullable();
            $table->year('year')->nullable();
            $table->string('fuel_type', 30)->nullable();
            $table->string('engine_size', 20)->nullable();
            $table->string('transmission', 20)->nullable();
            $table->string('vin', 17)->nullable()->unique();
            $table->date('mot_expiry')->nullable();
            $table->date('tax_expiry')->nullable();
            $table->integer('insurance_group')->nullable();
            $table->integer('co2_emissions')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index(['make', 'model']);
            $table->index('category_id');
            $table->index('year');

            // Skip fulltext indexes for SQLite (used in testing)
            if (config('database.default') !== 'sqlite') {
                $table->fullText(['make_and_model', 'make', 'model']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cars');
        Schema::dropIfExists('car_categories');
    }
};
