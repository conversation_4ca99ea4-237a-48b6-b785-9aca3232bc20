import Link from "@docusaurus/Link";
import Heading from "@theme/Heading";
import clsx from "clsx";
import * as React from "react";
import styles from "./styles.module.css";

type FeatureItem = {
    title: string;
    Svg: React.ComponentType<React.ComponentProps<"svg">>;
    description: JSX.Element;
    link: string;
    jobTitle?: string;
};

const FeatureList: FeatureItem[] = [
    {
        title: "I'm a Designer",
        Svg: require("@site/static/img/undraw_innovative_re_rr5i.svg").default,
        description: (
            <>
                You make things work for the user. You are the user advocate.
                Your work gives us the foundation for what we build
            </>
        ),
        link: "docs/intro",
    },
    {
        title: "I'm a Developer",
        jobTitle: "Developer",
        Svg: require("@site/static/img/undraw_react_re_g3ui.svg").default,
        description: (
            <>
                You're the deliverer. You make the designs come to life. You are
                the cog in the wheel that no one can do without
            </>
        ),
        link: "docs/intro",
    },
    {
        title: "I'm a QA",
        jobTitle: "QA",
        Svg: require("@site/static/img/undraw_dev_productivity_re_fylf.svg")
            .default,
        description: (
            <>
                You are probably the most important person on the team. You make
                sure that what we build is what we intended to build
            </>
        ),
        link: "docs/intro",
    },
    {
        title: "What about me?",
        jobTitle: "Product Manager",
        Svg: require("@site/static/img/undraw_multitasking_re_ffpb.svg")
            .default,
        description: (
            <>
                PM?, well you're the glue that holds us all together. You make
                sure that we stay focused building what the user wants, and all
                working towards the same goal doing our very best work
            </>
        ),
        link: "docs/intro",
    },
    {
        title: "Really???",
        jobTitle: "Tech Lead",
        Svg: require("@site/static/img/undraw_os_upgrade_re_r0qa.svg").default,
        description: (
            <>
                Oh, yes, Tech Lead/Solutions Architect, well you are the one
                that makes sure that we are all working to deliver for the
                founder in the most efficient and cost effective way possible
            </>
        ),
        link: "docs/intro",
    },
    {
        title: "Founder(s)",
        jobTitle: "User",
        Svg: require("@site/static/img/undraw_positive_attitude_re_wu7d.svg")
            .default,
        description: (
            <>
                To quote the great Weyoun, "The Founders <em>are</em> gods". We
                live to serve you and your team. There's a place for you nere
                too. Your feedback is invaluable.
            </>
        ),
        link: "docs/user-guides/overview",
    },
];

function Feature({ title, Svg, description, link, jobTitle }: FeatureItem) {
    return (
        <div className={clsx("col col--4")}>
            <div className="text--center">
                <Svg className={styles.featureSvg} role="img" />
            </div>
            <div className="text--center padding-horiz--md">
                <Heading as="h3">
                    <a href={link}>{title}</a>
                </Heading>
                <p>{description}</p>
                <div className={styles.buttons}>
                    <Link
                        className="button button--primary button--lg"
                        to={link}
                    >
                        View {jobTitle ? jobTitle : ""} Docs
                    </Link>
                </div>
            </div>
        </div>
    );
}

export default function HomepageFeatures(): JSX.Element {
    return (
        <section className={styles.features}>
            <div className="container">
                <div className="row">
                    {FeatureList.map((props, idx) => (
                        <Feature key={idx} {...props} />
                    ))}
                </div>
            </div>
        </section>
    );
}
