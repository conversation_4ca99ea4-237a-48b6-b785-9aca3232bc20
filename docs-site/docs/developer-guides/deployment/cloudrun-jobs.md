---
sidebar_position: 3
---

# Cloud Run Jobs

This document provides comprehensive documentation for running Laravel Artisan commands using Google Cloud Run Jobs in the Fresh Car Core project. This setup provides a scalable, serverless solution for background tasks and maintenance operations across staging and production environments.

## 🚀 Quick Start

### Prerequisites

Before setting up Cloud Run Jobs, ensure you have:

1. **Google Cloud CLI installed and configured** (see [Google Cloud Setup Guide](#-google-cloud-setup-guide))
2. **Project access** with appropriate permissions
3. **Existing secrets configured** in Secret Manager (production-\* and staging-\* prefixed)

### One-Time Setup

Run the automated setup script:

```bash
./scripts/setup-cloud-run-jobs.sh
```

This script will:

- ✅ Enable required Google Cloud APIs (Cloud Run, Secret Manager, Cloud SQL)
- ✅ Configure IAM permissions for the service account
- ✅ Verify existing Secret Manager secrets
- ✅ Deploy all job configurations for both environments
- ✅ Set up monitoring and logging

### Environment Support

The system supports both **staging** and **production** environments with separate:

- **Job configurations** (`cloud-run-jobs/staging/` and `cloud-run-jobs/production/`)
- **Secret names** (`staging-*` and `production-*` prefixes)
- **Resource allocations** (production has higher limits)
- **Environment variables** and database connections

**Important**: Secrets are already configured in the project. The setup script verifies they exist rather than creating them.

## 📋 Daily Usage

### Environment-Specific Wrappers

The system provides simple wrapper scripts for different environments:

```bash
# Staging (default environment)
./artisan-cloud migrate
./artisan-staging migrate

# Production
./artisan-production migrate
ENVIRONMENT=production ./artisan-cloud migrate
```

### Common Commands

```bash
# Database migrations
./artisan-cloud migrate                       # Staging
./artisan-production migrate                  # Production

# Queue workers
./artisan-cloud queue:work                    # Staging
./artisan-production queue:work emails        # Production

# Cache management
./artisan-cloud cache:clear                   # Staging
./artisan-production cache:clear              # Production

# Custom Laravel commands
./artisan-cloud user:create                           # Staging
./artisan-production search:reindex User              # Production
./artisan-production app:gohighlevel:refresh-tokens   # Production
```

### Advanced Usage with Parameters

```bash

# Cache operations with specific options
./scripts/cloud-run-jobs.sh cache:clear true true false

# Custom command with arguments
./scripts/cloud-run-jobs.sh artisan search:reindex User --force
```

## 📁 What's Included

### Job Configurations

- **Migration Job** - Database migrations and schema updates
- **Cache Clear Job** - Cache management and optimization
- **Custom Command Job** - Any Laravel Artisan command

### Management Scripts

- **`artisan-cloud`** - Environment-aware wrapper for running commands
- **`artisan-staging`** - Staging-specific wrapper
- **`artisan-production`** - Production-specific wrapper
- **`scripts/cloud-run-jobs.sh`** - Advanced job execution with parameters
- **`scripts/job-monitor.sh`** - Real-time monitoring and log viewing
- **`scripts/deploy-jobs.sh`** - Job deployment and configuration management

## 🔧 Advanced Usage

### Monitor Job Execution

```bash
# Check job status
./scripts/job-monitor.sh status laravel-migrate-job

# Monitor execution in real-time
./scripts/job-monitor.sh monitor laravel-queue-worker-job

# View job logs
./scripts/job-monitor.sh logs laravel-migrate-job

# Get job metrics
./scripts/job-monitor.sh metrics laravel-migrate-job 24
```

### Custom Job Parameters

```bash
# Queue worker with custom settings
./scripts/cloud-run-jobs.sh queue:work emails 600 50 5

# Cache operations with options
./scripts/cloud-run-jobs.sh cache:clear true true false

# Custom command with arguments
./scripts/cloud-run-jobs.sh artisan search:reindex User --force
```

### Create New Job Types

```bash
# Create a new job configuration
./scripts/deploy-jobs.sh create user-cleanup "user:cleanup --days=30" 3600 1Gi 2000m

# Deploy the new job
./scripts/deploy-jobs.sh deploy cloud-run-jobs/user-cleanup-job.yaml
```

## 🏗️ Architecture

```text
Local Development          Cloud Infrastructure
┌─────────────────┐       ┌─────────────────────┐
│   artisan-cloud │────▶  │   Cloud Run Jobs    │
│   (wrapper)     │       │   ┌─────────────┐   │
└─────────────────┘       │   │ Migration   │   │
                          │   │ Queue       │   │
┌─────────────────┐       │   │ Cache       │   │
│   job-monitor   │◀──────│   │ Custom      │   │
│   (monitoring)  │       │   └─────────────┘   │
└─────────────────┘       └─────────────────────┘
                                     │
                          ┌─────────────────────┐
                          │   Laravel App       │
                          │   ┌─────────────┐   │
                          │   │ Database    │   │
                          │   │ Storage     │   │
                          │   │ Secrets     │   │
                          │   └─────────────┘   │
                          └─────────────────────┘
```

## 🔐 Security Features

- **Secret Manager Integration** - Sensitive data stored securely
- **IAM-based Access Control** - Proper service account permissions
- **Network Security** - Private Cloud SQL connections
- **Audit Logging** - All executions logged to Cloud Logging

## 📊 Monitoring & Alerting

### Built-in Monitoring

- Execution status tracking
- Resource usage monitoring
- Error rate tracking
- Performance metrics

### Optional Alerting

- Job failure notifications
- Long-running job alerts
- Resource usage warnings

## 💰 Cost Optimization

### Resource Right-Sizing

Jobs are configured with appropriate resource limits:

- **Short tasks**: 0.5 CPU, 256Mi memory
- **Medium tasks**: 1 CPU, 512Mi memory
- **Long tasks**: 2 CPU, 1Gi memory

### Pay-per-Use Model

- Only pay when jobs are running
- No idle resource costs
- Automatic scaling to zero

## 🔄 CI/CD Integration

Jobs are automatically updated when you deploy:

1. **Code Changes** → Push to repository
2. **Cloud Build** → Builds new container image
3. **Auto-Deploy** → Updates both service and jobs
4. **Ready to Use** → New code available in jobs

## 🆘 Troubleshooting

### Common Issues

**Job won't start:**

```bash
./scripts/job-monitor.sh config laravel-migrate-job
./scripts/deploy-jobs.sh validate
```

**Database connection issues:**

```bash
gcloud sql instances describe your-instance
```

**Permission errors:**

```bash
gcloud secrets versions access latest --secret="app-key"
```

### Getting Help

1. **Check the logs**: `./scripts/job-monitor.sh logs <job-name>`
2. **Validate config**: `./scripts/deploy-jobs.sh validate`
3. **Review documentation**: `docs/CLOUD_RUN_JOBS.md`

## 📚 Available Commands

### Your Custom Laravel Commands

- `user:create` - Create application users
- `search:reindex` - Rebuild search indexes
- `app:gohighlevel:refresh-tokens` - Refresh API tokens
- `app:gohighlevel:receive-tokens` - Process API callbacks
- `app:gohighlevel:auth-url` - Generate auth URLs

### Standard Laravel Commands

- `migrate` - Database migrations
- `migrate:seed` - Migrations with seeders
- `queue:work` - Process queued jobs
- `cache:clear` - Clear application caches
- `config:cache` - Cache configuration
- `route:cache` - Cache routes
- `view:cache` - Cache views

## 🎯 Next Steps

1. **Complete Setup**: Run `./scripts/setup-cloud-run-jobs.sh`
2. **Test Basic Commands**: Try `./artisan-cloud migrate`
3. **Explore Monitoring**: Use `./scripts/job-monitor.sh`
4. **Create Custom Jobs**: Add your own commands
5. **Set Up Scheduling**: Use Cloud Scheduler for automation
6. **Configure Alerts**: Set up monitoring for production

---

## 🔧 Google Cloud Setup Guide

### 1. Create Google Cloud Account

If you don't have a Google Cloud account:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Sign up with your Google account
3. Accept the terms of service
4. Set up billing (required for Cloud Run and other services)

### 2. Install Google Cloud CLI

#### macOS (using Homebrew)

```bash
brew install --cask google-cloud-sdk
```

#### macOS/Linux (using installer)

```bash
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
```

#### Windows

Download and run the installer from [Google Cloud SDK page](https://cloud.google.com/sdk/docs/install)

### 3. Initialize and Authenticate

```bash
# Initialize gcloud CLI
gcloud init

# Authenticate with your Google account
gcloud auth login

# Set up application default credentials
gcloud auth application-default login
```

### 4. Configure Project Access

Contact your project administrator to get access to the Fresh Car project. You'll need:

- **Project ID**: `fresh-car-test`
- **IAM roles**: Developer access or equivalent
- **Region**: `europe-west1` (London)

Once you have access:

```bash
# Set the project
gcloud config set project fresh-car-test

# Set the default region
gcloud config set run/region europe-west1

# Verify your setup
gcloud config list
```

### 5. Verify Access

Test that you can access the required services:

```bash
# Test Cloud Run access
gcloud run services list --region=europe-west1

# Test Secret Manager access
gcloud secrets list

# Test Cloud SQL access
gcloud sql instances list
```

If any of these commands fail, contact your administrator for the appropriate permissions.

**Ready to get started?** Run `./scripts/setup-cloud-run-jobs.sh` and follow the prompts!
