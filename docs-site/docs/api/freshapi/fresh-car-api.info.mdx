---
id: fresh-car-api
title: "Fresh Car API"
description: "_This API has been developed to replace the dotNet FreshAPI V2, and is not yet in used by the client apps._"
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import Api<PERSON>ogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: 3.0.0"}
>
</span>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Fresh Car API"}
>
</Heading>

<ApiLogo
  logo={{"url":"/img/freshcar-logo.png","backgroundColor":"#FFFFFF","altText":"Fresh Car API"}}
  darkLogo={undefined}
>
  
</ApiLogo>

_This API has been developed to replace the dotNet FreshAPI V2, and is not yet in used by the client apps._

## Summary
This API provides endpoints for managing cars, car categories, car club
subscriptions, car club bookings, and user authentication. It allows users
to perform operations such as user registration and login, listing cars,
registering new cars, deleting cars, listing car categories, looking up car
details by registration number, searching cars by make and model, creating
car club subscriptions, creating car club bookings, updating booking

## Cross-Origin Resource Sharing (CORS)
This API supports Cross-Origin Resource Sharing (CORS) for cross-domain requests.

## Authentication
All endpoints require authentication using Laravel Sanctum. Include the bearer token in the Authorization header:
```
Authorization: Bearer {your-token}
```


<div
  style={{"marginBottom":"2rem"}}
>
  <Heading
    id={"authentication"}
    as={"h2"}
    className={"openapi-tabs__heading"}
    children={"Authentication"}
  >
  </Heading><SchemaTabs
    className={"openapi-tabs__security-schemes"}
  >
    <TabItem
      label={"HTTP: Bearer Auth"}
      value={"bearer"}
    >
      
      
      JWT Authorization header using the Bearer scheme. Example: 'Authorization: Bearer \{token\}'
      
      <div>
        <table>
          <tbody>
            <tr>
              <th>
                Security Scheme Type:
              </th><td>
                http
              </td>
            </tr><tr>
              <th>
                HTTP Authorization Scheme:
              </th><td>
                bearer
              </td>
            </tr><tr>
              <th>
                Bearer format:
              </th><td>
                JWT
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </TabItem>
  </SchemaTabs>
</div><div
  style={{"display":"flex","flexDirection":"column","marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Contact
  </h3><span>
    The Lightning Group: 
  </span><span>
    URL: [https://www.thelightninggroup.co.uk](https://www.thelightninggroup.co.uk)
  </span>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Terms of Service
  </h3><a
    href={"https://github.com/founderandlightning/freshcar-lbp-core/blob/main/LICENSE"}
  >
    {'https://github.com/founderandlightning/freshcar-lbp-core/blob/main/LICENSE'}
  </a>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    License
  </h3><a
    href={"https://opensource.org/licenses/MIT"}
  >
    MIT
  </a>
</div>
      