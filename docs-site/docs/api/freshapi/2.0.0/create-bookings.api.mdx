---
id: create-bookings
title: "createBookings"
description: "createBookings"
sidebar_label: "createBookings"
hide_title: true
hide_table_of_contents: true
api: eJztWktz2zYQ/isYHFvaptVm2vKWyEnrThNrYrWdqcYHiFxJiEGAARZyVI3+e2cBSqReDp1eebHFxWLfj0+PNUcxdzyb8DfGPEo95w8Jt/DZg8M3pljxbM1zoxE00ktRVUrmAqXRV5+c0URz+QJKQa/onrRQkDhRFBacuy14wqdR9D0K9JFSa4DiRiCQSlxVwDNupp8gR57wypoKLEpwJLgWcFvQQ80qNcIcLE/4zNhSYCT9MOAJ114pMVXAM7QeNofqGhkOLbncElEIhAuUJfBNQo7PpC1fdO2E9iYUHayP/JIiLNTQlCVodKd0H6nRBqEbZ2VhBtZCMSaLv9mvw7R28w4NkmMOW/zal9N99sJ4UrVJOGgqqlXU0sk9C854m0Nnk7YXPojT4TjSQBG5m92IVSfuOk4fKeigc/gQvX3B1d/89Mbb0HbvpfZd82yWYK0sYGRlDl3Cfd6CoXdoSrBDYdu6hbVixRMuEco2/fk+bgnrnKMXjQDq3W/QUYn8UczhV2t89YLimUuHMTkvyGwpHuG1Lt6bAlSnC7lAmBvbreRyo4zvZsgKRDdGU8Wh9PYLWvE/iqCO8i1C2THIezNx1BI2E8oBtSOSndsVNmoU3LVtvhnfUcIOHHuh8Fbt1npOi024dCMLlZBtH6fGKBCah+OPMPO6gDPnrQIeKj/dOvWtKzD3loZPKB/QvuTZJE2um837jKxtBIZbES+NWYgQjU34gj1s6GFDDxt62NDDhh429LChhw0dYEP7Q4fvvu/xQ48fevzQ44ceP/T4occPPX74Gn7YxElbGe1iIQzSlP4V4HIrQ6J4xu99noNzQYeD3FuJK55N1nwKwlJvTR42D1SMEFuOAsJzCwKh1uZ4ciDz6LgEXBi6WNEeosbHBc/4lajk1bRhc2CXQPNusubeKp7xBWLlsqsrZXKhFsZhtq6Mxc2Ryj+IgUUBPOFLYSWlIzaAsRgdnwmvKNKv0vSaIpQc6plZcIuLApYXopKX4l9v4QmmTiK4Sw14pPYGlqBMRaBhq/ycUC+wo9B7FHOp518TqOQSOkocWVP4nB52Qh+adN8ToKwXRp30XcmTSspMYKGGiQxJ/eLdtnZ//3scSohA6sfmW7S3X0RZKTjAlOkRQuSDdPDjRfrTxeCX8fWr7NV1Nvj5Mv3p+h9+hAqfY20hv/Q0rmvN2wjfGsIBSntOzxESS/dwVnqEohotbbCUHkKhhq2FeBriOWBzxHEKvzRMBzAlPQM6JufwQ5ocJDM/Oj/c7Onpvd3YtL+eG3qzhVu0etk2lLhTWx4erM7J0RZMN2GstXYGze39LREp5/dC2p7yKa0YPTOhd+px/I76lA2FZa9HtzSVwLrYkMvBUY+OF9IRH6usWcoCHANdVEZqdGxmLMMFsMLgB0AW5BLrX4NLdovnbpRCx0GSC+sS+svqcErYPis/Zc5Pd4a06dvJnDChC+YdWCY8LkBj/aY0KBdKmScXjh1DwyqwtNHYbmM45ny+YCLysHYdBMHKzKVOmJIOG2MjF1A+mYanmlqAghZT68qeZyoaznwVjgpAIZVj09W+8gjLE+ZA2HyxFUtsVI3BtpLqMWFhoe00nYrZEUMTPF8V8awmMReGQkyBzkGp5yQHK7aJPJEDJnWufEGnlXDuydiCWXCALlyFUkjFlmDlbJu0gLyNw1KEjxN0nDxxZ7NzO323EMJXGZUSMoCmsJfW9T6fcFHJZgg5+pxhEQbihK/XU+HgT6s2GyJ/9kA9PXloVnVAGglfgCgC7FjzR6C2H8YfYVyMST+xKx9A8OEPMqiddzhjdHc/DpbEH3JQGnnGrXiiCSyeeMZ3UyLsvUBbcyX03Is58UaZAXl5cq6BjPsbMFi6BeZ61bJwvY4cY/MIekOQJTqE9Mw3D5vN5j9NFQfm
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createBookings"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/bookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createBookings

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"}},"text/json":{"schema":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"}},"application/*+json":{"schema":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      