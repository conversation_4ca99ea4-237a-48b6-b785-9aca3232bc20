---
id: get-bookings
title: "getBookings"
description: "getBookings"
sidebar_label: "getBookings"
hide_title: true
hide_table_of_contents: true
api: eJyNVMFu3DgM/RWBZzeTDdCLb9vdbZGiWBRI2j0Ec6Btji1ElrQkNdmp4X9fSB5nJknT9jIekY/vUaTICRR7gfoO3oVwb30P2wqYJAYvJFBPcHV5mT8dScs2qg0earhJbUsiMM8VCLWJrR6gvpugIWRiqO+287aCEIkxh1x3UENPehQRqJ4RPvWNpEM4hkAFEXWAGjYY7aY5oYR4TyxFN7GDGgbVKPVm40KLbgii9RQD6/xC7lMGmIUAKtgjW2zccuEcsdx4h8kp1PD28vI3mOe5eq6zY5LhTUf7NxjtBX5LTA/UiFWSC19Sfyr7J+3JhTiS11X8NdKE+oukN4q99f3PCJ3d0y8yfubQpTYfHkm3pz7ftAONS6nWbk+gh0hHydyZAoF6BVTHP+8Dj5gr+vGf2/J2rN+FEm7VZfz7nKr5A9n8/vk6N4ZYlpz2Vy/SvB2sZJyJHPa2IzHkuxisVzG7wEYHMl3Qv0lN4c3Qr1cX5lpfixjRL7VskaXKv6ZFpT6wpfXsUmMkNY+JnNvXx1kZ9J1JQmww6UBebVvmoIijc+FBiluMBhOJd4FH8zgtYiS1g8EFY5h6K7q4CrELvfWVcVb0lOyCIs4WTw9Ha0eOzkBnIU9u5pbETYrF1ZGidWKaw1Nxn8aGuDJCyO2w0mbYiPdUchtDR64yLROelL5XsxeAU/FS7Bbf0WREUZMsLfAtOfcj5pLF2sjv9MBY37rUZW9EkYfAnWESUimhNKJ1Zk9sd2vTYK4gBtERfX6sHsvb7knNa/vscSCU/tNNdGh9JilzOR332R1gtHk0Vo5tBXlnZc80NSj0hd08Z/O/iTjv1+1pVZUVe7YpP/x1CxXkm56pPxu/simPLvSHsvhcyqdpWhC34Z78nPflPR1y+vkM83ae5/8B6AMvxw==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getBookings"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getBookings

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      