import type * as Preset from "@docusaurus/preset-classic";
import type { Config } from "@docusaurus/types";
import type * as OpenApiPlugin from "docusaurus-plugin-openapi-docs";
import { themes as prismThemes } from "prism-react-renderer";

const config: Config = {
    title: "Fresh Car Core Documentation",
    tagline: "Laravel Application Documentation",
    favicon: "img/favicon.webp",

    // Future flags, see https://docusaurus.io/docs/api/docusaurus-config#future
    future: {
        v4: true, // Improve compatibility with the upcoming Docusaurus v4
    },

    // Set the production url of your site here
    url: "https://your-domain.com",
    // Set the /<baseUrl>/ pathname under which your site is served
    // For Laravel integration, we'll serve under /admin/docs
    baseUrl: "/admin/docs/",

    // GitHub pages deployment config.
    // If you aren't using GitHub pages, you don't need these.
    organizationName: "founderandlightning", // Usually your GitHub org/user name.
    projectName: "freshcar-lbp-core", // Usually your repo name.

    onBrokenLinks: "warn",
    onBrokenMarkdownLinks: "warn",

    // Even if you don't use internationalization, you can use this field to set
    // useful metadata like html lang. For example, if your site is Chinese, you
    // may want to replace "en" with "zh-Hans".
    i18n: {
        defaultLocale: "en",
        locales: ["en"],
    },
    plugins: [
        [
            "docusaurus-plugin-openapi-docs",
            {
                id: "api",
                docsPluginId: "classic",
                config: {
                    freshapi: {
                        specPath: "../openapi/freshcar_v3.yaml",
                        outputDir: "docs/api/freshapi",
                        sidebarOptions: {
                            groupPathsBy: "tag",
                            categoryLinkSource: "tag",
                        },
                        version: "3.0.0",
                        label: "v3.0.0", // Current version label
                        baseUrl: "/docs/api/freshapi/3.0.0/freshcar_v3", // Leading slash is important

                        versions: {
                            "2.0.0": {
                                specPath: "../openapi/freshcar_v2.yaml",
                                outputDir: "docs/api/freshapi/2.0.0", // No trailing slash
                                label: "v2.0.0",
                                baseUrl: "/docs/api/freshapi/2.0.0/freshcar_v2", // Leading slash is important
                            },
                        },
                    } satisfies OpenApiPlugin.Options,
                    // freshapiv2: {
                    //     specPath: "../openapi/freshcar_v2.json",
                    //     outputDir: "docs/api/freshapi/v2",
                    //     sidebarOptions: {
                    //         groupPathsBy: "tag",
                    //         categoryLinkSource: "tag",
                    //     },
                    // },
                },
            },
        ],
    ],

    presets: [
        [
            "classic",
            {
                docs: {
                    sidebarPath: "./sidebars.ts",
                    // Remove edit links since this is internal documentation
                    editUrl: undefined,
                    docItemComponent: "@theme/ApiItem",
                },
                blog: false, // Disable blog functionality
                theme: {
                    customCss: "./src/css/custom.css",
                },
            } satisfies Preset.Options,
        ],
    ],
    themes: ["docusaurus-theme-openapi-docs"],
    themeConfig: {
        languageTabs: [
            {
                highlight: "javascript",
                language: "nodejs",
                logoClass: "nodejs",
            },

            {
                highlight: "php",
                language: "php",
                logoClass: "php",
            },

            {
                highlight: "dart",
                language: "dart",
                logoClass: "dart",
            },
            {
                highlight: "python",
                language: "python",
                logoClass: "python",
            },
            {
                highlight: "bash",
                language: "curl",
                logoClass: "bash",
            },
        ],
        // Replace with your project's social card
        image: "img/docusaurus-social-card.jpg",
        navbar: {
            title: "Fresh Car Admin",
            logo: {
                alt: "Fresh Car Logo",
                src: "img/freshcar-logo.png",
            },
            items: [
                // {
                //     type: "docSidebar",
                //     sidebarId: "freshcarSidebar",
                //     position: "left",
                //     label: "Documentation",
                // },
                {
                    type: "html",
                    position: "right",
                    value: '<a href="/admin" onclick="window.location.href=\'/admin\'; return false;" style="color: var(--ifm-navbar-link-color); text-decoration: none; padding: 0.25rem 0.5rem;">Back to Admin</a>',
                },
            ],
        },
        footer: {
            style: "dark",
            links: [
                {
                    title: "Documentation",
                    items: [
                        {
                            label: "Getting Started",
                            to: "docs/intro",
                        },
                    ],
                },
                {
                    title: "Admin",
                    items: [
                        {
                            html: '<a href="/admin" onclick="window.location.href=\'/admin\'; return false;">Dashboard</a>',
                        },
                        {
                            html: '<a href="/admin/users" onclick="window.location.href=\'/admin/users\'; return false;">Users</a>',
                        },
                        {
                            html: '<a href="/admin/features" onclick="window.location.href=\'/admin/features\'; return false;">Features</a>',
                        },
                    ],
                },
            ],
            copyright: `Copyright © ${new Date().getFullYear()} Founder and Ligntning. Built with Docusaurus.`,
        },
        prism: {
            theme: prismThemes.github,
            darkTheme: prismThemes.dracula,
        },
    } satisfies Preset.ThemeConfig,
};

export default config;
