#!/bin/bash

# Cloud Run Jobs Setup Script
# This script sets up the complete Cloud Run Jobs environment for Laravel

set -e

# Configuration
PROJECT_ID=${1:-$(gcloud config get-value project)}
REGION=${2:-"europe-west1"}
ENVIRONMENT=${2:-"staging"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_header "Checking Prerequisites"

    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi

    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found. Please run 'gcloud auth login'"
        exit 1
    fi

    # Check project
    if [ -z "$PROJECT_ID" ]; then
        log_error "No Google Cloud project set. Please run 'gcloud config set project YOUR_PROJECT_ID'"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Enable required APIs
enable_apis() {
    log_header "Enabling Required APIs"

    local apis=(
        "run.googleapis.com"
        "cloudbuild.googleapis.com"
        "secretmanager.googleapis.com"
        "logging.googleapis.com"
        "monitoring.googleapis.com"
        "sqladmin.googleapis.com"
    )

    for api in "${apis[@]}"; do
        log_info "Enabling $api..."
        gcloud services enable "$api" --project="$PROJECT_ID"
    done

    log_success "All required APIs enabled"
}

# Set up IAM permissions
setup_iam() {
    log_header "Setting up IAM Permissions"

    local service_account="tlg-devops@$PROJECT_ID.iam.gserviceaccount.com"

    # Calculate expiration time (1 hour from now) - compatible with both Linux and macOS
    local expiration_time
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        expiration_time=$(date -u -v+1H '+%Y-%m-%dT%H:%M:%SZ')
    else
        # Linux
        expiration_time=$(date -u -d '+1 hour' '+%Y-%m-%dT%H:%M:%SZ')
    fi
    log_info "Setting IAM permissions to expire at: $expiration_time"

    # Required roles for Cloud Run Jobs
    local roles=(
        "roles/cloudsql.client"
        "roles/secretmanager.secretAccessor"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
    )

    for role in "${roles[@]}"; do
        log_info "Adding role $role to service account..."
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$service_account" \
            --role="$role" \
            --condition="expression=request.time < timestamp('$expiration_time'),title=Deployment-Access,description=Temporary access for Cloud Run Jobs deployment" \
            --quiet
    done

    log_success "IAM permissions configured (expires in 1 hour)"
}

# Clean up expired IAM permissions
cleanup_iam() {
    log_header "Cleaning up expired IAM permissions"

    local service_account="tlg-devops@$PROJECT_ID.iam.gserviceaccount.com"
    local roles=(
        "roles/cloudsql.client"
        "roles/secretmanager.secretAccessor"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
    )

    for role in "${roles[@]}"; do
        log_info "Removing expired $role from service account..."
        # Try to remove any existing conditional bindings for this service account and role
        gcloud projects get-iam-policy "$PROJECT_ID" --format="json" | \
        jq -r --arg sa "serviceAccount:$service_account" --arg role "$role" \
        '.bindings[] | select(.role == $role and (.members[]? == $sa) and .condition) | .condition | "expression=\(.expression),title=\(.title),description=\(.description)"' | \
        while read -r condition; do
            if [ -n "$condition" ]; then
                gcloud projects remove-iam-policy-binding "$PROJECT_ID" \
                    --member="serviceAccount:$service_account" \
                    --role="$role" \
                    --condition="$condition" \
                    --quiet 2>/dev/null || true
            fi
        done
    done

    log_success "Expired IAM permissions cleaned up"
}

# Verify required secrets exist
verify_secrets() {
    log_header "Verifying Required Secrets"

    local missing_secrets=()

    # Check production secrets
    # local prod_secrets=("app-key" "db-password" "stripe-secret")
    # for secret in "${prod_secrets[@]}"; do
    #     if ! gcloud secrets describe "$secret" --project="$PROJECT_ID" >/dev/null 2>&1; then
    #         missing_secrets+=("$secret (production)")
    #     fi
    # done
    # Check staging secrets
    local staging_secrets=("staging-app-key" "staging-db-password" "staging-stripe-secret")
    for secret in "${staging_secrets[@]}"; do
        if ! gcloud secrets describe "$secret" --project="$PROJECT_ID" >/dev/null 2>&1; then
            missing_secrets+=("$secret (staging)")
        fi
    done

    if [ ${#missing_secrets[@]} -gt 0 ]; then
        log_error "Missing required secrets:"
        for secret in "${missing_secrets[@]}"; do
            echo "  - $secret"
        done
        log_warning "Please ensure all secrets are created before proceeding"
        return 1
    fi

    log_success "All required secrets are available"
}

# Deploy job configurations
deploy_jobs() {
    log_header "Deploying Cloud Run Jobs"

    if [ ! -f "scripts/deploy-jobs.sh" ]; then
        log_error "deploy-jobs.sh script not found"
        return 1
    fi

    # Make sure the script is executable
    chmod +x scripts/deploy-jobs.sh

    # Set environment variables for deployment
    export PROJECT_ID="$PROJECT_ID"
    export REGION="$REGION"
    export ENVIRONMENT="$ENVIRONMENT"

    # Deploy staging jobs
    log_info "Deploying staging jobs..."
    if ENVIRONMENT=staging ./scripts/deploy-jobs.sh deploy; then
        log_success "Staging jobs deployed successfully"
    else
        log_error "Failed to deploy staging jobs"
        cleanup_iam
        return 1
    fi

    # Deploy production jobs
    log_info "Deploying production jobs..."
    if ENVIRONMENT=production ./scripts/deploy-jobs.sh deploy; then
        log_success "Production jobs deployed successfully"
    else
        log_error "Failed to deploy production jobs"
        cleanup_iam
        return 1
    fi

    # Clean up temporary IAM permissions after successful deployment
    cleanup_iam
}

# Create Cloud Scheduler jobs for regular tasks
setup_scheduler() {
    log_header "Setting up Cloud Scheduler (Optional)"

    read -p "Do you want to set up scheduled jobs? (y/N): " -n 1 -r
    echo

    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Skipping Cloud Scheduler setup"
        return 0
    fi

    # Enable Cloud Scheduler API
    gcloud services enable cloudscheduler.googleapis.com --project="$PROJECT_ID"

    # Create a daily migration check (optional)
    log_info "Creating daily migration check..."
    gcloud scheduler jobs create http daily-migration-check \
        --schedule="0 2 * * *" \
        --uri="https://run.googleapis.com/apis/run.googleapis.com/v1/namespaces/$PROJECT_ID/jobs/laravel-migrate-job:run" \
        --http-method=POST \
        --oauth-service-account-email="$<EMAIL>" \
        --location="$REGION" \
        --project="$PROJECT_ID" \
        --quiet || log_warning "Failed to create scheduled job (may already exist)"

    log_success "Cloud Scheduler configured"
}

# Set up monitoring and alerting
setup_monitoring() {
    log_header "Setting up Monitoring and Alerting (Optional)"

    read -p "Do you want to set up basic monitoring? (y/N): " -n 1 -r
    echo

    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Skipping monitoring setup"
        return 0
    fi

    # Create a notification channel (email)
    read -p "Enter email for alerts: " email

    if [ -n "$email" ]; then
        log_info "Creating notification channel..."
        gcloud alpha monitoring channels create \
            --display-name="Cloud Run Jobs Alerts" \
            --type=email \
            --channel-labels=email_address="$email" \
            --project="$PROJECT_ID" \
            --quiet || log_warning "Failed to create notification channel"
    fi

    log_success "Basic monitoring configured"
}

# Create helpful aliases
create_aliases() {
    log_header "Creating Helpful Aliases"

    local alias_file="$HOME/.cloud-run-jobs-aliases"

    cat > "$alias_file" << 'EOF'
# Cloud Run Jobs Aliases
alias crj-migrate='./scripts/cloud-run-jobs.sh migrate'
alias crj-migrate-seed='./scripts/cloud-run-jobs.sh migrate:seed'
alias crj-queue='./scripts/cloud-run-jobs.sh queue:work'
alias crj-cache='./scripts/cloud-run-jobs.sh cache:clear'
alias crj-artisan='./scripts/cloud-run-jobs.sh artisan'
alias crj-status='./scripts/job-monitor.sh status'
alias crj-monitor='./scripts/job-monitor.sh monitor'
alias crj-logs='./scripts/job-monitor.sh logs'
alias crj-deploy='./scripts/deploy-jobs.sh deploy'
EOF

    log_info "Aliases created in $alias_file"
    log_info "Add 'source $alias_file' to your shell profile to use them"

    # Show example usage
    echo ""
    log_info "Example usage with aliases:"
    echo "  crj-migrate              # Run migrations"
    echo "  crj-queue default        # Start queue worker"
    echo "  crj-artisan user:create  # Run custom command"
    echo "  crj-status laravel-migrate-job  # Check job status"
}

# Main setup function
main() {
    log_header "Cloud Run Jobs Setup for Laravel"
    echo "Project: $PROJECT_ID"
    echo "Region: $REGION"
    echo "Environment: $ENVIRONMENT"
    echo ""

    check_prerequisites
    enable_apis
    setup_iam
    verify_secrets
    deploy_jobs
    setup_scheduler
    setup_monitoring
    create_aliases

    log_header "Setup Complete!"
    echo ""
    log_success "Cloud Run Jobs are now configured for your Laravel application"
    echo ""
    log_info "Next steps:"
    echo "1. Test the setup:"
    echo "   # Production"
    echo "   ./artisan-cloud migrate"
    echo "   ENVIRONMENT=production ./scripts/cloud-run-jobs.sh migrate"
    echo ""
    echo "   # Staging"
    echo "   ENVIRONMENT=staging ./artisan-cloud migrate"
    echo "   ENVIRONMENT=staging ./scripts/cloud-run-jobs.sh migrate"
    echo ""
    echo "2. Monitor jobs:"
    echo "   ./scripts/job-monitor.sh status laravel-migrate-job-production"
    echo "   ENVIRONMENT=staging ./scripts/job-monitor.sh status laravel-migrate-job-staging"
    echo ""
    echo "3. Read the documentation:"
    echo "   docs/CLOUD_RUN_JOBS.md"
}

# Show usage if no arguments
if [ $# -eq 0 ]; then
    echo "Usage: $0 [PROJECT_ID] [REGION]"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Use current gcloud project and default region"
    echo "  $0 my-project-id                     # Use specific project, default region"
    echo "  $0 my-project-id europe-west1       # Use specific project and region"
    echo ""
    read -p "Continue with current settings? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 0
    fi
fi

# Run main function
main
