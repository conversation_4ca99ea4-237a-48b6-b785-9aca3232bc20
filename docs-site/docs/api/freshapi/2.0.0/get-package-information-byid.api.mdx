---
id: get-package-information-byid
title: "getPackage-informationByid"
description: "getPackage-informationByid"
sidebar_label: "getPackage-informationByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVU1v5DYM/SsCTy2gZNIAvfjWbrtFiqIImrQ9BHOgbY4tjCxpRWrSqeH/Xkj2fCQzu93LjCU+Pj6RFDWCYMdQvcAjNlvsCNYaAkYcSChmwwgOB4IKgmdpfEugwbi8RulBQ6RPyURqoZKYSAM3PQ0I1QiyD9mPJRrXwTStM5iDd0yc7fd3d/mvJW6iCWJ8Zn1KTUPMME0amJoUjeyLipowUoTqZZ2JfKCI2eWhhQo6kkX+jXEbH4di+nFvWtDv+L8IHUh6vxCCnk9YwQqDWYVLp9V4SMkEWWzcHTKWooUKepHA1WplfYO29yzVGHyU6ULTbxmgZgLQsMNosLZzkrLHnKUNJitQwfd3d9/BNE36fZxNJO5vWtrdYDC3+G+K9Eo1GyG+deVAb8P+RDuyPgzk5BD8c6QJ5StJnwQ747r/I7RmR1/J+Bh9m5q8OJKuT73xlNttTtWhQ46dl0PC0pF5vQD08vGxFBIq+PXv59JvubbF3YjN+I9ZqvqAUf3w+JALQ5FnTbv7C5nPveGMUyH6nWmJFbk2eOOE1cZHJT2p1svvJKrwZuhf97fqQT7nMaCbc9lgZJ1/VYNCnY+GDmubasWpPgo536+93xrXsVboWpWYosIkPTkxTWngEhyt9a9czKzEq0Axd7g63jBWnJpe4YxRkTrDMpsKsfWdcVpZw3ISO6Mo33zl6HXZbcnSGejM5c3J7CxcpVBMLQkay6revw3u0lBT1IoJY9MfaDNswC0VbYNvyWrVRMJTpGs5uwCckpdCO9uWLcWCkngugWvI2i8xFxWHQl6pgTKusanN1oDMrz62KhKTcHGlAY1VO4pmcygaTLrM4gFdbtZlOnck6spgU9eG4PGCCP0jq2DRuExa7um4TL0XwGDKDLzgBA3V8TFYa8ijLTuMY41Mf0Y7TXn7U6KYR/f6NNHKcGwN5+8Wqg1apgttjXdCLt/Lb/5Y3pZvFejrmpdNdPsyOG3KK9Cwpf35kzWtp7Pp/svPz6Ah1+EsF++GQ5F6lX0cZ8Sz35KbpmMwyescaZr+A56gmMI=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getPackage-informationByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/package-information/{postcode}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getPackage-informationByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"postcode","in":"path","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      