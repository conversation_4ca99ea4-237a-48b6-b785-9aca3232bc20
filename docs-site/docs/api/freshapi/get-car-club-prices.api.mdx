---
id: get-car-club-prices
title: "Get car club prices by package and category"
description: "This endpoint retrieves the prices for a specific car club package and category."
sidebar_label: "Get car club prices by package and category"
hide_title: true
hide_table_of_contents: true
api: eJy1Vk1z2zYQ/SsY5JBkhiIVu0lb3lzHySjjJp7IbQ6RJwHBFYkxCCALQLaq4X/vLEjJkp2P9pBcHHEX+/GwePs2PIjG8/IjPxXITnWs+FXGa/ASlQvKGl7yy1Z5BqZ2VpnAEAIqWIFnoQXmUEnwbGmRCeYdSLVUkkmBTOpYMSfktWiACVMzKQI0Ftc5z7h1gILCz2pe8gbCqUBKfpHC8Yw7gaKDAEi1bbgRHfCSj+FmNc+4otK+RMA1zzjCl6gQal4GjJBxL1voBC83PKwdnVQmQAPIM7602IkwfDo+4n2f7cJLgadjkf87gQ+oTMP7/oqcvbPGgyf70XRKfw4hHfrcYVkzH6UE75dR6zXVxD3IiCqsU/sVCATk5ccrCt9BaO2IW4IqtLzkxeq4EE4VUuCEsC/cFksPuNoCGVHzkrchOF8WhbZS6Nb6UG6cxdDz+1d/Tg5sCMAzvhKoRKWHxujE0NlSRE2IPps+m/I+IXqYp4YVaOs6MCFfIvhWCsylzeP1g4wv71y3eR/GE05NyKgkTJ5Nf//t+YsXR7/88uvxNIeI1sHkBnw4yjGaXDhXPEgyD6JRpvlugh8VeoG2jpJ+7MJc3V3bnOZjwGl7ebtRoSR8HCH6PTpk439ebQf0zYfLB1nffLhkJzG0FtU/6QGxFkQNyKKnhuhJ/pGisCF8zs5uRec0lOzxwbly67cJ9hpM/zhNnTJLmypVQVNprwgDRtRwcjGjAQD0QyHH+TSf8ozfTrRt0pkBwUJ1TbGFLtlyZxpqTsjrBm009anVFnnJH71K/3jGhQ6XcBseJOzvt/8pcdHJxYy1wrMKwLBxtqBmwTIEp4WEhENtw1sILEWkE38fZYmHlGfGBraGwJRh0UPNqnU6IbWisRPO+fzTwizMo0dsHrtO4Hphdpkd2pWq4Y4SB/rrhBmGSgr02UCBA5ko2P7WsVoYH6tdS3sGVll7rUzjhyqjB2QihhZMUDJdWc5mgQmt7Y1PZr8wwTIHSJTGdpTqiUxaJgYnhtAoHwZTiqxto0zGtPJhV+7CDG5AJMYM3Ixd1KDhzmv/zEFzeiidRUemhakhCKU94XqQ3sSuAsyYB4Gy3cYlt05cD1uiszXojEkEQakWZofPfdxGj68BGF092MZP412eovV+8g5Vowx7D95GlMDmrUhdPzl9937+dO+ifXTEcf4/nUszIJNjbTuhDKOVAT74fMx+cnCZC3Oi9d4IjQvm3pWPz/pcoFiBZnNhZIhdzmZG6lgPcz6wBkvPmCaavn2NIsqF+fz588J8gwbWNuJk4ILRkWdcWhOETDQ/7sjLFti5atpgqLDXaCNx2SF73tzc5KEFvXWjZ+9GGu0zrpUE42Ev6J+zywdBrAMzIJ1bbIrxkC/It894AOz8u+V82AJ75xoV2ljl0nbFktgGUJh6V8oeNVVuIi1CUWlbFXRjxfns9Ozt/CyxGooAE606FR5ubxoOsrNkJxySQFpalAMNgfERgY0rivkgKqVVWKcJXwpFdC0ayBO9j9tzwykkL19MM+5oX/BOmRiAqHPNSz67YKKuEbxnFoenPXvJblow+zMDdUKYnvhdyOc/jEh1QSeUZtJ2lTJpNtIq58760Amzd1uvIewpvEHJVOuvar37+2uTJgpM+DmyclywAW5D4bRQhsBIY7UZZdJHvjqmheMUTfcolUhEDWLpKuMkh8hvs6mEh79Q9z19HoQgSahaedJANS+XQnv4TodP3o+q8Sn7VnHXsL6na1dCR/JLquQn5DoUuXfZru7kXRKaezrz9Rk9T5qyPRVzT7UkaEaTMPuhN5vB4zJxC2nMoZDENby/6vv+XxlfYGM=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get car club prices by package and category"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/car-club/prices"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint retrieves the prices for a specific car club package and category.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"packageId","in":"query","required":true,"schema":{"type":"integer","format":"int32"}},{"name":"carCategory","in":"query","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Prices retrieved successfully"}}}
>
  
</StatusCodes>


      