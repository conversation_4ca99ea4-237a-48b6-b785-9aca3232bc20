<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ __('Webhook Processing Flow') }}
                </h2>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Complete processing timeline for webhook {{ substr($webhookGroupId, 0, 8) }}...
                </p>
            </div>
            <div>
                <a href="{{ route('admin.webhook-monitoring.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-800 dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest hover:bg-gray-700 dark:hover:bg-white focus:bg-gray-700 dark:focus:bg-white active:bg-gray-900 dark:active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Back to Monitoring
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Webhook Information Card -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100 mb-4">Webhook Information</h3>
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Webhook ID</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono">{{ $webhookGroupId }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Webhook Name</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $webhookInfo['webhook_name'] }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $webhookInfo['location_display'] }}</dd>
                        </div>
                        @if($webhookInfo['customer_email'])
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Customer Email</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $webhookInfo['customer_email'] }}</dd>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Processing Timeline -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100 mb-6">Processing Timeline</h3>
                    
                    <div class="flow-root">
                        <ul role="list" class="-mb-8">
                            @foreach($activities as $index => $activity)
                                <li>
                                    <div class="relative pb-8">
                                        @if(!$loop->last)
                                            <span class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700" aria-hidden="true"></span>
                                        @endif
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800 {{ $activity->status_badge_class }}">
                                                    <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="{{ $activity->activity_icon }}" />
                                                    </svg>
                                                </span>
                                            </div>
                                            <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                <div>
                                                    <p class="text-sm text-gray-900 dark:text-gray-100 font-medium">{{ $activity->status }}</p>
                                                    @if($activity->customer_email)
                                                        <p class="text-sm text-gray-500 dark:text-gray-400">Email: {{ $activity->customer_email }}</p>
                                                    @endif
                                                    @if($activity->customer_type)
                                                        <p class="text-sm text-gray-500 dark:text-gray-400">Customer Type: {{ ucfirst($activity->customer_type) }}</p>
                                                    @endif
                                                    @if($activity->booking_type)
                                                        <p class="text-sm text-gray-500 dark:text-gray-400">Booking Type: {{ ucfirst(str_replace('_', ' ', $activity->booking_type)) }}</p>
                                                    @endif
                                                    @if($activity->signature_validation)
                                                        <p class="text-sm text-gray-500 dark:text-gray-400">Signature: {{ ucfirst($activity->signature_validation) }}</p>
                                                    @endif
                                                    @if($activity->processing_time)
                                                        <p class="text-sm text-gray-500 dark:text-gray-400">Processing Time: {{ number_format($activity->processing_time, 2) }}s</p>
                                                    @endif
                                                </div>
                                                <div class="whitespace-nowrap text-right text-sm text-gray-500 dark:text-gray-400">
                                                    <time datetime="{{ $activity->created_at->toISOString() }}">{{ $activity->formatted_date }}</time>
                                                    <p class="text-xs">{{ $activity->time_ago }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Raw Data (Collapsible) -->
            <div class="mt-8 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <details class="group">
                        <summary class="flex cursor-pointer items-center justify-between text-lg font-medium leading-6 text-gray-900 dark:text-gray-100 marker:content-none">
                            <span>Raw Activity Data</span>
                            <svg class="h-5 w-5 text-gray-400 transition-transform group-open:rotate-180" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                            </svg>
                        </summary>
                        <div class="mt-4 space-y-4">
                            @foreach($activities as $activity)
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">{{ $activity->status }} - {{ $activity->formatted_date }}</h4>
                                    <pre class="text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 p-3 rounded overflow-x-auto">{{ json_encode($activity->properties, JSON_PRETTY_PRINT) }}</pre>
                                </div>
                            @endforeach
                        </div>
                    </details>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
