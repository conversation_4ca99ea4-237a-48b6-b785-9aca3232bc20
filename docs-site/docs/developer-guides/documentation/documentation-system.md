---
sidebar_position: 2
---

# Documentation System

This guide explains how the integrated Docusaurus documentation system works within the Fresh Car Core Laravel application.

## Overview

The documentation system combines Docusaurus (a modern static site generator) with <PERSON><PERSON>'s authentication system to provide secure, searchable, and well-organized documentation for the Fresh Car Core project.

## Architecture

### Integration Components

1. **Docusaurus Site** (`docs-site/`)

   - Static site generator for documentation
   - Markdown-based content
   - Built-in search and navigation
   - Responsive design

2. **Laravel Controller** (`app/Http/Controllers/Admin/DocsController.php`)

   - Serves static Docusaurus files
   - Handles authentication and authorization
   - Provides security through Laravel middleware

3. **Admin Routes** (`routes/web.php`)
   - Protected by same authentication as admin panel
   - Requires `view dashboard` permission or `super_admin` role

### Authentication Flow

```mermaid
graph TD
    A[User visits /admin/docs] --> B{Authenticated?}
    B -->|No| C[Redirect to login]
    B -->|Yes| D{Has permission?}
    D -->|No| E[403 Forbidden]
    D -->|Yes| F[DocsController]
    F --> G[Serve Docusaurus files]
    G --> H[Documentation displayed]
```

## Development Workflow

### Adding New Documentation

1. **Create Markdown Files**

   ```bash
   # Navigate to docs directory
   cd docs-site/docs

   # Create new documentation file
   touch new-feature.md
   ```

2. **Edit Content**

   ```markdown
   ---
   sidebar_position: 2
   ---

   # New Feature Documentation

   Your content here...
   ```

3. **Update Sidebar** (if needed)

   ```typescript
   // docs-site/sidebars.ts
   const sidebars: SidebarsConfig = {
     tutorialSidebar: [
       "intro",
       "new-feature", // Add your new page
       // ...
     ],
   };
   ```

4. **Build and Deploy**

   ```bash
   # Option 1: Use npm script
   npm run docs:build

   # Option 2: Use build script
   ./scripts/build-docs.sh

   # Option 3: Manual build
   cd docs-site && npm run build && cd .. && cp -r docs-site/build/* public/docs/
   ```

### Local Development

```bash
# Start Docusaurus development server
npm run docs:dev

# This will open http://localhost:3000 with live reload
# Note: This bypasses Laravel authentication for development
```

### Testing Documentation

```bash
# Run documentation-specific tests
vendor/bin/pest tests/Feature/Admin/DocsControllerTest.php

# Test authentication and file serving
vendor/bin/pest --filter=DocsController
```

## Content Organization

### Directory Structure

```
docs-site/
├── docs/
│   ├── intro.md                    # Getting started
│   ├── setup/
│   │   └── google-cloud.md         # Setup guides
│   ├── deployment/
│   │   ├── overview.md             # Deployment procedures
│   │   └── rollback.md             # Rollback procedures
│   ├── api/
│   │   └── car-management.md       # API documentation
│   └── guides/
│       └── documentation-system.md # This guide
├── static/                         # Static assets
├── docusaurus.config.ts           # Docusaurus configuration
└── sidebars.ts                    # Sidebar navigation
```

### Content Guidelines

1. **Use Descriptive Titles**

   - Clear, concise headings
   - Consistent formatting

2. **Include Code Examples**

   ```bash
   # Always provide working examples
   php artisan migrate
   ```

3. **Add Navigation Metadata**

   ```markdown
   ---
   sidebar_position: 1
   title: Custom Title
   ---
   ```

4. **Link Between Pages**
   ```markdown
   See [Google Cloud Setup](/docs/setup/google-cloud) for details.
   ```

## Security Features

### Authentication Requirements

- All documentation routes require authentication
- Users must have `view dashboard` permission OR `super_admin` role
- Same security model as admin panel

### Path Security

- Directory traversal protection
- Sanitized file paths
- Restricted to documentation directory

### Content Security

- Static files only (no PHP execution)
- Proper MIME type handling
- XSS protection through Laravel

## Deployment Integration

### Build Process

The documentation is built as part of the application deployment:

```bash
# During deployment
npm run build:all  # Builds both Laravel assets and documentation
```

### Cloud Build Integration

Add to `cloudbuild.yaml`:

```yaml
steps:
  # ... existing steps ...

  - name: "node:18"
    entrypoint: "npm"
    args: ["run", "docs:build"]

  # ... rest of build steps ...
```

### Environment Considerations

- **Local**: Use `npm run docs:dev` for development
- **Staging**: Built documentation served through Laravel
- **Production**: Built documentation served through Laravel

## Troubleshooting

### Common Issues

1. **Documentation not loading**

   ```bash
   # Check if files exist
   ls -la public/docs/

   # Rebuild documentation
   ./scripts/build-docs.sh
   ```

2. **Permission denied**

   ```bash
   # Check user permissions
   php artisan permission:show

   # Grant dashboard permission
   php artisan permission:give <EMAIL> "view dashboard"
   ```

3. **Build failures**

   ```bash
   # Check Node.js version
   node --version  # Should be 18+

   # Clear and reinstall
   cd docs-site && rm -rf node_modules && npm install
   ```

### Debug Mode

Enable debug logging in `DocsController`:

```php
// Add to DocsController for debugging
Log::info('Docs request', [
    'path' => $path,
    'user' => auth()->user()->email,
    'file_exists' => File::exists($fullPath)
]);
```

## Best Practices

1. **Keep Documentation Updated**

   - Update docs with code changes
   - Review documentation in pull requests

2. **Use Consistent Formatting**

   - Follow markdown standards
   - Use code blocks for commands

3. **Test Documentation Changes**

   - Build locally before committing
   - Verify links work correctly

4. **Organize Content Logically**
   - Group related topics
   - Use clear navigation structure

## Future Enhancements

Potential improvements to consider:

- **Search Integration**: Enhanced search with Algolia
- **Version Control**: Documentation versioning
- **API Integration**: Auto-generated API docs
- **Metrics**: Documentation usage analytics
