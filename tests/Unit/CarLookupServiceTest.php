<?php

namespace Tests\Unit;

use App\Services\CarLookupService;
use App\Services\DvlaMotHistoryService;
use Illuminate\Support\Facades\Http;
use Mockery;
use Tests\TestCase;

class CarLookupServiceTest extends TestCase
{
    private CarLookupService $carLookupService;
    private DvlaMotHistoryService $motHistoryService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the DvlaMotHistoryService
        $this->motHistoryService = Mockery::mock(DvlaMotHistoryService::class);

        // Create the service with the mocked dependency
        $this->carLookupService = new CarLookupService($this->motHistoryService);
    }

    /**
     * Test successful car lookup by registration number.
     */
    public function test_lookup_by_registration_success(): void
    {
        // Mock the DVLA API response
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'registrationNumber' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'colour' => 'Blue',
                'yearOfManufacture' => 2020,
                'fuelType' => 'Petrol',
                'engineCapacity' => 2000,
                'transmission' => 'Manual',
                'motExpiryDate' => '2024-12-31',
                'taxDueDate' => '2024-06-30',
                'co2Emissions' => 150,
            ], 200),
        ]);

        // Mock the MOT History service
        $this->motHistoryService->shouldReceive('getMotHistory')
            ->with('AB12CDE')
            ->andReturn([
                'registration' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'firstUsedDate' => '2020-01-01',
                'fuelType' => 'Petrol',
                'primaryColour' => 'Blue',
                'motTests' => [],
            ]);

        $result = $this->carLookupService->lookupByRegistration('AB12CDE');

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertEquals('AB12CDE', $result['registrationNumber']);
        $this->assertEquals('BMW 3 Series', $result['makeAndModel']);
        $this->assertEquals('BMW', $result['make']);
        $this->assertEquals('3 Series', $result['model']);
    }

    /**
     * Test car lookup by registration number not found.
     */
    public function test_lookup_by_registration_not_found(): void
    {
        // Mock the DVLA API to return 404
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'error' => 'Vehicle not found',
            ], 404),
        ]);

        $result = $this->carLookupService->lookupByRegistration('NOTFOUND');

        $this->assertNull($result);
    }

    /**
     * Test registration number normalization.
     */
    public function test_lookup_by_registration_normalization(): void
    {
        // Mock the DVLA API response (all normalized inputs should result in the same API call)
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'registrationNumber' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'colour' => 'Blue',
                'yearOfManufacture' => 2020,
                'fuelType' => 'Petrol',
                'engineCapacity' => 2000,
                'transmission' => 'Manual',
                'motExpiryDate' => '2024-12-31',
                'taxDueDate' => '2024-06-30',
                'co2Emissions' => 150,
            ], 200),
        ]);

        // Mock the MOT History service for all calls
        $this->motHistoryService->shouldReceive('getMotHistory')
            ->with('AB12CDE')
            ->times(3)
            ->andReturn([
                'registration' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'firstUsedDate' => '2020-01-01',
                'fuelType' => 'Petrol',
                'primaryColour' => 'Blue',
                'motTests' => [],
            ]);

        // Test with spaces
        $result1 = $this->carLookupService->lookupByRegistration('AB12 CDE');
        $this->assertNotNull($result1);

        // Test with lowercase
        $result2 = $this->carLookupService->lookupByRegistration('ab12cde');
        $this->assertNotNull($result2);

        // Test with mixed case and spaces
        $result3 = $this->carLookupService->lookupByRegistration('Ab12 CdE');
        $this->assertNotNull($result3);

        // All should return the same car
        $this->assertEquals($result1['registrationNumber'], $result2['registrationNumber']);
        $this->assertEquals($result2['registrationNumber'], $result3['registrationNumber']);
    }

    /**
     * Test successful car search by make and model.
     */
    public function test_search_by_make_model_success(): void
    {
        $results = $this->carLookupService->searchByMakeModel('BMW');

        $this->assertIsArray($results);
        $this->assertNotEmpty($results);

        foreach ($results as $car) {
            $this->assertArrayHasKey('makeAndModel', $car);
            $this->assertArrayHasKey('make', $car);
            $this->assertArrayHasKey('model', $car);
            $this->assertStringContainsStringIgnoringCase('BMW', $car['make']);
        }
    }

    /**
     * Test car search with no results.
     */
    public function test_search_by_make_model_no_results(): void
    {
        $results = $this->carLookupService->searchByMakeModel('NONEXISTENTBRAND');

        $this->assertIsArray($results);
        $this->assertEmpty($results);
    }

    /**
     * Test car search with short query.
     */
    public function test_search_by_make_model_short_query(): void
    {
        $results = $this->carLookupService->searchByMakeModel('A');

        $this->assertIsArray($results);
        $this->assertEmpty($results);
    }

    /**
     * Test case insensitive search.
     */
    public function test_search_by_make_model_case_insensitive(): void
    {
        $resultsUpper = $this->carLookupService->searchByMakeModel('BMW');
        $resultsLower = $this->carLookupService->searchByMakeModel('bmw');
        $resultsMixed = $this->carLookupService->searchByMakeModel('BmW');

        $this->assertEquals(count($resultsUpper), count($resultsLower));
        $this->assertEquals(count($resultsLower), count($resultsMixed));
    }

    /**
     * Test search by model name.
     */
    public function test_search_by_model_name(): void
    {
        $results = $this->carLookupService->searchByMakeModel('Focus');

        $this->assertIsArray($results);
        $this->assertNotEmpty($results);

        foreach ($results as $car) {
            $this->assertStringContainsStringIgnoringCase('Focus', $car['model']);
        }
    }

    /**
     * Test search by partial make and model.
     */
    public function test_search_by_partial_make_model(): void
    {
        $results = $this->carLookupService->searchByMakeModel('3 Series');

        $this->assertIsArray($results);
        $this->assertNotEmpty($results);

        foreach ($results as $car) {
            $this->assertStringContainsStringIgnoringCase('3 Series', $car['model']);
        }
    }

    /**
     * Test that search results contain expected structure.
     */
    public function test_search_results_structure(): void
    {
        $results = $this->carLookupService->searchByMakeModel('BMW');

        $this->assertNotEmpty($results);

        foreach ($results as $car) {
            $this->assertArrayHasKey('makeAndModel', $car);
            $this->assertArrayHasKey('make', $car);
            $this->assertArrayHasKey('model', $car);
            $this->assertArrayHasKey('category', $car);
            $this->assertArrayHasKey('variants', $car);
            $this->assertIsArray($car['variants']);
        }
    }

    /**
     * Test lookup results structure.
     */
    public function test_lookup_results_structure(): void
    {
        // Mock the DVLA API response
        Http::fake([
            config('services.dvla.api_url') => Http::response([
                'registrationNumber' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'colour' => 'Blue',
                'yearOfManufacture' => 2020,
                'fuelType' => 'Petrol',
                'engineCapacity' => 2000,
                'transmission' => 'Manual',
                'motExpiryDate' => '2024-12-31',
                'taxDueDate' => '2024-06-30',
                'co2Emissions' => 150,
            ], 200),
        ]);

        // Mock the MOT History service
        $this->motHistoryService->shouldReceive('getMotHistory')
            ->with('AB12CDE')
            ->andReturn([
                'registration' => 'AB12CDE',
                'make' => 'BMW',
                'model' => '3 Series',
                'firstUsedDate' => '2020-01-01',
                'fuelType' => 'Petrol',
                'primaryColour' => 'Blue',
                'motTests' => [],
            ]);

        $result = $this->carLookupService->lookupByRegistration('AB12CDE');

        $this->assertNotNull($result);
        $this->assertArrayHasKey('registrationNumber', $result);
        $this->assertArrayHasKey('makeAndModel', $result);
        $this->assertArrayHasKey('make', $result);
        $this->assertArrayHasKey('model', $result);
        $this->assertArrayHasKey('colour', $result);
        $this->assertArrayHasKey('year', $result);
        $this->assertArrayHasKey('fuelType', $result);
        $this->assertArrayHasKey('engineSize', $result);
        $this->assertArrayHasKey('transmission', $result);
    }
}
