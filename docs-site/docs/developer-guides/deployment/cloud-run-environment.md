# Deployment and Environment

This document explains the deployment workflows and environment configuration for the Fresh Car Core Laravel application running on Google Cloud Run.

## Deployment Architecture

The project uses a **trunk-based development** approach with separate staging and production environments:

### Staging Environment

- **Trigger**: Automatic deployment on every push to main branch
- **Build File**: `cloudbuild.staging.yaml`
- **Image Tags**: `staging-latest` and `$COMMIT_SHA`
- **Service**: `api-service` (staging)
- **Jobs**: Cloud Run Jobs with `-staging` suffix
- **Secrets**: `staging-*` prefixed secrets

### Production Environment

- **Trigger**: Manual deployment using promote/retag strategy
- **Build File**: `cloudbuild.yaml`
- **Image Source**: Promotes latest staging image to production
- **Image Tags**: `production-latest`
- **Service**: `api-service` (production)
- **Jobs**: Cloud Run Jobs with `-production` suffix
- **Secrets**: `production-*` prefixed secrets

### Deployment Workflow

1. **Development**: Push code to main branch
2. **Staging**: Automatic build and deployment via `cloudbuild.staging.yaml`
3. **Testing**: Validate changes in staging environment
4. **Production**: Manual promotion via `cloudbuild.yaml` (retags staging image)
5. **Jobs Update**: Cloud Run Jobs automatically pick up new images on next execution

## Environment Configuration

There are three main approaches to handle environment variables:

1. **Cloud Run Environment Variables** - Set directly in the Cloud Run service
2. **Google Secret Manager** - For sensitive data like passwords and API keys
3. **Cloud Build Substitution Variables** - For build-time configuration

## 1. Cloud Run Environment Variables

### Direct Environment Variables

These are set directly in the `gcloud run deploy` command in your `cloudbuild.yaml`:

```yaml
- "--set-env-vars=APP_ENV=production,APP_DEBUG=false,LOG_CHANNEL=stderr"
```

### Using Substitution Variables

For dynamic values, use Cloud Build substitution variables:

```yaml
- "--set-env-vars=DB_HOST=$$DB_HOST"
```

The `$$` escapes the variable so it's not substituted during the YAML parsing.

## 2. Google Secret Manager (Recommended for Sensitive Data)

### Setting up Secrets

Use the provided script to set up secrets:

```bash
chmod +x scripts/setup-cloud-env.sh
./scripts/setup-cloud-env.sh your-project-id europe-west1
```

### Current Secrets Configuration

The project has the following secrets already configured:

**Production Secrets:**

- `production-app-key` - Laravel application key
- `production-db-password` - Database password
- `production-stripe-secret` - Stripe API secret
- `production-dvla-*` - DVLA API credentials

**Staging Secrets:**

- `staging-app-key` - Staging Laravel application key
- `staging-db-password` - Staging database password
- `staging-stripe-secret` - Staging Stripe API secret
- `staging-dvla-*` - Staging DVLA API credentials

### Manual Secret Creation (if needed)

```bash
# Create a secret
echo -n "your-secret-value" | gcloud secrets create secret-name --data-file=-

# Update a secret
echo -n "new-secret-value" | gcloud secrets versions add secret-name --data-file=-
```

### Using Secrets in Cloud Run

In your `cloudbuild.yaml`:

```yaml
- "--set-secrets=STRIPE_SECRET=stripe-secret:latest"

availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/stripe-secret/versions/latest
      env: 'STRIPE_SECRET'
```

## 3. Cloud Build Substitution Variables

### Setting Substitution Variables

#### Option A: Cloud Build Trigger (Recommended)

1. Go to Cloud Build > Triggers in Google Cloud Console
2. Edit your trigger
3. Add substitution variables:
   - `_DB_HOST`: `your-database-host`
   - `_DB_USERNAME`: `your-database-username`
   - `_DB_DATABASE`: `your-database-name`

#### Option B: Command Line

```bash
gcloud builds submit --substitutions=_DB_HOST=your-host,_DB_USERNAME=your-user
```

## Required Environment Variables for Laravel

Based on your `.env.example`, here are the essential variables:

### Core Application

- `APP_NAME`: Application name
- `APP_ENV`: Environment (production)
- `APP_KEY`: Encryption key (use Secret Manager)
- `APP_DEBUG`: Debug mode (false for production)
- `APP_URL`: Application URL

### Database

- `DB_CONNECTION`: Database type (pgsql)
- `DB_HOST`: Database host
- `DB_PORT`: Database port (5432)
- `DB_DATABASE`: Database name
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password (use Secret Manager)

### Logging

- `LOG_CHANNEL`: stderr (for Cloud Run)
- `LOG_LEVEL`: info or error for production

### Payment (Stripe)

- `STRIPE_KEY`: Public key
- `STRIPE_SECRET`: Secret key (use Secret Manager)
- `STRIPE_WEBHOOK_SECRET`: Webhook secret (use Secret Manager)

## Security Best Practices

1. **Never put sensitive data in environment variables directly**
2. **Use Secret Manager for**:

   - Database passwords
   - API keys
   - Encryption keys
   - Webhook secrets

3. **Use environment variables for**:
   - Non-sensitive configuration
   - Public URLs
   - Feature flags

## Deployment Process

1. **Set up secrets** (one-time):

   ```bash
   ./scripts/setup-cloud-env.sh your-project-id
   ```

2. **Configure Cloud Build trigger** with substitution variables

3. **Deploy**:

   ```bash
   gcloud builds submit
   ```

## Troubleshooting

### Check Environment Variables in Cloud Run

```bash
gcloud run services describe api-service --region=europe-west1 --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)"
```

### Check Secrets

```bash
gcloud secrets list
gcloud secrets versions list secret-name
```

### Laravel Configuration Cache

Remember to clear configuration cache when environment variables change:

```bash
php artisan config:clear
php artisan config:cache
```

This is handled automatically in your `cloud-run-web-entrypoint` script with `php artisan optimize:clear`.

## Quick Start Guide

### 1. Set up Google Cloud Project

```bash
# Set your project ID
export PROJECT_ID="your-project-id"
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable secretmanager.googleapis.com
```

### 2. Create Secrets

```bash
# Run the setup script
./scripts/setup-cloud-env.sh $PROJECT_ID europe-west1
```

### 3. Configure Cloud Build Trigger

1. Go to Cloud Build > Triggers
2. Create or edit your trigger
3. Add substitution variables:
   - `_DB_CONNECTION`: `pgsql`
   - `_DB_HOST`: `your-database-host`
   - `_DB_PORT`: `5432`
   - `_DB_DATABASE`: `your-database-name`
   - `_DB_USERNAME`: `your-database-username`

### 4. Deploy

```bash
gcloud builds submit
```

### 5. Test Deployment

After deployment, test your environment variables:

```bash
# Health check
curl https://your-service-url/health

# Environment check (only works in non-production or with debug header)
curl https://your-service-url/env-check
```

## Example Complete Setup

See the updated `cloudbuild.yaml` for a complete example of how environment variables and secrets are configured for deployment.

## Files Modified

- `cloudbuild.yaml` - Updated with environment variables and secrets configuration
- `cloud-run-web-entrypoint` - Enhanced with proper caching for production
- `app/Providers/RouteServiceProvider.php` - Added environment testing routes
- `scripts/setup-cloud-env.sh` - Script to automate secret creation
- `routes/env-test.php` - Routes for testing environment configuration
