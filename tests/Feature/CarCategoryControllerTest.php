<?php

namespace Tests\Feature;

use App\Models\CarCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CarCategoryControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_list_active_car_categories(): void
    {
        CarCategory::factory()->create([
            'name' => 'Small',
            'description' => 'Compact cars',
            'price_multiplier' => 1.00,
            'is_active' => true,
        ]);

        CarCategory::factory()->create([
            'name' => 'Large',
            'description' => 'Full-size cars',
            'price_multiplier' => 1.50,
            'is_active' => true,
        ]);

        CarCategory::factory()->create([
            'name' => 'Inactive',
            'description' => 'Inactive category',
            'price_multiplier' => 1.00,
            'is_active' => false,
        ]);

        $response = $this->getJson('/api/v3/car-categories');

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'priceMultiplier',
                    ]
                ]
            ])
            ->assertJsonFragment(['name' => 'Small'])
            ->assertJsonFragment(['name' => 'Large'])
            ->assertJsonMissing(['name' => 'Inactive']);
    }

    public function test_returns_empty_collection_when_no_active_categories(): void
    {
        CarCategory::factory()->create([
            'name' => 'Inactive',
            'is_active' => false,
        ]);

        $response = $this->getJson('/api/v3/car-categories');

        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }
}
