@php
    $svgContent = file_get_contents(public_path('svg/freshcar-logo.svg'));
    // Add attributes to the SVG tag
    $svgContent = str_replace('<svg', '<svg ' . $attributes->merge(['class' => 'w-full h-auto']) . ' viewBox="0 0 750 287"', $svgContent);
@endphp
<div class="freshcar-logo">
    {!! $svgContent !!}
</div>

<style>
/* Fresh Car Logo Dark Mode Styles */
.dark .freshcar-logo svg path[fill="#FEFEFE"],
.dark .freshcar-logo svg path[fill="#FDFDFD"],
.dark .freshcar-logo svg path[fill="#FCFCFC"],
.dark .freshcar-logo svg path[fill="#FBFBFB"],
.dark .freshcar-logo svg path[fill="#FAFAFA"] {
    fill: #f3f4f6; /* Light gray for dark mode */
}

.dark .freshcar-logo svg path[fill="#48704D"] {
    fill: #10b981; /* Brighter green for dark mode */
}
</style>
