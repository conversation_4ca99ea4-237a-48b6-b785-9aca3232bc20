---
id: update-cars-byid
title: "updateCarsByid"
description: "updateCarsByid"
sidebar_label: "updateCarsByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVktzGzcM/iscnPqgLdedXvaW2MnUnU7ridX24NEB2oW0jLkkQ2KlqDv73zMgV5IVOW2OuUhLPD4AJPiBAzCuE1SPcIMRFhoCRuyIKYpwAIcdQQU1xrsGNBgHFQTkFjRE+tCbSA1UHHvSkOqWOoRqAN4FcTKOaU0RNKx87JCL6OdrGMdFcafEr32zE5/aOybH8okhWFMjG+9m75N3IjsD98v3VDNoCNEHimwoidakW7LEktXBdOm9JXQwjhqwaYwAo71/5rdCm0gDG7bi8OBXXGDeODa8u53/mZ2ZPvK3ldHzrfrhx28oNXGPlIJ3qcS6vrqSv4ZSHU0QVPHt65pSysES1X00vMtttySMFKF6XEirSOxc410DFfShQaYbjOn1zkhTnmKeqTvi1jelb2tp3Ny/FcwwmFmNMc2G3N4jSBJxs2/9PlqooGUOqZrNrK/Rtj5xNQQfeTyL+7sYqAIAGjYYDS5tKV48SvUr7K1chF+urn6SbdKfx1lFSu1FQ5sLDOYS/+0jbWmZDFO6dMRnYW9pQ9aHjhzvg38JtEf+StAHxrVx6/8DtGZDX4l4H33T17I4gC6OZ/4gDVu2an/yh4aUkDCxS27QYqCnj7d7avntn3nuI7kE747c8uYjdkEa9aTdhbFGobOVz6GmXn4rZakbjOrV/Z0cIsVU8t9cn5U0b00SOxWi35iGkiLXBG8cJ7XyUXFLqvH8B7HKuGL69/WluuMveXToyr5LV2r5VTUyrX00tF/bfqlSvzwk8ly+9P7JuHXSCl2j+kRRYc8tOZ44IgdHa/02ZXVS7FWgKPysDrcsqdTXrcJioyKtTeKiysDWr43TyprEx2SLFUWRONpO0kY2/Gj0zOWkMlsSV33IqoYYjU1quTsN7vpuSVGrRBjrdg8rZh0+Uc6t8w1ZrepIeIz00p6dGRw3LzOI6CaRSozcp3IEriZr/ws5Z7E/yBfOQBlX274RbcCUtj42KlIiTtmVOjRWbSia1f7QYNQQfOIOM7tPI7nwnDRrUi8R4eEC5akVLBonQPkeDxMHPgIGA1rGewINVRnzCw1CdKIehiUm+ivacRTxh56iEPTiyG+ZKhuT5Ls5DIfPMjkMd/ju3fRq+F6BfjnDSYhul2nU9rICDU+0O7xExsWooSVs8pAYJt1NCXMxF4Sj79l7QqjnMBXuX81vfhU6mR4i0kFQQcStvHFwW4L7crxikGUDWHTrHtdiW0DzxOxlW4/D9JSrcqovljcMxWLun8iN46FalrVUO46fAGGuZig=
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateCarsByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/cars/{carId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateCarsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"carId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      