<?php

namespace Database\Seeders;

use DB;
use Illuminate\Database\Seeder;

class FeatureSeeder extends Seeder
{
    public function run(): void
    {
        $features = [
            'team',
            'stripe',
            'profileManagement',
            'cookiePolicy',
            'notifications'
        ];

        foreach ($features as $feature) {
            if (config('database.default') === 'sqlite') {
                // SQLite syntax
                DB::statement(
                    "INSERT OR IGNORE INTO features (name, scope, value, created_at, updated_at)
                    VALUES ('{$feature}Feature', '__global', 'true', datetime('now'), datetime('now'))"
                );
            } else {
                // PostgreSQL syntax
                DB::statement(
                    "INSERT INTO features (name, scope, value, created_at, updated_at)
                    VALUES ('{$feature}Feature', '__global', 'true', NOW(), NOW())
                    ON CONFLICT DO NOTHING"
                );
            }
        }
    }
}
