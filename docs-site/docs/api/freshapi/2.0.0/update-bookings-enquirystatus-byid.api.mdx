---
id: update-bookings-enquirystatus-byid
title: "updateBookingsEnquirystatusByid"
description: "updateBookingsEnquirystatusByid"
sidebar_label: "updateBookingsEnquirystatusByid"
hide_title: true
hide_table_of_contents: true
api: eJztV0tz2zYQ/iuYPfVBW7JaT1reYjuZupNJPbbSztSjw4pciYhBgAGWclQO/3tnAerlR+JjDrlIJLD77Qv7LdgB4zJAfgtnzt1pu4RZBg16rInJy0YHFmuCHOZJ4LKEDLSFHBrkCjLw9KnVnkrI2beUQSgqqhHyDnjdiKK2TEvykMHC+Ro5Lf0ygb6fJXUKfObKtegUzjJZlkdsGqMLZO3s6GNwVtZ24Duzt0BWntc3jNwGiWCw7OYfqWDIoPGuIc+agqgeiucd1Nq+I7vkCvKTrXJgL/noM/FpoX1N5QUy7QU2SOzFVSLTEeuaIAPbGoNzQykvAoO2IGNiQNeEwdnL8iVpehHUe8cpuIe+PdDtM8Cy1KKI5movLQs0gTJgzSILHxqJZTgUb/YTdjH9CwSG6TN/r8u3V5f9tvnp5+8F+sYKJECeQuNsSHYn47H8lRQKrxvBhxxu2qKgEGJBAxWt17yOZDwn9OQhv50JeYoXMRZJCLT7psNgO0TbZ2stvH1o5OvyNXHlysT1hZB95PwcRtjo0TAQwoj2NUfddlD0IM771WaQtN5ADhVzE/LRyLgCTeUC513jPPeP3HsnAioBQAYr9FrqEZMmGilrC2yNlPx0PD6R9GYP7Sw8heqopNURNvoY/2s93dM8aKZwbIkfmb2gFRnX1GR5Y/w50Bb5haA3jEttl18DNHpFL0S88q5sC3nZgs52Z+VG2j2lanNitidcTMIwp+NgTwLZ8PB200R//jON508o5Ho3pd98xrox9AQf7HrnQePDZDz59Wj86mjy+/TkND89ySe/HY9fnfwLzzX3+Autukcw2i5cjGxovreSRXWOXr2+upQzQz6kdK0mjzI4rXQQOdV4t9IlBUW2bJy2HNTCecUVqdLxe2IVcUX078mxuuTnNGq0qcwF+pDJryqQaem8ps27aecqtPOtI/vrm47KFNpStYG8wpYrsjwQejSOxrj7ELeDYqca8kJ8aksGQYW2qBQmGeVpqQOnrQhs3FLbTBkdeOdskiLJrLJ0P6yWZGhPaE/lIDKTHFdtE7dKYtQmqPn60Lht6zn5TAVCX1QbWBGr8Y6ib7UryWSq8IQ7S0/l7JHALnmR12RvWFKJmVIJ4qH6EnL0YlPIJ2qgtC1MW8pugyHcO18qT4E4RFWqURu1Iq8Xm6LJSW1c4BrjKB7u04l91YZ+1QH/qqcIe9vB8erVGNRWoCORdAM13wI2Wnp5gIUMDugZMsh3N/lZBsLAotZ1cwz0wZu+l+VPLXmZOLMd8UYOL3WQ53I79x54uL2/ww/Xw0XjRwXZ054Pi2jXkd9NK2+QwR2tDz44+lmfQUVYxsnXDfvnydTRVFB2+o8+G4QXt4Ps6vX0/I+Yn/S9IecNcvB4L58yeJ8ccOkwxOuSrHVg0C5bXIpsAo0XglZSvi3LAyKNrj4ZYtcliam7I9v324hZ3iXavv8fQG/QRQ==
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateBookingsEnquirystatusByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/bookings/enquirystatus/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateBookingsEnquirystatusByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"text/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"application/*+json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      