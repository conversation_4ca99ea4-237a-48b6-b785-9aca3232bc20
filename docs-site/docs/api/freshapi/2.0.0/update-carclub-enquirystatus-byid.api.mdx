---
id: update-carclub-enquirystatus-byid
title: "updateCarclubEnquirystatusByid"
description: "updateCarclubEnquirystatusByid"
sidebar_label: "updateCarclubEnquirystatusByid"
hide_title: true
hide_table_of_contents: true
api: eJztV0tz2zYQ/iuYPfVBW7JaT1reEieZupNJPbHSztSjw4pcSYhBgAEWclQO/3tnAeplO4mPOeQikcDuty/st2AHjMsA5Q1coL8wcQ6zAlr02BCTl40OLDYEJcydu9V2eVlDAdpCCS3yCgrw9DFqTzWU7CMVEKoVNQhlB7xpRVFbpiV5KGDhfIOcl36ZQN/PsjoFfuHqjehUzjJZlkdsW6MrZO3s6ENwVtb24HuzN0BWnjfXjByDRDBYdvMPVDEU0HrXkmdNQVSPxcsOGm3fkF3yCsqznXJgr+0S+kJ8WmjfUP0SmQ4CGyQO4qqR6YR1Q1CAjcbg3FDOi8CgrciYFNA7wuDsZf2UND0J6q3jHNx93+7p9gVgXWtRRHN1kJYFmkAFsGaRhfetxPIi1/zVYcJeTv8CgWH6xN/r8u3V5bBtfvr5e4G+sQIJkKfQOhuy3cl4LH81hcrrVvChhOtYVRRCKmigKnrNm0TGc0JPHsqbmZCneJFikYRATKYv0FcmzgfTIZl+sdFC28c2vireEK9cnZm+EqpPjF/CCFs9qrLeiA4VR91uSvQgnvv1dopEb6CEFXMbytHIuArNygUuu9Z57h8490YEVAaAAtbotRQjZUw0csoWGI3U+3w8PpPcFvftLDyF1UlN6xNs9Sn+Fz3d0TxopnBqiR+YfUlrMq5tyPLW+OdAI/ITQa8Zl9ouvwZo9JqeiHjlXR0redmBzvYH5Vp6Padqe1x2x1tMwjCk01TPAsXw8HrbQX/+M02HT/jj3X5Ev/qETWvoETLYN869rofJePLryfjZyeT36dl5eX5WTn47HT87+xc+19njL/TpAbtou3ApsqHzXksW1QV69fzqUs4M+ZDTtZ48yOB0pYPIqda7ta4pKLJ167TloBbOK16Rqh2/JVYJV0T/npyqS/6cRoM2l7lCHwr5VRUyLZ3XtH03ca5CnO8cOVwfOicUCm2tYiCvMPKKLA9snoyjMe4upO2g2KmWvLCe2jFBUCFWK4VZRnla6sB5KwEbt9S2UEYH3jubpUgyqyzdDas1GToQOlA5isxkx1Vs01ZNjNoENd8cG7exmZMvVCD01WoLK2IN3lLyrXE1mUJVnnBv6bGcPRDYJy+xmuwNSyozUy5BOlRfQk5ebAv5SA2UtpWJtey2GMKd87XyFIhDUqUGtVFr8nqxLZqc1NYFbjDN4eEynblXDeSrjthXPcbWuwZO167WoLaCnHikG4j5BrDVqasSKhRwRM5QQLm/xM8KEP4Vra6bY6D33vS9LH+M5GXYzPa0mxi81kGe693Iu+fg7uoOP7wb7hg/Kiged3xYRLtJ7G6ivEEBt7Q5+tboZ30BK8I6Db1u2L/Ipk6mgrLXf/DFIKy4m2JXz6cXfwjTDZ8actqgBI938hWDd9kBl49CuinJWgcG7TLiUmQzaLoLRMn4rir3aDS5+miIXZclpu6WbN/vImZ5l2j7/n/fPM2Y
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateCarclubEnquirystatusByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/carclub/enquirystatus/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateCarclubEnquirystatusByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"text/json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}},"application/*+json":{"schema":{"required":["enquiryStatus"],"type":"object","properties":{"enquiryStatus":{"minLength":1,"type":"string"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"cancellationReasonId":{"type":"integer","format":"int32","nullable":true},"cancellationReasonNotes":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBookingEnquiryStatusDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      