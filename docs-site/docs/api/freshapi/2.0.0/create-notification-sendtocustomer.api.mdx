---
id: create-notification-sendtocustomer
title: "createNotificationSendtocustomer"
description: "createNotificationSendtocustomer"
sidebar_label: "createNotificationSendtocustomer"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v5DYM/SsCz9pMGmAvvhVbLJCi2AaYaXsI5sCxOWMhsqQlqUmnhv97IXm+kuxHLrYlPj4+U9QbQXEn0DzCl6hu61pUFwOsLTBJikFIoBnh7va2vDqSll2qkAaWuW1JBKbJglCb2ekBmscRNoRMDM3jelpbiIm4st530EDLhErXxZYUOo1tFo0DMdhXVd6RMJD2sZCnKAoWEmoPDSwwucV14qJkruKnS6YQ74mlqs7soYFeNUmzWPjYou+jaDOmyDq90fVHAZiZACzskR1u/NyukjH3a4vZKzTw8fb2F5imyb6us2WS/kNH+w+Y3A3+l5meaSNOSW4C6Zuyv9GefEwDBT0V/x5pRn0n6VJx58LuZ4Te7emdjA8cu9yWxZl0fZmSZdvTMLfqNCsj6CHRsWQ5mQqB5gSwx4/PkQcsHf39n1WdPBe2saY79QX/uUg1n5DNrw/35WCIZda0v3sjc9U7KTiTOO5dR2IodCm6oGK2kY32ZLqoX0hN5S3Qv+9uzL1+L2PAMPeyRRZbnqZFpV1kR6e1zxsjeXMWcr2/ifHJhZ1Yg6EzWYgNZu0p6HGIa3H0Pj5LDYvRaBLxNvJgzndNjOS2NzhjDNPOic6hSuzjzgVrvBO9iJ1RxGUn0PNxtyNPV6CrlBd/5mfhJqca6kjReTGbw8viIQ8bYmuEkNv+RFtgAz5R1TbEjrw19eKfK32rZ28Al+bl1M2x45YRRc0yH0FoyfsfMVcVp4P8xhkYF1qfuxJNKPIcuTNMQio1lQZ03uyJz85zA5Ot7jRgKMMasM727G3m2qPMj+3wfE2U/tVF8uhCoa63dTwa3yNgcmBfOrqFV+a3tlDsrcDHcYNCf7GfprL9NRMXI19fXK16+ZXRPvy5XIGF0pUrTa+uanXVYwjDoZqkz2U1jjNiFZ8oTMVbn+hQfqqsYVpP0/Q/4uhcHg==
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createNotificationSendtocustomer"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Notification/SendToCustomer"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createNotificationSendtocustomer

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      