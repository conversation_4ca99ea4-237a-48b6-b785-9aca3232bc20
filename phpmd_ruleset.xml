<?xml version="1.0"?>
<ruleset name="Boilerplate PHPMD rule set"
         xmlns="http://pmd.sf.net/ruleset/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0
                     http://pmd.sf.net/ruleset_xml_schema.xsd"
         xsi:noNamespaceSchemaLocation="
                     http://pmd.sf.net/ruleset_xml_schema.xsd">
    <description>
        Custom PHP Mess Detector ruleset
    </description>

    <rule ref="rulesets/cleancode.xml">
        <exclude name="StaticAccess"/>
        <exclude name="BooleanArgumentFlag"/>
    </rule>
    <rule ref="rulesets/codesize.xml">
        <exclude name="CyclomaticComplexity"/>
        <exclude name="NPathComplexity"/>
        <exclude name="ExcessiveClassComplexity"/>
        <exclude name="TooManyMethods"/>
        <exclude name="TooManyPublicMethods"/>
        <exclude name="ExcessivePublicCount"/>
    </rule>
    <rule ref="rulesets/naming.xml">
        <exclude name="ShortVariable"/>
        <exclude name="BooleanGetMethodName"/>
        <exclude name="CyclomaticComplexity"/>
        <exclude name="LongVariable"/>
    </rule>
    <rule ref="rulesets/unusedcode.xml"/>

</ruleset>
