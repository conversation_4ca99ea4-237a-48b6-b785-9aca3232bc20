{"bookingId": 0, "requestedDate": "2024-07-29T15:51:28.071Z", "confirmedDate": "2024-07-29T15:51:28.071Z", "addressId": 0, "additionalComments": "string", "notes": "string", "preferredTime": "2024-07-29T15:51:28.071Z", "bookingStatusId": 0, "totalCost": 0, "enquiryStatus": "string", "resourceId": 0, "resourceName": "string", "timeOfDay": "string", "bookingReferenceNumber": "string", "bookingHubDurationMinutes": "string", "overridePrice": 0, "bookingCustomerCars": [{"bookingCustomerCarId": 0, "bookingId": 0, "customerCarId": 0, "packageGroupId": 0, "registrationNumber": "string", "makeAndModel": "string", "category": "string", "colour": "string", "year": "string", "optionalExtras": [{"packageItemId": 0}]}], "isPrepaid": true, "isRefunded": true, "customerCarClubPackageId": 0, "currency": 0}