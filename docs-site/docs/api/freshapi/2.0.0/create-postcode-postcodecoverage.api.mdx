---
id: create-postcode-postcodecoverage
title: "createPostcodePostcodecoverage"
description: "createPostcodePostcodecoverage"
sidebar_label: "createPostcodePostcodecoverage"
hide_title: true
hide_table_of_contents: true
api: eJzlVktv3DYQ/ivEoKdWfgI9VJcgcRrARdAY9aYFutgAs9KsxJgiGXK4zkbQfy+GWmnXdlLnnotEDb/55sUZqgfGJkK5hBsXuXI1waqAQJ8SRX7l6h2UPVTOMlmWJXpvdIWsnT37GJ0VWaxa6lBWoqcD1cLnj/h45wlKcOuPVDEU4IPzFFhTFK0ZWfbQafuWbMMtlBcFeGSmYKGED0t8efLl31V/UVwOy/OT31ajQFYv1Isjyaq/HH6C2WbkoG0Dw1AA1rUWx9HcHNnfoIlUAGs2gr9qqbqbcvF68S5rMn3mHyjc4yL//MuPErfoBore2Tg6fHl+Lq+aYhW0F0oo4TZVFcWYLUWqUtC8g3LZw5owUIByuRpWBYjhnMDrGkqoAiHTZG56V25LARuC4pGNZ+EdceuEWHIKOYEtlHCGXp9N8HlxddCLFLYUYvY3BQMltMw+lmdnxlVoWhe57L0LPDzx6a0A1EgABWwxaFybqbKBx0xtMBmGEn49P7+QlBaP7WwCxfakpu0Jen2KX1Kge1pHzRRPLfETs69pS8b5jixPxr9FmpC/k/SWsdG2eY7Q6C19J+NNcHWq5GMmXR3Ox610zpiq6ZT008EVk1KZDIFyAhT7xRsXOpSM/vHPIp856ca/DuP598/YeUMP+2vuhgK03bhsbX/u30hk6gqDenlzLXWkEMcQtpdPolq0OgpO+eC2uqaoyNbeactRbVxQ3JKqHf9JrDKvQP++PFXX/C2NDu2Y+gpDLOSpKmRqXNA0fZu0VjGtZ0eO5Wvn7rRtYqHQ1ipFCgoTt2R5P6+ycTTG3ce8HRU75SlsXOjU3JRRxVS1CkeMCtToyONWJjau0bZQRkc+ODuiSDKrLN3vpTUZOgIdqTyIzIyOq+TzVk2M2kS13j00blO3plCoSBiqdqIVWId3lH3rXE2mUHlGzJa+lrMngEPykq/Hvb1IRUZOcSyBrciY/2POXkyF/EoNlLaVSbXseozx3oVaBYrEMatSh9qoLQW9mYomJ1WOb4f5prGYW2Ecg2qaY+q5uTn3VL6uvUFthTi3dr+fkUtAr6E4/O0clvOcXBUgk1DAfb/GSO+DGQYRf0oUZNqvDgMwD/wCWsI6T/8e7mgnF8z4z3SyEJcEbpK49uT/SebEPM5v3t0upPX3/13d2M0B76HIzxJArpZch3wRi6wHg7ZJ4nkJI2e+BpPEO+fk0VzJnu630O6OPOz7EbFwd2QHuQjGgFi+YVgNw/AfCZ6WRw==
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createPostcodePostcodecoverage"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Postcode/PostcodeCoverage"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createPostcodePostcodecoverage

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["postcode"],"type":"object","properties":{"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"}},"additionalProperties":false,"title":"CheckPostcodeDTO"}},"text/json":{"schema":{"required":["postcode"],"type":"object","properties":{"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"}},"additionalProperties":false,"title":"CheckPostcodeDTO"}},"application/*+json":{"schema":{"required":["postcode"],"type":"object","properties":{"postcode":{"minLength":1,"pattern":"^[aA-zZ]{1,2}[0-9][aA-zZ0-9]? ?[0-9][aA-zZ]{2}$","type":"string"}},"additionalProperties":false,"title":"CheckPostcodeDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      