---
id: get-package-information-all
title: "getPackage-informationAll"
description: "getPackage-informationAll"
sidebar_label: "getPackage-informationAll"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v5DYM/SsCz04mDdCLb4u2u0hRFAGS7h6COdA2xxZGllSSmuys4f9eSJ6PJJPd7mU8Ih8fKZJ6Eyj2AvUT3GO7xZ5gXQGTxOCFBOoJbm9u8qcjadlGtcFDDQ+pbUkE5rkCoTax1T3UTxM0hEwM9dN6XlcQIjHmkLsOauhJD0murN8EHovrg3NQvaH/EXIkHcKBDiqIqAPUsMJoV/EyZoUlSIh3xFJKTOyghkE1Sr1audCiG4JoPcXAOl/U8lcGmIUAKtghW2zc0pscsTRng8kp1PDrzc0vMM9z9TbPhkmGq452VxjtNX5LTM/UiFWSa19u8jrt77QjF+JIXo/Jv0eaUH+S9EGxt77/P0Jnd/STjPccutTmw4l0fV6Jh3agcWnVcTEm0H2kQ8o8mQKB+gioDn8+lglCDX9+eSxrlodawq26jP+YSzW/IZsP93d5MMSy1LS7vSjzcbCScSZy2NmOxJDvYrBexWwCGx3IdEH/JjWFN0M/316bO/1exIh+6WWLLFX+NS0q9YEtHc8uNUZScyrkpb0JYWt9L5VB35kkxAaTDuTVtmVzS3J0LjxLcYvRYCJxXm1zelhiJLWDwQVjmHorurgKsQu99ZVxVvRc7IIizhZPzwdrR45egF6EvLqZWwo3KRZXR4rWiWn2r5P7NDbElRFCbocjbYaNuKVS2xg6cpVpmfCc6b2eXQDOzUuxW3wHkxFFTbKMwLfk3I+YSxXHQb4zA2N961KXvRFFngN3hklIpYTSiNaZHbHdHIcGcwUxiI7o87J6LLvdk5p3BM28o32n96H0VVfRofWZszzT6aB2T4DRFu27oIQKsuKtK8ialqHT1KDQP+zmOZv/TcRZqtdnKStq/UJYP/3xmGlSTnUq583zLEp6cKHfF2F0KZ+maUE8hi35Oevplvb5PvkM83qe5/8ACZ1LdQ==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getPackage-informationAll"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/package-information/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getPackage-informationAll

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      