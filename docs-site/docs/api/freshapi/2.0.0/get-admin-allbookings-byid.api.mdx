---
id: get-admin-allbookings-byid
title: "getAdminAllbookingsByid"
description: "getAdminAllbookingsByid"
sidebar_label: "getAdminAllbookingsByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVU1v5DYM/SsCTy2gzaQpevEt23aLFEWxaNLuIZgDx+bYQmTJK1KTTg3/94Ky52uTtHuxLerx8Yki6REEW4bqEW69fx/jkwst3za9C7C2MGDCnoSSIkYI2BNUsJlhdw1YcAEqGFA6sJDoc3aJGqgkZbLAdUc9QjWC7Ad1dEGopQQWtjH1KLPp+xuYprW68xADE6vHzfW1vhriOrlBXNQ497muiRmmyQJTnZOTfRG2IUyUoHpcK1EcKKG63DVQQUtSznPr/SKc3++dar8kfxvXk3RxoQI7n7aCFQ5uheqxwpPLajxmZwJVmXaH7OXkoYJOZOBqtfKxRt9FlmocYpLphZ7fFGBmArCww+Rw4+fsqMecni1mr3n84fr6O5imyX4ZZ5uIu3cN7d7h4K7wn5zomTbshPgqlPNchv2JduTj0FOQQ/C3SDPKV5LeC7YutP9H6N2OvpLxY4pNrnVxJF2fiuJeK29O1aE0jkWoIWEpzlLNM8AuHx8Olfnrp4dSaC5sY3F34hX/QaWaHzGZ2493ejGUeNa0u3kh86FzrDgzpLhzDbGh0AzRBWGzjclIR6aJ8juJKbwK/evmytzJWx49hjmXNSa2+jQ1CrUxOTqsfd4YzpujkHP7oVCtwdCYzJQMZukoiKtL05Tg6H185rLNRqIZKGnLmmNrseFcdwZnjEnUOpZ5qxD72LpgjXcsJ7EzipJaAj0v1oY8nYHOXC5O5mfhJg9lqyFB59ls9pfBQ+43lKxhwlR3B1qF9fhERVsfG/LW1InwFOm1nL0AnJKXh2beW0yGBSXzfAWhJu//i7moOFzkK3dgXKh9bnR3QObnmBqTiEm4uFKPzpsdJbc9XBpMFobI0mPQYl0mdUtiylAzZ1PNvDb+ju0h9LesBo8uKGXp0nEZeY+AgwMLZejp+8QJFqrTb2FtQSebeozjBpn+TH6a1Pw5U9KRvT4NtDIbG8f63UC1Rc/0Qlwdg1DQtvzmj+Uv860B+7roxYhhX+amz7oCC0+0v/h7TevpbLj/8vODninrSY/Z+GI4FK2v0o/jjHiITxSm6RhNdK2RpulfQrOeAA==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminAllbookingsByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/allbookings/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminAllbookingsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      