{"name": "founderandlightning/freshcar-lbp-core", "type": "boilerplate", "description": "Fresh Car Core", "keywords": ["FounderAndLightning", "boilerplate", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-gd": "*", "ext-openssl": "*", "bugsnag/bugsnag-laravel": "^2.0", "dedoc/scramble": "^0.12.20", "doctrine/dbal": "^3.6", "elasticsearch/elasticsearch": "^8.9", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/promises": "^1.4.0", "intervention/image": "^2.7", "laravel/cashier": "^15.0", "laravel/framework": "^11.0", "laravel/pennant": "^1.3", "laravel/sanctum": "^4.0", "laravel/telescope": "^5.9", "laravel/tinker": "^2.8", "league/flysystem": "^3.0", "league/flysystem-aws-s3-v3": "^3.15", "mongodb/mongodb": "^2.1", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-permission": "^6.0", "spatie/laravel-webhook-client": "^3.4", "symfony/http-client": "^6.1", "symfony/postmark-mailer": "^6.1", "wildbit/postmark-php": "^4.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "enlightn/security-checker": "^2.0", "nunomaduro/collision": "^8.1", "enlightn/enlightn": "^2.6", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^2.0", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "pestphp/pest": "^2.3", "pestphp/pest-plugin-faker": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "phpmd/phpmd": "^2.13", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0", "squizlabs/php_codesniffer": "^3.7"}, "repositories": [{"type": "vcs", "url": "https://github.com/svik<PERSON><PERSON>t/composer-git-hooks"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "modules/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "dev": ["@post-root-package-install", "@putenv CACHE_DRIVER=file", "@post-create-project-cmd", "@php artisan migrate --seed", "chmod -R 777 storage bootstrap", "@cghooks", "@ide-helper"], "cghooks": "[ -f vendor/bin/cghooks ] && vendor/bin/cghooks update || echo 'cghooks not found'", "ide-helper": ["@php artisan ide-helper:generate -n", "@php artisan ide-helper:meta -n", "@php artisan ide-helper:models -n"], "phpmd": "vendor/bin/phpmd app text phpmd_ruleset.xml", "security-checker": "vendor/bin/security-checker security:check", "pint": "vendor/bin/pint app tests modules", "phpcs": "vendor/bin/phpcs app -n --standard=PSR12", "phpcbf": "vendor/bin/phpcbf app -n --standard=PSR12", "pest": "vendor/bin/pest --stop-on-error --stop-on-failure --parallel --compact", "coverage": "vendor/bin/pest --stop-on-error --stop-on-failure --parallel --compact --coverage", "postman": "postman collection run postman/ci-collection.json", "pre-commit": ["@pint", "@phpcbf", "@phpcs", "@phpmd", "@security-checker"], "pre-push": ["@pest", "@php artisan enlightn"], "post-merge": ["composer install", "@php artisan migrate", "@php artisan db:seed"], "tests": ["@pre-commit", "@pest", "@php artisan enlightn --ci"], "ci": ["@tests"]}, "extra": {"laravel": {"dont-discover": []}, "hooks": {"config": {"stop-on-failure": ["pre-commit", "pre-push"]}, "pre-commit": ["docker-compose exec -T api composer run pre-commit"], "pre-push": ["docker-compose exec -T api composer run pre-push"], "post-merge": ["docker-compose exec -T api composer run post-merge"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}