---
id: update-admin-customers-byid-addresses-byid
title: "updateAdminCustomersByidAddressesByid"
description: "updateAdminCustomersByidAddressesByid"
sidebar_label: "updateAdminCustomersByidAddressesByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVktz4zYM/iscnPpQ4jSdXnTLJrvTdDptZuO2h4wPsAjb3FCkloCcdTX67zugZDmJs20Onc5ebJEEPjwIfEQHgmuG8g4uW5ZYU7qwNhEz8YWtXYBFAQ0mrEkoqVwHAWuCEqpR/tpCAS5ACQ3KBgpI9LF1iSyUkloqgKsN1QhlB7JrVNMFoTUlKGAVU40ybP14Dn1fTPA4uPGfoi8GdWJ5E+1OdaoYhILoJzaNdxWKi2H2gWPQvSPwuPxAlUABTYoNJXHEeur4ijyJejWJLmP0hEGD0mCcAqO/eaS3Qs9UgDjxqnAbVzLAvA3iZHc1/z0rC32Sr8ujx6n67vuvyDVVT8RNDDzYOj870z9LXCXXKKrqtlVFzNkYU9UmJ7tc2UvCRAnKu4WWitrOMV5bKKFtLArlnth3Cr/ZOTu1iy6geGbqtVo1ySbaocorLfNc7SXMsHEzVPXZvt941h1ar5/hHmnWTS3TgwaWtvuObZOHEjYiDZezmY8V+k1kKbsmJumPnP5VBcwAAAVsMTlc+iGhqjFkdIWt1+b66ezsB0198dzOKhFvTixtT7Bxp/h3m+iBluyE+DSQHJm9oi352NQUZG/8S6AtyitBbwXXLqz/DdC7Lb0S8SZF21a6mEAXhzq61SYYUrWvpqnI1SSMjJWLfhAoxo93e7r65a95rk1trPcHvnr7CetGi/9JCykL9kqRq5hNjf3xTsMyl5jMxc21XiIlHvzfnh+FNN84VjnTpLh1lthQsE10QdisYjKyIWOj/EZiMq6K/nl+aq7lSxo1hiHvFSYu9NdUKLSOydF+7dul4XY5OfJ4fxnjvQtrLgwGa1qmZLCVDQUZeScbR+/jA+djNhJNQ0k530ydy4bbamNwkDGJ1o5lOMrAPq5dKIx3LAdnBylKuhPoYdy1mvCD0COVJ5H5wXHTNvnIkqDzbJa7p8ZDWy8pFYYJU7XZw6pYjfeUfaujJV+YKhEeLL2UsyOBQ/Iy/ejZuGVYUFoeriBU5P0/IWcv9hf5wh0YFyrfWj1tkPkhJmsSMQlnVarRebOl5Fb7S4O+gCay1JhfjPGpH0jSZJY0E00apUYzEaV5iV+n1spvZOPRBTWRO7wbOfQOsHGgz4qOM8U0tzAUUD4ZYiYq1ZPD/LEoQNlSkbpuiUx/JN/3uv2xpaQvx+JAkplvrWP9ttOr9czpaeqAb96P48y3BoqXgxk3MewyF/tWV1DAPe2eTmHKbf+j5UOC+kVfwIbQ5rezG88vB1Mnc0U56B+NWcqe0/N3czG//FkZcZzPtAmghIQPOvrhw+BAHCpUBfJeBx7DusW1yg6geZBo9f4PM8ZTus2uvhhi1w0S83hPoe+niEXXGm3ffwYEF/R5
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminCustomersByidAddressesByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/customers/{customerId}/addresses/{addressId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminCustomersByidAddressesByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}},{"name":"addressId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      