<?php

use App\Http\Controllers\Admin\ActivityLogController;
use App\Http\Controllers\Admin\DocsController;
use App\Http\Controllers\Admin\FeatureController;
use App\Http\Controllers\Admin\GohighlevelController;
use App\Http\Controllers\Admin\LoginController;
use App\Http\Controllers\Admin\LogViewerController;
use App\Http\Controllers\Admin\NewPasswordController;
use App\Http\Controllers\Admin\PackageController;
use App\Http\Controllers\Admin\ProfileController;
use App\Http\Controllers\Admin\RoleListController;
use App\Http\Controllers\Admin\ServiceGroupController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\WebhookMonitoringController;
use App\Http\Controllers\Admin\PasswordResetLinkController as AdminPasswordResetLinkController;
use App\Http\Controllers\Auth\PasswordResetLinkController as UserPasswordResetLinkController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', [HomeController::class, 'index'])->name('home');

Route::get('/api', function () {
    return redirect()->route('home.api');
})->name('api');

Route::get('/password-reset/{token}', [UserPasswordResetLinkController::class, 'verify'])->name('password.reset.verify');
Route::get('/verify-email/{id}/{hash}', [VerifyEmailController::class, 'verify'])
    ->middleware('throttle:6,1')
    ->name('verification.verify');

Route::prefix('/admin')->group(function () {
    Route::middleware('guest')->group(function () {
        Route::get('/login', [LoginController::class, 'index'])->name('admin.login');
        Route::post('/login', [LoginController::class, 'store'])->name('admin.login.store');
        Route::get('/forgot-password', [AdminPasswordResetLinkController::class, 'create'])
            ->name('admin.password.request');
        Route::post('forgot-password', [AdminPasswordResetLinkController::class, 'store'])
            ->name('admin.password.email');
        Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
            ->name('admin.password.reset');
        Route::post('reset-password', [NewPasswordController::class, 'store'])
            ->name('admin.password.store');
    });

    Route::middleware('permission:view dashboard')->group(function () {
        Route::middleware(['auth', 'verified'])->group(function () {
            Route::get('/', function () {
                return view('dashboard');
            })->name('admin.dashboard')->can('view dashboard');

            Route::get('/profile', [ProfileController::class, 'edit'])->name('admin.profile.edit');
            Route::patch('/profile', [ProfileController::class, 'update'])->name('admin.profile.update');
            Route::put('password', [ProfileController::class, 'updatePassword'])->name('admin.password.update');
            Route::delete('/profile', [ProfileController::class, 'destroy'])->name('admin.profile.destroy');

            Route::controller(UserController::class)
                ->prefix('/users')
                ->group(function () {
                    Route::get('/add', 'create')
                        ->name('admin.users.create')
                        ->can('create user');
                    Route::get('/{user}', 'edit')
                        ->name('admin.users.edit')
                        ->can('update user');
                    Route::post('/', 'store')
                        ->name('admin.users.store')
                        ->can('create user');
                    Route::patch('/{user}', 'update')
                        ->name('admin.users.update')
                        ->can('update user');
                    Route::patch('/{user}/verify-email', 'verifyEmail')
                        ->name('admin.users.verify_email')
                        ->can('update user');
                    Route::get('/', 'index')
                        ->name('admin.users.index')
                        ->can('view users');
                    Route::delete('/{user}', 'destroy')
                        ->name('admin.users.destroy')
                        ->can('delete user');
                });

            Route::controller(FeatureController::class)
                ->prefix('/features')
                ->group(function () {
                    Route::get('/', 'index')
                        ->name('admin.features.index')
                        ->can('view features');
                    Route::post('/toggle', 'toggle')
                        ->name('admin.features.toggle')
                        ->can('edit features');
                    Route::get('/add', 'create')
                        ->name('admin.features.create')
                        ->can('edit features');
                    Route::post('/', 'store')
                        ->name('admin.features.store')
                        ->can('edit features');
                    Route::delete('/', 'destroy')
                        ->name('admin.features.destroy')
                        ->can('edit features');
                });

            Route::controller(ServiceGroupController::class)
                ->prefix('/service-groups')
                ->group(function () {
                    Route::get('/', 'index')
                        ->name('admin.service-groups.index')
                        ->can('view service groups');
                    Route::get('/create', 'create')
                        ->name('admin.service-groups.create')
                        ->can('create service groups');
                    Route::post('/', 'store')
                        ->name('admin.service-groups.store')
                        ->can('create service groups');
                    Route::get('/{serviceGroup}', 'show')
                        ->name('admin.service-groups.show')
                        ->can('view service groups');
                    Route::get('/{serviceGroup}/edit', 'edit')
                        ->name('admin.service-groups.edit')
                        ->can('update service groups');
                    Route::patch('/{serviceGroup}', 'update')
                        ->name('admin.service-groups.update')
                        ->can('update service groups');
                    Route::patch('/{serviceGroup}/toggle-status', 'toggleStatus')
                        ->name('admin.service-groups.toggle-status')
                        ->can('update service groups');
                    Route::delete('/{serviceGroup}', 'destroy')
                        ->name('admin.service-groups.destroy')
                        ->can('delete service groups');
                });

            Route::controller(PackageController::class)
                ->prefix('/packages')
                ->group(function () {
                    Route::get('/', 'index')
                        ->name('admin.packages.index')
                        ->can('view packages');
                    Route::get('/create', 'create')
                        ->name('admin.packages.create')
                        ->can('create packages');
                    Route::post('/', 'store')
                        ->name('admin.packages.store')
                        ->can('create packages');
                    Route::get('/{package}', 'show')
                        ->name('admin.packages.show')
                        ->can('view packages');
                    Route::get('/{package}/edit', 'edit')
                        ->name('admin.packages.edit')
                        ->can('update packages');
                    Route::patch('/{package}', 'update')
                        ->name('admin.packages.update')
                        ->can('update packages');
                    Route::post('/{package}/duplicate', 'duplicate')
                        ->name('admin.packages.duplicate')
                        ->can('create packages');
                    Route::patch('/{package}/toggle-status', 'toggleStatus')
                        ->name('admin.packages.toggle-status')
                        ->can('update packages');
                    Route::delete('/{package}', 'destroy')
                        ->name('admin.packages.destroy')
                        ->can('delete packages');
                });

            Route::get('/roles', RoleListController::class)
                ->name('admin.roles.index')
                ->can('view roles');

            Route::get('/activity-logs', ActivityLogController::class)
                ->name('admin.logs.index')
                ->can('view logs');

            Route::get('/logs/viewer', [LogViewerController::class, 'index'])
                ->name('admin.logs.viewer')
                ->middleware(['can:view logs']);

            Route::get('/logs/content', [LogViewerController::class, 'content'])
                ->name('admin.logs.content')
                ->middleware(['can:view logs']);

            Route::get('/logs/download', [LogViewerController::class, 'download'])
                ->name('admin.logs.download')
                ->middleware(['can:view logs']);

            Route::controller(WebhookMonitoringController::class)
                ->prefix('/webhook-monitoring')
                ->group(function () {
                    Route::get('/', 'index')
                        ->name('admin.webhook-monitoring.index')
                        ->can('view webhook monitoring');
                    Route::get('/webhook/{webhookGroupId}', 'show')
                        ->name('admin.webhook-monitoring.show')
                        ->can('view webhook monitoring');
                    Route::get('/metrics', 'metrics')
                        ->name('admin.webhook-monitoring.metrics')
                        ->can('view webhook monitoring');
                    Route::get('/chart-data', 'chartData')
                        ->name('admin.webhook-monitoring.chart-data')
                        ->can('view webhook monitoring');
                });

            // Documentation routes
            Route::prefix('/docs')->group(function () {
                Route::get('/', [DocsController::class, 'index'])
                    ->name('admin.docs.index');
                Route::get('/{path}', [DocsController::class, 'show'])
                    ->name('admin.docs.show')
                    ->where('path', '.*');
            });
        });

        Route::post('logout', [LoginController::class, 'destroy'])
            ->name('admin.logout');
    });

    Route::middleware('permission:manage gohighlevel')->group(function () {
        Route::middleware(['auth', 'verified'])->group(function () {
            Route::controller(GohighlevelController::class)
                ->prefix('/gohighlevel')
                ->group(function () {
                    Route::get('/locations', 'locations')
                        ->name('admin.gohighlevel.locations')
                        ->can('view gohighlevel');

                    Route::get('/locations/edit/{location}', 'editLocation')
                        ->name('admin.gohighlevel.locations.edit')
                        ->can('edit gohighlevel');

                    Route::get('/locations/add', 'createLocation')
                        ->name('admin.gohighlevel.locations.create')
                        ->can('edit gohighlevel');

                    Route::get('/locations/{location_id}/refresh', 'refreshLocationToken')
                        ->name('admin.gohighlevel.locations.refresh')
                        ->can('edit gohighlevel');

                    Route::post('/locations', 'storeLocation')
                        ->name('admin.gohighlevel.locations.store')
                        ->can('edit gohighlevel');

                    Route::patch('/locations/{location}', 'updateLocation')
                        ->name('admin.gohighlevel.locations.update')
                        ->can('edit gohighlevel');

                    Route::delete('/locations', 'destroyLocation')
                        ->name('admin.gohighlevel.locations.destroy')
                        ->can('edit gohighlevel');

                    Route::get('/locations/import', 'importLocationsForm')
                        ->name('admin.gohighlevel.locations.import-form')
                        ->can('edit gohighlevel');

                    Route::post('/locations/import', 'fetchLocations')
                        ->name('admin.gohighlevel.locations.import')
                        ->can('edit gohighlevel');
                });
        });
    });
});

Route::get('/debug', function (Request $request) {
    return [
        'status' => 'Environment check',
        'timestamp' => now()->toIso8601ZuluString(),
        'environment_variables' => [
            'APP_NAME' => config('app.name'),
            'APP_ENV' => config('app.env'),
            'APP_DEBUG' => config('app.debug'),
            'APP_URL' => config('app.url'),
            'DB_CONNECTION' => config('database.default'),
            'DB_HOST' => config('database.connections.'.config('database.default').'.host'),
            'DB_PORT' => config('database.connections.'.config('database.default').'.port'),
            'DB_DATABASE' => config('database.connections.'.config('database.default').'.database'),
            'DB_USERNAME' => config('database.connections.'.config('database.default').'.username'),
            'LOG_CHANNEL' => config('logging.default'),
            'CACHE_DRIVER' => config('cache.default'),
            'SESSION_DRIVER' => config('session.driver'),
            'QUEUE_CONNECTION' => config('queue.default'),
        ],
        'request_headers' => $request->headers->all(),
        'server_variables' => $request->server->all(),
        'request_url' => $request->fullUrl(),
        'root_url' => url('/'),
        'is_secure' => $request->secure(),
        'corrected_url' => $request->headers->get('X-Corrected-URL', 'Not set'),
        'original_url' => $request->headers->get('X-Original-URL', 'Not set'),
    ];
});
