<x-app-layout>
    <x-slot name="header">
        {{ __('gohighlevel.locations') }}
    </x-slot>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <section>
                        <header>
                            <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                {{ __('gohighlevel.edit_location') }}
                            </h2>
                        </header>

                        <form method="post" action="{{ route('admin.gohighlevel.locations.update', $location) }}" class="mt-6 space-y-6">
                            @csrf
                            @method('patch')

                            <div>
                                <x-input-label for="name" :value="__('misc.name')" />
                                <x-text-input id="name" name="name" type="text" class="mt-1 block w-full"
                                    :value="old('name', $location->name)" required autofocus autocomplete="name" />
                                <x-input-error class="mt-2" :messages="$errors->get('name')" />
                            </div>

                            <div>
                                <x-input-label for="location_id" :value="__('gohighlevel.location_id')" />
                                <x-text-input id="location_id" name="location_id" type="text" class="mt-1 block w-full"
                                    :value="old('location_id', $location->location_id)" required autofocus autocomplete="location_id" />
                                <x-input-error class="mt-2" :messages="$errors->get('location_id')" />
                            </div>

                            <div>
                                <x-input-label for="private_integration_key" :value="__('gohighlevel.private_integration_key')" />
                                <x-text-input id="private_integration_key" name="private_integration_key" type="text" class="mt-1 block w-full"
                                    :value="old('private_integration_key', $location->private_integration_key)" autofocus autocomplete="private_integration_key" />
                                <x-input-error class="mt-2" :messages="$errors->get('private_integration_key')" />
                            </div>
                            <div>
                                <x-input-label for="client_id" :value="__('gohighlevel.client_id')" />
                                <x-text-input id="client_id" name="client_id" type="text" class="mt-1 block w-full"
                                    :value="old('client_id', $location->client_id)" autocomplete="client_id" />
                                <x-input-error class="mt-2" :messages="$errors->get('client_id')" />
                            </div>

                            <div>
                                <x-input-label for="client_secret" :value="__('gohighlevel.client_secret')" />
                                <x-text-input id="client_secret" name="client_secret" type="text" class="mt-1 block w-full"
                                    :value="old('client_secret', $location->client_secret)" autocomplete="client_secret" />
                                <x-input-error class="mt-2" :messages="$errors->get('client_secret')" />
                            </div>

                            <div>
                                <x-input-label for="base_url_authorization" :value="__('gohighlevel.base_url_authorization')" />
                                <x-text-input id="base_url_authorization" name="base_url_authorization" type="text" class="mt-1 block w-full"
                                    :value="old('base_url_authorization', $location->base_url_authorization)" required autofocus autocomplete="base_url_authorization" />
                                <x-input-error class="mt-2" :messages="$errors->get('base_url_authorization')" />
                            </div>

                            <div>
                                <x-input-label for="base_url_api" :value="__('gohighlevel.base_url_api')" />
                                <x-text-input id="base_url_api" name="base_url_api" type="text" class="mt-1 block w-full"
                                    :value="old('base_url_api', $location->base_url_api)" required autofocus autocomplete="base_url_api" />
                                <x-input-error class="mt-2" :messages="$errors->get('base_url_api')" />
                            </div>

                            <div class="flex items-center gap-4">
                                <x-primary-button>{{ __('misc.save') }}</x-primary-button>
                            </div>
                        </form>
                    </section>

                </div>
            </div>
        </div>
    </div>
</x-app-layout>
