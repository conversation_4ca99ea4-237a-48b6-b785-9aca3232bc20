<?php

namespace Database\Seeders;

use App\Models\CarCategory;
use Illuminate\Database\Seeder;

class CarCategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Small',
                'description' => 'Compact cars suitable for city driving',
                'price_multiplier' => 1.00,
                'is_active' => true,
            ],
            [
                'name' => 'Medium',
                'description' => 'Mid-size cars offering comfort and efficiency',
                'price_multiplier' => 1.25,
                'is_active' => true,
            ],
            [
                'name' => 'Large',
                'description' => 'Full-size cars with premium features',
                'price_multiplier' => 1.50,
                'is_active' => true,
            ],
            [
                'name' => 'SUV',
                'description' => 'Sport Utility Vehicles for versatile use',
                'price_multiplier' => 1.75,
                'is_active' => true,
            ],
            [
                'name' => 'Luxury',
                'description' => 'Premium luxury vehicles',
                'price_multiplier' => 2.00,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            CarCategory::updateOrCreate(
                ['name' => $category['name']],
                $category
            );
        }
    }
}
