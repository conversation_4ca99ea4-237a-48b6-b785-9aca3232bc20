curl -X POST https://0e9a-2a00-23c6-9120-f001-edff-5ec3-e83c-e609.ngrok-free.app/high/webhook/opportunity-status-update   -H "Content-Type: application/json"   -H "x-wh-signature: test-signature"   -d '[
    {
        "contact_id": "mYTqcN6I8esiYUgLSqSI",
        "first_name": "<PERSON><PERSON>",
        "last_name": "3",
        "full_name": "Luk 3",
        "email": "<EMAIL>",
        "tags": "submitted with no photos",
        "country": "GB",
        "date_created": "2025-06-02T08:48:24.571Z",
        "full_address": null,
        "contact_type": "lead",
        "opportunity_name": "Luk3",
        "status": "won",
        "lead_value": 2,
        "opportunity_source": "undefined",
        "source": "undefined",
        "pipleline_stage": "booked",
        "pipeline_id": "iy8yb8BBsbY1WiqzlcQ3",
        "id": "VOn9DcpHfTGo5iGwYHdH",
        "pipeline_name": "Smart Repair",
        "user": {
            "firstName": "<PERSON><PERSON>",
            "lastName": "<PERSON>",
            "email": "<EMAIL>"
        },
        "owner": "Morlene Lightning",
        "location": {
            "name": "Test Account",
            "address": "29 Stafford Street",
            "city": "Edinburgh",
            "state": null,
            "country": "GB",
            "postalCode": "EH3 7BJ",
            "fullAddress": "29 Stafford Street, Edinburgh EH3 7BJ",
            "id": "SiOmQ4U4fRp5dvSKYoLe"
        },
        "workflow": {
            "id": "08580efd-c57e-4fa3-b382-ceae806f79ae",
            "name": "testWorkflowWebhook"
        },
        "triggerData": [],
        "contact": {
            "attributionSource": {
                "sessionSource": "CRM UI",
                "medium": "manual",
                "mediumId": null
            },
            "lastAttributionSource": []
        },
        "attributionSource": [],
        "customData": []
    }
]'