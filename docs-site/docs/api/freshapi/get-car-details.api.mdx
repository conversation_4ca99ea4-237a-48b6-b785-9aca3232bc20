---
id: get-car-details
title: "Get car details"
description: "This endpoint allows you to retrieve the details of a specific car associated with the authenticated user. The car is identified by its unique ID."
sidebar_label: "Get car details"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get car details"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/cars/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint allows you to retrieve the details of a specific car associated with the authenticated user. The car is identified by its unique ID.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Car details retrieved successfully","content":{"application/json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}}}},"401":{"description":"Unauthorized - Authentication required"},"404":{"description":"Car not found"}}}
>
  
</StatusCodes>


      