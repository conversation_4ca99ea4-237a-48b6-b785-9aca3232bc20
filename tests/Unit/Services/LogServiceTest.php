<?php

namespace Tests\Unit\Services;

use App\Services\LogService;
use App\Services\MongoDBAtlasService;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class LogServiceTest extends TestCase
{
    private MongoDBAtlasService $mongoDBAtlasServiceMock;
    private LogService $logService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mongoDBAtlasServiceMock = Mockery::mock(MongoDBAtlasService::class);
        $this->logService = new LogService($this->mongoDBAtlasServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_store_log_successfully(): void
    {
        $identifier = 'Create User';
        $payload = ['user_id' => 123, 'email' => '<EMAIL>'];
        $metadata = ['source' => 'frontend'];

        $expectedResponse = [
            'insertedId' => '507f1f77bcf86cd799439011'
        ];

        // Mock the request to provide IP and user agent
        $request = Request::create('/test', 'POST');
        $request->server->set('REMOTE_ADDR', '127.0.0.1');
        $request->headers->set('User-Agent', 'Test Browser');
        $request->headers->set('X-Request-ID', 'test-request-123');
        
        $this->app->instance('request', $request);

        $expectedEnrichedMetadata = array_merge($metadata, [
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Browser',
            'request_id' => 'test-request-123',
        ]);

        $this->mongoDBAtlasServiceMock
            ->shouldReceive('storeLog')
            ->once()
            ->with($identifier, $payload, $expectedEnrichedMetadata)
            ->andReturn($expectedResponse);

        $result = $this->logService->storeLog($identifier, $payload, $metadata);

        $this->assertEquals($expectedResponse, $result);
    }

    public function test_get_logs_by_identifier(): void
    {
        $identifier = 'Create User';
        $expectedLogs = [
            'documents' => [
                [
                    '_id' => '507f1f77bcf86cd799439011',
                    'identifier' => $identifier,
                    'payload' => ['user_id' => 123],
                    'created_at' => '2023-01-01T00:00:00.000Z'
                ]
            ]
        ];

        $this->mongoDBAtlasServiceMock
            ->shouldReceive('find')
            ->once()
            ->with('api_logs', ['identifier' => $identifier], [])
            ->andReturn($expectedLogs);

        $result = $this->logService->getLogsByIdentifier($identifier);

        $this->assertEquals($expectedLogs, $result);
    }

    public function test_get_logs_with_filters(): void
    {
        $filters = ['identifier' => 'Create User'];
        $options = ['limit' => 10];
        $expectedLogs = [
            'documents' => [
                [
                    '_id' => '507f1f77bcf86cd799439011',
                    'identifier' => 'Create User',
                    'payload' => ['user_id' => 123],
                    'created_at' => '2023-01-01T00:00:00.000Z'
                ]
            ]
        ];

        $this->mongoDBAtlasServiceMock
            ->shouldReceive('find')
            ->once()
            ->with('api_logs', $filters, $options)
            ->andReturn($expectedLogs);

        $result = $this->logService->getLogs($filters, $options);

        $this->assertEquals($expectedLogs, $result);
    }

    public function test_store_log_handles_exception(): void
    {
        $identifier = 'Create User';
        $payload = ['user_id' => 123];
        $metadata = [];

        // Mock the request
        $request = Request::create('/test', 'POST');
        $this->app->instance('request', $request);

        $this->mongoDBAtlasServiceMock
            ->shouldReceive('storeLog')
            ->once()
            ->andThrow(new \Exception('MongoDB connection failed'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('MongoDB connection failed');

        $this->logService->storeLog($identifier, $payload, $metadata);
    }
}
