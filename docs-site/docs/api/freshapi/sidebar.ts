import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/freshapi/fresh-car-api",
    },
    {
      type: "category",
      label: "Authentication",
      link: {
        type: "doc",
        id: "api/freshapi/authentication",
      },
      items: [
        {
          type: "doc",
          id: "api/freshapi/register",
          label: "Register a new user",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/login",
          label: "Authenticate user",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/logout",
          label: "Logout user",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/forgot-password",
          label: "Request password reset",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/reset-password",
          label: "Reset password",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/resend-email-verification",
          label: "Resend email verification",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/resend-email-verification-for-user",
          label: "Resend email verification for specific user",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Car",
      link: {
        type: "doc",
        id: "api/freshapi/car",
      },
      items: [
        {
          type: "doc",
          id: "api/freshapi/list-all-cars-for-authenticated-user",
          label: "List user cars",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/register-new-car",
          label: "Register a new car",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/get-car-details",
          label: "Get car details",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/delete-car",
          label: "Delete a car",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "Car Club",
      link: {
        type: "doc",
        id: "api/freshapi/car-club",
      },
      items: [
        {
          type: "doc",
          id: "api/freshapi/list-car-club-subscriptions",
          label: "List subscriptions",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/create-a-new-car-club-subscription",
          label: "Create a new car club subscription",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/cancel-car-club-subscription",
          label: "Cancel a subscription",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/freshapi/pause-car-club-subscription",
          label: "Pause a subscription",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/resume-car-club-subscription",
          label: "Resume a subscription",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/create-car-club-booking",
          label: "Create Car club booking",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/update-booking-status",
          label: "Update booking status",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/get-cancellation-information-for-a-booking",
          label: "Get cancellation information for a booking",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/get-car-club-prices",
          label: "Get car club prices by package and category",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/get-car-club-cancellation-reasons",
          label: "Get Cancellation reasons",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Car Lookup",
      link: {
        type: "doc",
        id: "api/freshapi/car-lookup",
      },
      items: [
        {
          type: "doc",
          id: "api/freshapi/lookup-car-by-registration",
          label: "Look up car details by registration number",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/search-cars-by-make-model",
          label: "Search by make and model",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Car Categories",
      link: {
        type: "doc",
        id: "api/freshapi/car-categories",
      },
      items: [
        {
          type: "doc",
          id: "api/freshapi/get-car-categories",
          label: "List all car categories",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Payments",
      link: {
        type: "doc",
        id: "api/freshapi/payments",
      },
      items: [
        {
          type: "doc",
          id: "api/freshapi/create-payment-intent",
          label: "Create payment intent",
          className: "api-method post",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
