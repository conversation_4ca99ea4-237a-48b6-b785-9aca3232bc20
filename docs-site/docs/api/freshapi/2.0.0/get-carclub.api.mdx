---
id: get-carclub
title: "getCarclub"
description: "getCarclub"
sidebar_label: "getCarclub"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v3DgM/SsCz24mDbAX34rspshisSiQtHsI5kDbHFuILKkkNenU8H9fSJ6vJE3by3gkPr5HieKbQLEXqB/gGvnapQbWFTBJDF5IoJ7g6vIyfzqSlm1UGzzUcJfalkRgnisQahNb3UH9MEFDyMRQP6zndQUhEmNOue2ghp70GrnNItULvmehkXQI+wSoIKIOUMMKo121R5AQb4mliCZ2UMOgGqVerVxo0Q1BtJ5iYJ1fif2TAWYhgAq2yBYbt5w2ZyzH3WByCjX8cXn5HuZ5rl7qbJhkeNfR9h1Ge4HfE9MTNWKV5MKXyp/L/klbciGO5PUg/hZpQv1N0jvF3vr+V4TObuk3GT9x6FKbF0fS9anJd+1A43JVh1ZPoLtIe8ncmQKB+gCo9n9uAo+Yb/Tv/+7Lw7F+E0q6VZfxN7lUc41sPny6zY0hlqWm7dWrMu8HKxlnIoet7UgM+S4G61XMJrDRgUwX9F9SU3gz9MvVhbnVtzJG9MtdtshS5V/TolIf2NJh7VJjJDXHQs73mxAere+lMug7k4TYYNKBvNq2DEERR+fCk5SwGA0mEm8Cj+Y4KmIktYPBBWOYeiu6hAqxC731lXFW9FTsgiLOO56e9rsdOToDnaU8O5lbCjcpllBHitaJaXbPxX0aG+LKCCG3w4E2w0Z8pFLbGDpylWmZ8KT0ozt7BThdXordEttvGVHUJEsLfEvO/Yy5VHFo5A96YKxvXepyNKLIU+DOMAmplFQa0TqzJbabQ9NgriAG0RF9fqwey9vuSc0bZnacB6VvuooOrc8cZSynvZs9AEYLFRz8bF1BdqwcmKYGhT6zm+e8/TURZ2tdn4yquOuZTX786x4qyOc8E38xfMUn9yH0u2J7LuXVNC2I+/BIfs5u+Ui7XH1ew7ye5/l/P4MtGg==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getCarclub"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/carclub"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getCarclub

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      