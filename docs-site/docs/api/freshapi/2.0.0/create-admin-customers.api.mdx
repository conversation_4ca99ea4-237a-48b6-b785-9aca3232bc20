---
id: create-admin-customers
title: "createAdminCustomers"
description: "createAdminCustomers"
sidebar_label: "createAdminCustomers"
hide_title: true
hide_table_of_contents: true
api: eJztWN1v2zYQ/1cIPm5KIrsLuumtTVugQ9EEjbcBC/xwls42G4pUyaNTT/D/PhwlWfJH0qTPfnEk8r70u7vfMawlwcLL7E5eBU+2ROffFKUycppIh98Cenpri7XMaplbQ2iIH6GqtMqBlDUXX701vObzJZbAT6ynHBZsFYrCofcyieqQ0+dQztDJRGIJSstEzpXz9BlKlInU0D5OE0nrCmUm7ewr5iQTWTlboSOFPgbTRvux4LdWVhnCRTQ+t64EapZejWUiTdAaZhplRi7gJpGkiN9qWcL3T2gWtJTZKN269eSUWRxT7OPdVb5ME1kqs7W1b2oz+LwXa+6C92L1BmtWe1quAMLr+VvlWKA+wGKLKsudkYo5OwCoS80XnKNDk2Mf9Q/B7crleBV9UgZHXArWU24L9k72wfy4Wlr9ny6WHf97NfMM+Afq42Pqz4WFDbz6KQMRpheXzRbnPZfjZ9RrMOQibaAJpczu0mTUp+kJ7Nu+lFethU3z+Yq5BvTNIK1z0B4HCm3ZvWmweje55kA8GEXKY/G+a4GnsWLWg+La6HWL3bPdOwTCLojonXHH73RiyBNDnhjyxJAnhjzGkMOj5C+/nqjyRJUnqjxR5YkqD6iSlR36yhrf1OE4TflPgT53qmKbMpO3Ic+55lnaYx6corXM7mo5Q3DcN3fTzTSR7DlyLlexzKO7+H//9hZAJnuWHxEqkZaWjXB+uVeA0ygvoFIXwMIX+UDao1vxE0cUnJaZXBJVPru40DYHvbSesrqyjjYH/j+xgGgMyESuwClGOkLBGg0WcwiaK+AyTUcMWrLvZ+7QL88KXJ1Bpc7hv+DwAWdeEfpzg3Tg9h2uUNuqREOd88eMBqBnGr0lWCiz+JFBrVb4TIs3zhYh55et0WlfAbc8ThuoujrY1i675MxEEZl1Akn78KHrqT//mcSq4hH9pb8Zev8dyqoZfsMxmvbF3DN+P+z6xX6M9Wt7A6rfaEdPv7AzY+Q4Hf92lr4+G/8xGV1ml6Ns/Pt5+nr0r3xijvS2BuNiQPHpPmEfKLRMfHT91XC94c3+vWfE4ae3XJcy1srMbUxVC+UHLgtxBU68ufnITYDON/lfjQ9KYrJUnuVE5exKFegFmqKyypAXc+sELVEUlj4jiWiXRf8en4uP9JhGCaap2xycT/hX5EC4sE5h967DTPgw2wYyXJ9Ze6/MwicCTCGCRycg0BINtSfA6By0tg8+bntBVlTomNjFlrO88CFfCmhkhMOF8tRsRcPaLpRJhFae+mAbKWSYhcGHdrVAjQOhgcrOl+kmcBGquFUggdJezNa7zk2sqUR4BJcvO7MsVsI9xthKW6BORCTTradjmB0I9OCFqmj22iXhCSj4JgUmR62fshyj6BJ5JAdCmVyHgncr8P7BukI49Eg+qsYWFCt0at4lrZvuJcRjgWm6uZkXIg4M8ehY2dJQvCmpNCjD5iIb1u0wuZNQqdhXfDHd97LnsxqPDBap6xl4/MvpzYaXvwXkLrqb9pMizr5ELhGKOAhreY/reDiI99pnEw6ExXXggA7uuJlQt9Pu5vp2whzZ3o2XTRs7eJBJ/M2k5CkbMY8HUF6rpQazCLBg2cZmPBKEnRPyHgHHSNstMOtBhHXdSEzsPZoNT8zmg4jf5Wa62Wz+B9LUPR0=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAdminCustomers"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/admin/customers"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAdminCustomers

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["address","contactNumber","email","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true}},"additionalProperties":false,"title":"CreateCustomerDTO"}},"text/json":{"schema":{"required":["address","contactNumber","email","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true}},"additionalProperties":false,"title":"CreateCustomerDTO"}},"application/*+json":{"schema":{"required":["address","contactNumber","email","firstName","lastName"],"type":"object","properties":{"customerId":{"type":"integer","format":"int32","nullable":true},"title":{"maxLength":10,"type":"string","nullable":true},"firstName":{"maxLength":50,"minLength":1,"type":"string"},"lastName":{"maxLength":50,"minLength":1,"type":"string"},"contactNumber":{"maxLength":50,"minLength":1,"type":"string"},"email":{"minLength":1,"type":"string"},"dateOfBirth":{"type":"string","format":"date-time","nullable":true},"customerReferenceNumber":{"type":"string","nullable":true},"address":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"},"sanitisedEmail":{"type":"string","nullable":true,"readOnly":true}},"additionalProperties":false,"title":"CreateCustomerDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      