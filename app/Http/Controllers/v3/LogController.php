<?php

namespace App\Http\Controllers\v3;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreLogRequest;
use App\Services\Contracts\LogServiceInterface;
use App\Traits\HttpResponse;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Exception;

class LogController extends Controller
{
    use HttpResponse;

    public function __construct(
        private readonly LogServiceInterface $logService
    ) {
    }

    /**
     * Store a new log entry
     *
     * @param StoreLogRequest $request
     * @return JsonResponse
     */
    public function store(StoreLogRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            
            $result = $this->logService->storeLog(
                $validated['identifier'],
                $validated['payload'],
                $validated['metadata'] ?? []
            );

            return $this->response(
                $result,
                'Log stored successfully',
                Response::HTTP_CREATED
            );
        } catch (Exception $e) {
            return $this->response(
                null,
                'Failed to store log: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Retrieve logs by identifier
     *
     * @param string $identifier
     * @return JsonResponse
     */
    public function getByIdentifier(string $identifier): JsonResponse
    {
        try {
            $logs = $this->logService->getLogsByIdentifier($identifier);

            return $this->response(
                $logs,
                'Logs retrieved successfully'
            );
        } catch (Exception $e) {
            return $this->response(
                null,
                'Failed to retrieve logs: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Retrieve logs with optional filters
     *
     * @param StoreLogRequest $request
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $filters = request()->only(['identifier', 'created_at']);
            $options = [
                'limit' => (int) request()->get('limit', 50),
                'sort' => ['created_at' => -1] // Sort by created_at descending
            ];

            $logs = $this->logService->getLogs($filters, $options);

            return $this->response(
                $logs,
                'Logs retrieved successfully'
            );
        } catch (Exception $e) {
            return $this->response(
                null,
                'Failed to retrieve logs: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
