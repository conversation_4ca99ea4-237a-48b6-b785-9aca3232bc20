---
id: get-preferredvaleter
title: "getPreferredvaleter"
description: "getPreferredvaleter"
sidebar_label: "getPreferredvaleter"
hide_title: true
hide_table_of_contents: true
api: eJyNVU1v4zgM/SsCz56mW2Avvi12dxYdDBYF2pk5FDkwNmMLlSUNSbmbCfzfF7STNP2Yj0sSiY+PT5T4sgfFTqC+hxumLTFT+xkDKTGsK8jIONjCEHuIOBDUkJNok1oSqMBHqOFrId5BBdL0NCDUe9BdNqQo+9jBNK0rYJKcopBY/Ory0r5akoZ9Vp+M5rY0DYnANFUg1BT2upvrbgiZGOr7tRGlTIyWct1CDR3pSfl4UF69IH4bM5D26UABdlbtoYYVZr961QsTxOOxD4UD1NCrZqlXq5AaDH0Srfc5sU6vyn80gFsIoIIR2eMmLI2wjKUTWyxBoYbfLy9/g2maqpd1tkzSv2tpfIfZX+C3wvRIG/FKchHnIzwv+xeNFFIeKOqx+PdIC+ovkt4qdj52PyMMfqRfZLzh1JbGFifS9dP939qTWlp1fAWn12Ulj6/O1gdAdfjxPvGA1tEPX+7mN+XjNs3pXoPh35tU9yey++Pm2i6GWBZN49UrmXe9F8O5zGn0LYmj2Obko4rbJnbak2uT/kvqZl6Dfr66cNf6vYwB49LLBlkq+3QNKnWJPR3XoWyclM1JyPn+JqUHHzupHMbWFSF2WLSnqL6Z52MujiGkR5nD4jS5TLxNPLjTFImT0vQOF4xj6rzoEpqJQ+p8rFzwok9iFxTZdLtIj4fdlgKdgc5Snp0sLMJdyXOoJUUfxG12z4vHMmyIKyeE3PRHWoMN+ECztiG1FCrXMOFTpbd69grw1LyS2yV22HKiqEWWK4gNhfAj5lnF8SLfuAPnYxNKa9GMIo+JW8ckpDKn0oA+uJHYb4+XBlM1O+yA0R7rwXM7UvcznzsNhtJ/usoBfTSyeT73B3+7B8weqjfd3jzMEPv9BoU+cZgm217c3Xyv9WLG1UK9xSBUwQPtXvwfjBiKSZiH+Oh0s3OfGe4/f99BBdaoM9EvpncueAhh3J1R7/cL4i49UJzMbhcZamuY1tM0/Q8uS21b
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getPreferredvaleter"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/PreferredValeter"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getPreferredvaleter

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"postcodes","in":"query","schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      