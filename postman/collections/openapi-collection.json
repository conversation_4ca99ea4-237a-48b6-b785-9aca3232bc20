{"info": {"_postman_id": "11289616-21bd-4ecc-9464-8fc2e1482133", "name": "openapi-collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_uid": "33601963-11289616-21bd-4ecc-9464-8fc2e1482133"}, "item": [{"name": "/register", "id": "7673ad38-0fe6-4ac4-8c3e-42ed688f63c8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"neem+{{$randomInt}}@yopmail.com\",\n  \"password\": \"Test@4426\"\n}"}, "url": {"raw": "{{baseUrl}}/register", "host": ["{{baseUrl}}"], "path": ["register"]}}, "response": [{"id": "83f1b3b6-fc0f-4697-92ad-25a11cdfa5d5", "name": "<PERSON><PERSON><PERSON> successfully", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/register", "host": ["{{baseUrl}}"], "path": ["register"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"data\": {\n    \"first_name\": \"<string>\",\n    \"last_name\": \"<string>\",\n    \"email\": \"<string>\",\n    \"created_at\": \"<string>\",\n    \"id\": \"<integer>\",\n    \"onboarded\": \"<boolean>\"\n  },\n  \"message\": \"<string>\"\n}"}]}], "variable": [{"id": "c5039d80-1239-4801-b4c3-fecabb43b9c8", "key": "baseUrl", "value": "https://staging-fl-platform-api.herokuapp.com/api/v1", "type": "string"}]}