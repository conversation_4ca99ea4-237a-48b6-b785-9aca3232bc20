<!DOCTYPE html>
<html
    lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    x-data="{
      darkMode: localStorage.getItem('darkMode')
      || localStorage.setItem('darkMode', 'system')}"
    x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))"
    x-bind:class="{'dark': darkMode === 'dark' || (darkMode === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)}"
    class="bg-white dark dark:bg-gray-900">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>
        <link rel="icon" href="{{ asset('images/favicon.png') }}">
        <link rel="apple-touch-icon-precomposed" href="{{ asset('images/apple-touch-icon-precomposed.png') }}">
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="text-gray-900 antialiased">
        <!-- Dark mode toggle button -->
        <div class="absolute top-4 right-4 z-10">
            <button
                @click="darkMode = darkMode === 'dark' ? 'light' : 'dark'"
                class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
                aria-label="Toggle dark mode"
            >
                <svg x-show="darkMode === 'light'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                </svg>
                <svg x-show="darkMode === 'dark'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
            </button>
        </div>

        <!-- Main content -->
        <div class="min-h-screen flex flex-col justify-center items-center px-6 py-12 pb-24 bg-gray-50 dark:bg-gray-900">
            <!-- Logo section -->
            <div class="text-center mb-12">
                <!-- Combined logos -->
                <div class="mb-8 flex flex-col items-center space-y-8">
                    <!-- Fresh Car Logo -->
                    <div class="flex justify-center">
                        <x-freshcar-logo />
                    </div>

                    <!-- Partnership indicator -->
                    <div class="flex items-center space-x-6 text-gray-400 dark:text-gray-500 max-w-md w-full">
                        <div class="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
                        <span class="text-sm font-medium uppercase tracking-wider">powered by</span>
                        <div class="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
                    </div>

                    <!-- Founder and Lightning Logo -->
                    <div class="flex justify-center">
                        <x-main-logo class="w-20 h-auto text-gray-700 dark:text-gray-300" />
                    </div>
                </div>

                <h1 class="text-4xl font-bold text-gray-600 dark:text-gray-400 mb-4">
                    Welcome to {{ config('app.name', 'Fresh Car Core') }}
                </h1>

                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    Your mobile car care, management and subscription platform
                </p>
            </div>

            <!-- Action buttons -->
            <div class="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                <a
                    href="{{ route('admin.login') }}"
                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg text-center transition-colors duration-200 shadow-lg hover:shadow-xl"
                >
                    Admin Login
                </a>
                
                <a
                    href="{{ route('admin.login') }}"
                    class="flex-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-semibold py-3 px-6 rounded-lg text-center transition-colors duration-200 shadow-lg hover:shadow-xl"
                >
                    Get Started
                </a>
            </div>

            <!-- Additional info -->
            <div class="mt-16 text-center">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    Need help? Contact your system administrator
                </p>
        </div>

        <!-- Footer - Fixed to bottom of viewport -->
        <footer class="fixed bottom-0 left-0 right-0 w-full bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4">
            <div class="text-center">
                <p class="text-xs text-gray-400 dark:text-gray-500">
                    &copy; {{ date('Y') }} {{ config('app.name', 'Fresh Car Core') }}. All rights reserved.
                </p>
            </div>
        </footer>
    </body>
</html>
