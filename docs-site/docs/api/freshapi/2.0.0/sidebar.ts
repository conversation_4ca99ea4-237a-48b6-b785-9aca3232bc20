import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/freshapi/2.0.0/fresh-car-api",
    },
    {
      type: "category",
      label: "Account",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/register-account",
          label: "registerAccount",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/login-account",
          label: "loginAccount",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/confirm-email",
          label: "confirmEmail",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-account-information",
          label: "getAccountInformation",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/forgot-password",
          label: "forgotPassword",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/reset-password",
          label: "resetPassword",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/logout-account",
          label: "logoutAccount",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/refresh-token",
          label: "refreshToken",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-password",
          label: "updatePassword",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-consent",
          label: "updateConsent",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-account-updateuserdetails",
          label: "createAccountUpdateuserdetails",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-account-emailavailable",
          label: "createAccountEmailavailable",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-account-delete",
          label: "createAccountDelete",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Address",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-addresses",
          label: "getAddresses",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-addresses",
          label: "createAddresses",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-addresses-byid",
          label: "updateAddressesByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-addresses-search",
          label: "createAddressesSearch",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "AdminValeters",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-valeters",
          label: "getAdminValeters",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-valeters-get-all-valeters",
          label: "getAdminValetersGet-all-valeters",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "AllBookingsAdmin",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-allbookings",
          label: "getAdminAllbookings",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-admin-allbookings",
          label: "createAdminAllbookings",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-allbookings-byid",
          label: "getAdminAllbookingsByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-allbookings-enquirystatus-byid",
          label: "updateAdminAllbookingsEnquirystatusByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-allbookings-notes-byid",
          label: "updateAdminAllbookingsNotesByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-allbookings-customercars-byid",
          label: "updateAdminAllbookingsCustomercarsByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-allbookings-refund-byid",
          label: "updateAdminAllbookingsRefundByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-allbookings-statuscount",
          label: "getAdminAllbookingsStatuscount",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "BookableSlots",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-bookableslots-query",
          label: "createBookableslotsQuery",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Booking",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-paid-bookings",
          label: "createPaid-bookings",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-bookings",
          label: "createBookings",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-bookings",
          label: "getBookings",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-bookings-byid",
          label: "updateBookingsByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-bookings-byid",
          label: "getBookingsByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-bookings-enquirystatus-byid",
          label: "updateBookingsEnquirystatusByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-bookings-cancellationreasons",
          label: "getBookingsCancellationreasons",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Car",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-cars",
          label: "getCars",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-cars",
          label: "createCars",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-cars-byid",
          label: "getCarsByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-cars-byid",
          label: "updateCarsByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "CarCategory",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-carcategories",
          label: "getCarcategories",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "CarClub",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-carclub-subscribe",
          label: "createCarclubSubscribe",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-carclub-book",
          label: "createCarclubBook",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-carclub-enquirystatus-byid",
          label: "updateCarclubEnquirystatusByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-carclub-cancel-byid",
          label: "updateCarclubCancelByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-carclub-pause-byid",
          label: "updateCarclubPauseByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-carclub-resume-byid",
          label: "updateCarclubResumeByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-carclub",
          label: "getCarclub",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-carclub-prices-byid-byid",
          label: "getCarclubPricesByidByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-carclub-cancellationinformation-byid",
          label: "getCarclubCancellationinformationByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-carclub-subscriptioncancellationreasons",
          label: "getCarclubSubscriptioncancellationreasons",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "CarLookup",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-carlookup-registration-lookup-byid",
          label: "getCarlookupRegistration-lookupByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-carlookup-make-model-filter-byid",
          label: "getCarlookupMake-model-filterByid",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "CustomerAddressesAdmin",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-admin-customers-byid-addresses",
          label: "createAdminCustomersByidAddresses",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customers-byid-addresses-byid",
          label: "updateAdminCustomersByidAddressesByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "CustomerBookingAdmin",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-admin-customers-byid-bookings",
          label: "createAdminCustomersByidBookings",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customers-byid-bookings-byid",
          label: "updateAdminCustomersByidBookingsByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "CustomerCarClubAdmin",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-customercarclub-byid",
          label: "getAdminCustomercarclubByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customercarclub-cancel-byid",
          label: "updateAdminCustomercarclubCancelByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customercarclub-pause-byid",
          label: "updateAdminCustomercarclubPauseByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customercarclub-resume-byid",
          label: "updateAdminCustomercarclubResumeByid",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customercarclub-cleans-byid",
          label: "updateAdminCustomercarclubCleansByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "CustomerCarsAdmin",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-admin-customers-byid-cars",
          label: "createAdminCustomersByidCars",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customers-byid-cars-byid",
          label: "updateAdminCustomersByidCarsByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "CustomerPreferredValeterAdmin",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-customerpreferredvaleter-byid",
          label: "getAdminCustomerpreferredvaleterByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-admin-customerpreferredvaleter-byid",
          label: "createAdminCustomerpreferredvaleterByid",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customerpreferredvaleter-byid",
          label: "updateAdminCustomerpreferredvaleterByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "CustomersAdmin",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-customers",
          label: "getAdminCustomers",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-admin-customers",
          label: "createAdminCustomers",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-admin-customers-byid",
          label: "getAdminCustomersByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-admin-customers-byid",
          label: "updateAdminCustomersByid",
          className: "api-method put",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-admin-customers-login-byid",
          label: "createAdminCustomersLoginByid",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "MyBookingHub",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-api-mbh-bookings-byid",
          label: "updateApi-mbhBookingsByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "NewCustomerBooking",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-newcustomerbookings",
          label: "createNewcustomerbookings",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Notification",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-notification-sendtocustomer",
          label: "createNotificationSendtocustomer",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Package",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-package-information",
          label: "getPackage-information",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-package-information-byid",
          label: "getPackage-informationByid",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-package-information-all",
          label: "getPackage-informationAll",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-package-information-carclub",
          label: "getPackage-informationCarclub",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Postcode",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-postcode-postcodecoverage",
          label: "createPostcodePostcodecoverage",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-postcode-postcoderesource-byid",
          label: "getPostcodePostcoderesourceByid",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "PreferredValeter",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-preferredvaleter",
          label: "createPreferredvaleter",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-preferredvaleter",
          label: "getPreferredvaleter",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/freshapi/2.0.0/update-preferredvaleter-byid",
          label: "updatePreferredvaleterByid",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "StripeWebhook",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/create-stripewebhook",
          label: "createStripewebhook",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Valeter",
      items: [
        {
          type: "doc",
          id: "api/freshapi/2.0.0/get-valeters-byid",
          label: "getValetersByid",
          className: "api-method get",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
