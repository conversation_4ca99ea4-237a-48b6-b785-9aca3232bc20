---
id: update-admin-customercarclub-cleans-byid
title: "updateAdminCustomercarclubCleansByid"
description: "updateAdminCustomercarclubCleansByid"
sidebar_label: "updateAdminCustomercarclubCleansByid"
hide_title: true
hide_table_of_contents: true
api: eJzdVktz4zYM/iscnPpQ4jSdXnTLervTdDptJvG2h4wPMAlL3FAklw9nXY3+eweUbCeOs815L7YIAh8eJD6wh4RNhPoe5jkm11GYY5ibvLpSnbawrMBjwI4SBdbqwWJHUIN8rn2D8gEbulZQgbZQg8fUQgWBPmcdSEGdQqYKomypQ6h7SFvPONomaihABWsXOkyj6OdLGIblaE4xvXNqyzbS2UQ28Sd6b7TEpJ2dfYrOsuwAfnB7D9IQ2nhLHWqrbcMpTb7d6hPJBBX44DyFpCkWL0cGbwq2AlRKczhobp7ArdFEqiDpZBjho1eYaCravDh6v/ir2Cf6kr6RVJ4ezg8/fgs5MUKg6J2NYxSXFxf8pyjKoD0DQw13WUqKsfiLJHPQaVuaZkUYKEB9v+Rbze5Lca4V1JCLz9JuuxaUGOQ+gHdbzV313NMbjTpKrVNjO0rux9KWNczQ6xmy9Uw+N5+N5Z31rzX4AJxb2Oz4IAcDNbQp+VjPZsZJNK2Lqe69C2l4EfgfrCBGAKhgg0Hjyow1ZYuxqGvMho/vl4uLn7j61bGfdaDYninanKHX5/hvDvRIq6gTxXNL6YXb97Qh43xHNu2cvwaaMb0R9C5ho23zf4BGb+iNiDfBqSx5sQddHq7SHTfQWKrdhdrffXYJE7/yelKopo8Pu4b4/Z9FuZ7clLcHdv31C3aeW+BEf10MzOlrV7xNjfKBMxNzDOLq5prPkUIcU9hcvshq0erIesIHt9GKoiCrvNM2RbF2QaSWhHLpT0qi4LLq35fn4jq9ZtGhHUsvMcSKf4XERI0LmnZrk1ci5tU+kKfylXMP2jaxEmiVyJGCwJxasmmireIcjXGPsWxHkZzwFJhYxL5/o4hZtgJHHRGo0TGNWwXYuEbbShgd0yHYUYsCSyw9TlJFhp4oPTF5lpkZAxfZly1FCbWJYrV97tzmbkWhEpEwyHYHy2odPlCJrXOKTCVkIDx4OlWzFwqH4hUW4r1JJGLClON4BFaSMV9DLlHsDvLEGQhtpcmKdz3G+OiCEoEipVhM+YIasaGg17tDg6EC72LqsAyc6akycqUoZCmO2FKMdClOkey+t8pk9ga1ZQelxfuJSe8BvQaeLvxaquCITVlSHEAF9atPpmUFTJmM1vcrjPQxmGFg8edMgSfI8sCUhXSVjvyt9gPsKPD9Qwm+u51m7fcCqtMJTUK020LIJvMKKnig7dceesNyqKAlVGWw9ZP6fPR8tmDQA9yL5xrz2n463Vwt5r8xV03vPL6bUEPAR35C4uMYjxsvTnlAsKwHg7bJ2LDuCFoGfeaD2R/eERGWUE9m3PejxsI9kB2GfQESrznbYfgPKEP1bg==
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminCustomercarclubCleansByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/customercarclub/cleans/{customerCarClubPackageId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminCustomercarclubCleansByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerCarClubPackageId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["cleansRemaining"],"type":"object","properties":{"cleansRemaining":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"UpdateCarClubCleansDTO"}},"text/json":{"schema":{"required":["cleansRemaining"],"type":"object","properties":{"cleansRemaining":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"UpdateCarClubCleansDTO"}},"application/*+json":{"schema":{"required":["cleansRemaining"],"type":"object","properties":{"cleansRemaining":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"UpdateCarClubCleansDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      