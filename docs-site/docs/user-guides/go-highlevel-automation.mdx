---
sidebar_position: 2
---

import ScribeHowEmbed from "../scribeEmbed.js";

# HighLevel Automation

The SMART repair automation removes the need for SMART repair operators to manually book customer repairs into the MyBookinghub system. It does this by automatically creating bookings in MyBookinghub when a customer and an operator have completed the quote process, and the Booking is 'Won'.

There are a few things that need to be configured in order for this to work.

## Configuration

### Sub Accounts Setup

To enable the automation, we have to create a Private Integration Key and give it the scopes that are needed to collect data from HighLevel.

<ScribeHowEmbed url="Create_Fresh_Car_Core_Smart_Automation_Integration__fMvYvUcwSaKmAAqZEkc0Xg" />

Once you have copied the Private Integration Key, you will need to add it to the Fresh Car Core application.

1. Navigate to [Locations](http://localhost:1010/admin/gohighlevel/locations)
2. Click on 'Edit' against the Sub Account you created the key for
3. Paste the Private Integration Key in the 'Client Secret' field
    - Client ID: The client ID of the Private Integration Key

### Webhook

In order for the application to know when a booking has been won, a webhook needs to be configured in GoHighLevel. This will be done by a TLG developer and involves the following steps

1. Go to the 'Settings' tab in GoHighLevel
2. Click on 'Integrations'
3. Click on 'Webhooks'
4. Click on 'Add Webhook'
5. Set the 'Event' to 'Opportunity Status Update'
6. Set the 'URL' to the URL of the application followed by `/high/webhook/opportunity-status-update`
7. Set the 'Content Type' to 'application/json'

Once the webhook is configured, the core application will receive a notification when a booking is won and will automatically create a booking in MyBookinghub using the Fresh Car API.
