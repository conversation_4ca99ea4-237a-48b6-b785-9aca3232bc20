<?php

namespace Database\Factories;

use App\Models\Car;
use App\Models\CarCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CarFactory extends Factory
{
    protected $model = Car::class;

    public function definition(): array
    {
        $makes = ['BMW', 'Audi', 'Mercedes', 'Ford', 'Volkswagen', 'Toyota', 'Honda'];
        $models = ['3 Series', 'A4', 'C-Class', 'Focus', 'Golf', 'Corolla', 'Civic'];
        $colours = ['Black', 'White', 'Silver', 'Blue', 'Red', 'Grey'];
        
        $make = $this->faker->randomElement($makes);
        $model = $this->faker->randomElement($models);
        
        return [
            'user_id' => User::factory(),
            'registration_number' => strtoupper($this->faker->bothify('??## ???')),
            'make_and_model' => $make . ' ' . $model,
            'make' => $make,
            'model' => $model,
            'category_id' => CarCategory::factory(),
            'colour' => $this->faker->randomElement($colours),
            'year' => $this->faker->numberBetween(2010, 2024),
            'fuel_type' => $this->faker->randomElement(['Petrol', 'Diesel', 'Electric', 'Hybrid']),
            'engine_size' => $this->faker->randomElement(['1.0L', '1.4L', '1.6L', '2.0L', '2.5L', '3.0L']),
            'transmission' => $this->faker->randomElement(['Manual', 'Automatic']),
            'vin' => strtoupper($this->faker->bothify('?????????????????')),
            'mot_expiry' => $this->faker->dateTimeBetween('now', '+2 years'),
            'tax_expiry' => $this->faker->dateTimeBetween('now', '+1 year'),
            'insurance_group' => $this->faker->numberBetween(1, 50),
            'co2_emissions' => $this->faker->numberBetween(80, 300),
        ];
    }
}
