---
id: create-carclub-book
title: "createCarclubBook"
description: "createCarclubBook"
sidebar_label: "createCarclubBook"
hide_title: true
hide_table_of_contents: true
api: eJztWk1z2zYQ/SsYHFvaltVm0vKWyEnrTpNoYrWdqcaHJbmSEIMAAyykqBr+9w4ASqQs2WGTTA+JLrYELHYX+/He0uaGE8wtT6d8BGYkXcZvE27wvUNLz3Wx5umG51oRKvIfoaqkyIGEVhfvrFZ+zeYLLMF/onWFPOU6e4c58YRXRldoSKANaqKB51rfCTX/uDwUhUFrr4uOqFCEczQ84TNtSqC49MOQ1wnPnSVdohmB+ZQzIyCca7PunLRkvKcJV05KyCTylIzDOuG68iEA+eIDGbCdI2AMrHnCBWFpP37HCvI7mOM1YdnT5zrxgRHR/LijbAbSYsJJkPeTN2EetwbedH2+mrzx9z+4WK7VTJgSiysgPBaKnT8FEJ6RKNHrqQzO0BgsJn6h37Ej1tubjXRZoiLbKxsGrXYmx95pF3ZssALRlc+0lgjqflVIl21j2Fd7k9RfjHZV70NZzNevLrtyJjTYK6EcYb8I7PfWqPX/M2rz1E9fop/+91Lqe6HRQyXT+Hj8Rr21GwTCfRuNxgrWvrWvkEDIHjkVdgzr13p1vFcbba+QFrrYC9HD1dacuQ609ilnRlKgohvMDVJPhHrvhEH7LPeRO3aTvoEd70UvhPRzcvI2Uv1WD+EHOhH7VwlEJ2I/EfuJ2E/EfiL2b5HYuw/u331/YvivEZFODH9i+BPDnxj+xPDfHsPXEUsrrWwM/XAw8L8KtLkRVXSd37g8R2uDXYu5M4LWPJ1ueIZg0PB0elvf+pbGiGM+wDzf2s4b2zy5p/aYRBmyylNeaRsKA2jBU34BlbjIo+RFFkUtmiV6uJtuuDOSp3xBVNn04kLqHORCW0o3lTZUH1j+3QuwqIAnfAlG+ORFQNGGYghm4KRvsyeDwaWPVXLfzsygXZwVuDyDSpzDP87gCjMrCO25Qjowe4VLlLry2dwaf0ipA+qp9IZgLtT8YwqlWGJPjWOjCxfKdqf0tk38jR/+Yqi26d/VtjfpMxNEfK1HgaT58HILXL/9NQnF5AfKt+1/jV58gLKSeHx07AyHg4PRb/AAEbUNep9vpgfUMfC3vDcN8eFg+OPZ4OnZ8OfJ5ZP0yWU6/Ol88PTyb34wAT0memzKaV3rDjODvVHFY8ljjDI45IvBo4NFa/Sx+WF6MAp8ufj+p7vUt0fZo+WHGKADRmjdOgD+B7b28b2bm30Y31KiUDMdCr9B2pe+ydgIDHs2vvaQgsbGbloODxpsshDWy7HK6KUo0DJURaWFIstm2jBaICs0vUZiQa8X/XN4zq7poRMlqIgCORib+J8sj0kSuP0uXcasy3aOdNebkrEJA1UwZ9EwcLRARc3TXzAOUuqVDduWkWYVGj+LsB3wW2ZdvmAQZZjBubAUt4JiqedCJUwKS62zUQp9wJnCVbNaoMSOUOfI3s1kdJy5KmwVsUhYtt43rlyZoUmYRTD5YqvWi5Vwh8G3UhcoExZIaWfpWMwOBNrguaqIe80SswTkbEyBylHKxzQHL7aJPJIDJlQuXeF3K7B2pU3BDFokG45iCUKyJRox2yYtjEvaUglhAlEQgCryLmuIlx3j5h2ih7/sVxJEGL0CsWwaUp5yqEREEq+HR+DxLwJ46vX7m00GFv8wsq798nuHHjOmty3jhtEh4QuEIswRG36HHlZG8d2Bs4n3wotLF54N7r9H4CFlNzKM39xMghfx/QOfUJ5yAyvfx7DiKd/hVICRsLbhEtTcwdzLRp1hvHL+iu3Qtk9kwdPt84padzzcbKLERN+hqv3kES9E/juvb+u6/heFF7cN
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createCarclubBook"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/carclub/book"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createCarclubBook

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"carClubBooking":{"type":"object","properties":{"addressId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"confirmedDate":{"type":"string","format":"date-time"},"preferredTime":{"type":"string","format":"date-time","nullable":true},"additionalComments":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"isPrepaid":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"bookingHubDurationMinutes":{"type":"string","nullable":true},"carClubBookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"CarClubBookingCustomerCarPackageDTO"},"nullable":true}},"additionalProperties":false,"title":"CreateCarClubBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateCarClubBookingRequestDTO"}},"text/json":{"schema":{"type":"object","properties":{"carClubBooking":{"type":"object","properties":{"addressId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"confirmedDate":{"type":"string","format":"date-time"},"preferredTime":{"type":"string","format":"date-time","nullable":true},"additionalComments":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"isPrepaid":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"bookingHubDurationMinutes":{"type":"string","nullable":true},"carClubBookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"CarClubBookingCustomerCarPackageDTO"},"nullable":true}},"additionalProperties":false,"title":"CreateCarClubBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateCarClubBookingRequestDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"carClubBooking":{"type":"object","properties":{"addressId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"confirmedDate":{"type":"string","format":"date-time"},"preferredTime":{"type":"string","format":"date-time","nullable":true},"additionalComments":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"isPrepaid":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"bookingHubDurationMinutes":{"type":"string","nullable":true},"carClubBookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"CarClubBookingCustomerCarPackageDTO"},"nullable":true}},"additionalProperties":false,"title":"CreateCarClubBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateCarClubBookingRequestDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      