---
id: create-admin-customers-byid-addresses
title: "createAdminCustomersByidAddresses"
description: "createAdminCustomersByidAddresses"
sidebar_label: "createAdminCustomersByidAddresses"
hide_title: true
hide_table_of_contents: true
api: eJztV99v2zgM/lcEPt0Pb0k77MVvXXcDehhuxdq7eyjywNhMrFWWPIlKljP8vw+UHTtJu604YG99SSyK/EhR1CeqBcZ1gPwOLmNgV5O/KEtPIVC4KGttYZFBgx5rYvKi14LFmiCHYtC/KiEDbSGHBrmCDDx9jtpTCTn7SBmEoqIaIW+Bd41Yasu0Jg8ZrJyvkXvRq3PoukVvToHfuHInNoWzTJblE5vG6AJZOzv7FJwV2QQ+ub0D7NfwXls6gwwaF7hwJUEG7LZpTUMobvmJChYV7xryrCkkT739VfmUqDOw0RhcGupX3GXH/vMWavzynuyaK8jPXs8zqLUdx2Msgb22azg2P3/M/Njg+/5f/S+AlKZjwyfEPeb5xOX5Dy0LFy37tOFkYw353Tw7m7bpO7lnzRI3XA4IXb98LVWC5vpgW1doAh0YHNf729sPyZbpCz9X13N1/ZzqOqSw335/LrPnMvsJZSbWnkLjbOgr4Xw+l7+SQuF1I6CQw00sCgoh+QpURK95l+73JaEnD/ndQq5jcZ3qVeoICk/IlDqDvevwZqfLsWmA7MTNUyxq4soJvORe6lgaiRxm2OgZiuVs32yEWTv1Hd0MD0AC+c2+RYneQA4VcxPy2cy4Ak3lAudt4zx3D2J8LwqqB4AMNui1FFLKnVj0yVthNLJpr+fzM8lydupn5SlUL0ravMBGv8T/oqctLYNmCi8t8QO3b2lDxjU1Wd47/xZoRH4i6A3jWtv1jwCN3tATEa+9K2MhgxF0MZXMjXBXn6p94YxsIy5haABlPChkw8e7/TH489/bVIbChx+n9u+PL1g3hk64bH7KTBMDHFPOo/JXh/KeIKbxdPQn2Xio5520uSuX1jccv3eSS3WJXl1cX0nlkA990jbnD/J4W+kgeqrxbqNLCops2ThtOaiV84orUqXjv4hVwhXVf85fqiv+lkWNtt/sAn3I5FcVyLR2XtN+bOJShbgcAzmUL52713YdMoW2VDGQVxi5IsvDHZWcozFuG9J0UOxUQ14ITI3MEFSIRaWw11Ge1jpwP5WAjVtrmymjA0/B9lokWVaWtoO0JEMHSgcmRyszfeAqNmmqJEZtglrujp3bWC/JZyoQ+qLaw4pajfeUYqtdSSZTiaVGT4/l7IHClLzYlP3cIFKBkWPot8AWZMz3kFMU+418ZA+UtoWJpcw2GMLW+VJ5CsQhmVKN2qgNeb3ab9r+FqsxXX/791oiYpWYWI1UrISL1TfpezzKqS1uDGor6IlR2oGn7wAbnU6ZvBez8WEoYPnRK3Gi60UGQshi3LZLDPS3N10n4s+R5LTdLSYeTpRe6iDf5XgFnsQ5vhPhl49DE/Wrguzx+Ach2l2iexNlBBnc0+74ZdstugwqwjJdiO2gcNn7enErMBPAg/ep8OR4t11/uLkV6hvetXXPMx638mTGbR+A66sitYIia8GgXUdci26PmXqDKIkfN+eEV1Okjy6xbXuNW3dPtuvGFbOMZbFd9xVO1ZFe
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAdminCustomersByidAddresses"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/admin/customers/{customerId}/addresses"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAdminCustomersByidAddresses

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"}},"text/json":{"schema":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"}},"application/*+json":{"schema":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      