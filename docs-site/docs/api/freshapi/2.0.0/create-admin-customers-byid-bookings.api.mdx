---
id: create-admin-customers-byid-bookings
title: "createAdminCustomersByidBookings"
description: "createAdminCustomersByidBookings"
sidebar_label: "createAdminCustomersByidBookings"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAdminCustomersByidBookings"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/admin/customers/{customerId}/bookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAdminCustomersByidBookings

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"}},"text/json":{"schema":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"}},"application/*+json":{"schema":{"required":["addressId","bookingStatusId","requestedDate"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32","nullable":true},"requestedDate":{"type":"string","format":"date-time"},"confirmedDate":{"type":"string","format":"date-time","nullable":true},"addressId":{"type":"integer","format":"int32"},"additionalComments":{"type":"string","nullable":true},"notes":{"type":"string","nullable":true},"preferredTime":{"type":"string","format":"date-time","nullable":true},"bookingStatusId":{"type":"integer","format":"int32"},"totalCost":{"type":"number","format":"double"},"enquiryStatus":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"timeOfDay":{"type":"string","nullable":true},"bookingReferenceNumber":{"type":"string","nullable":true},"bookingHubDurationMinutes":{"type":"string","nullable":true},"overridePrice":{"type":"number","format":"double","nullable":true},"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"},"nullable":true},"isPrepaid":{"type":"boolean"},"isRefunded":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32","nullable":true},"currency":{"enum":[0,1],"type":"integer","format":"int32","title":"Currency"}},"additionalProperties":false,"title":"BookingDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      