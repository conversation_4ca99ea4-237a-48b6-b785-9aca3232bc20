---
id: get-postcode-postcoderesource-byid
title: "getPostcodePostcoderesourceByid"
description: "getPostcodePostcoderesourceByid"
sidebar_label: "getPostcodePostcoderesourceByid"
hide_title: true
hide_table_of_contents: true
api: eJyVVU1vIzcM/SsCT11gNk4D9DK3fu0iRVEEm7Q9BD7QEj0jRCNpRcqpO5j/Xmg+bCdxmuzFHknk4xNJPfYg2DDU93ATWHQwBOsKIibsSCiVkx48dgQ1xMWiAuvLGqWFChJ9zTaRgVpSpgpYt9Qh1D3IPhY/lmR9A8NQHaDQGCs2eHRLWF5Qv2ZKe3gfjA5dh7dU6AqZG9QP2NDnFHK8Nt8AuC6X4Bg8E5fzq8vL8meIdbKx8IQabrPWxFziA5POycp+zM6GMFGC+n5dgEKkhMXl2kANDclyweU/EYecNP20twaqZ0Hetu9I2jBDQzXVoIYVRrtaPA4fX2bXVb+Ubih5oLRbKpuTgxpakcj1auWCRtcGlrqPIcnwgt7vxUBNAFDBDpPFjZuSVjymrG0xO4Eafri8/B6GsV5P42wTcfvR0O4jRnuB/+ZEj7RhK8QXfrzW07C/0I5ciB15WYK/BppR3gl6K9hY37wF6OyO3ol4k4LJuiwOoOtjr9yW9ptStXTMoRNLyKVDy3o2qOaPTyF1WDL62993Y/9Zvw2juxVX7D8VqupnTOrHm+tSGEo8cdpdvaB511oudiqmsLOGWJE3MVgvrLYhKWlJmSB/kKgRt5j+dXWhruU1jw79lEuNiavyqzQKNSFZWtYubxTnzYHI6f4mhAfrG64UeqMyU1KYpSUvVo9vaQyOzoVHHo9ZSVCR0jakTh1eHCvOulU42ahEjWWZjkZgFxrrK+Usy5HsZEVFCZSnx3nXkKMToxOXJzdzE3GV43hkSNA6Vpv90+A+dxtKlWLCpNsFtph1+EAjty4YcpXSifAY6VzOXhgck5ejmc7mLcWCknkqgdfk3P8hjyyWQp6pgbJeu2zKaUTmx5CMSsQkPLpSh9apHSW7XYoGQzXOjA59adZZsxsStQiUei5y6pwqHp6J0D+yig6tL9Dja+1nBbwHjBaq4xQ7fi4qCBXU8WTIFaErjn2/QaY/kxuGsj2NiyKOxnJRNwP1Fh1TBQ+0f3V27dDlwnEUklc83xpXR4z1UVzPU3mWIB28kC8S8d2XeRx/UFCdT9y8iX5/GnMheUjRsB5Oxs3nX++ggtISJwV5plMj1bPofT9Z3IUH8sNwCCZlXSINw38vJQXW
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getPostcodePostcoderesourceByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/Postcode/PostcodeResource/{postcode}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getPostcodePostcoderesourceByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"postcode","in":"path","required":true,"schema":{"type":"string"}},{"name":"additionalPostcodes","in":"query","schema":{"type":"string"}},{"name":"commaSeparatedPackageGroupIds","in":"query","schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      