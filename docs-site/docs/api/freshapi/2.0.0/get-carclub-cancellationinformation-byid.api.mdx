---
id: get-carclub-cancellationinformation-byid
title: "getCarclubCancellationinformationByid"
description: "getCarclubCancellationinformationByid"
sidebar_label: "getCarclubCancellationinformationByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVV1v7DQQ/SvWPIHk2y1FvOQNFi4qQuiKFnio9mGSzCZWHdvXM96yRPnvyE7263YLfdmN5+Oc8Xh8PIJgx1A9wRrj2qYaNhoCRhxIKGbHCA4Hggpq75+N6+5b0GAcVBBQetAQ6XMykVqoJCbSwE1PA0I1guxDTjROqKMIGrY+Diiz6ds7mKZNTufgHRPnjLvb2/zXEjfRBDE+8zykpiFmmCYNTE2KRvalsJowUoTqaZOBfKCIOeW+hQo6kjXGxqZ6ja4ha4vLuLkE490Pe5N3ckn13qyBpPcLDei5ExWsMJhVM+evmusAq/HYxwnyfuLu0OcULVTQiwSuVivrG7S9Z6nG4KNMr2r9NQeoGQA07DAarO3cx5wxN3KLyeaOf3d7+w1M06S/5NlG4v5DS7sPGMwN/pMivVDNRohvXNndJe2PtCPrw0BODuRvgSaUd4I+CHbGdf8HaM2O3on4Kfo2NXlxBN2cxuchz+jcqsMQHcc1U8IyxmXu5wC9fHw8zPAvfz2WkcyHW9KN2Bz/MZeq1hjV95/u88FQ5Lmm3d2rMh97wzlOheh3piVW5NrgjRNWWx+V9KRaL7+RqIKbQ/+8u1H38lbGgG7uZYORdf5VDQp1Pho6rG2qFaf6WMi5fRlP1gpdqxJTVJikJyemKRNcyNFa/8LFzUq8ChTziKvjJWTFqekVzjEqUmdYZlcBtr4zTitrWE7FzlEUs8XRy2JtydJZ0FnKxc7sXLhKobhaEjSWVb2/JHdpqClqxYSx6Q+wOWzAZyq1Db4lq1UTCU9M13r2KuDUvBTa2beYFAtK4vkIijL8F3Kp4nCQV85AGdfY1GZvQOYXH1sViUm4pNKAxqodRbM9HBpMGoJnGdDlYV00vSNRi+CpNxRPXRPK42UR+ltWwaJxmaDc2XGRwyfAYEDDIojl6yoDaKhOj8tGQ1a9nD+ONTL9Ee00ZfPnRDEL/+YkdkU3W8P5u4Vqi5bpVamNd0IuX9mvfl/eqq8V6OtbWIzo9kVTbcor0PBM+4s3cNpMZ8/Azz89goZ8Rme9+UI4Sq1X4cdxjnj0z+Sm6cgmeZ2Zpulf4D23cw==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getCarclubCancellationinformationByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/carclub/cancellationinformation/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getCarclubCancellationinformationByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      