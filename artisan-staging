#!/bin/bash

# Artisan Staging - Run Laravel Artisan commands in Cloud Run Jobs (Staging Environment)
# This is a convenience wrapper that automatically sets ENVIRONMENT=staging

export ENVIRONMENT=staging

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Execute the main artisan-cloud script with staging environment
exec "$SCRIPT_DIR/artisan-cloud" "$@"
