---
id: get-admin-customers-byid
title: "getAdminCustomersByid"
description: "getAdminCustomersByid"
sidebar_label: "getAdminCustomersByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVVFv4zYM/isCnzZA13Qd9uK32203dBiGw9rtHoo8MDZjC5Uln0ilywL/94Gy4yTX9u5eEov6+PEjRVEHEGwZqgd4l1liT4nfNr0LsLYwYMKehJLuHyBgT1BBPeNuG7DgAlQwoHRgIdGn7BI1UEnKZIHrjnqE6gCyH9TTBaGWEljYxtSjTKYfb2Ac1+rOQwxMrB4319f61xDXyQ3iosa5y3VNzDCOFpjqnJzsi7INYaIE1cNaieJACdXltoEKWpKS0JLez3unyi+pX0P1JF2cacBOmVawwsGtUPGrYzV4dTgVZgQVmHbHyuXkoYJOZOBqtfKxRt9FluowxCTjMzF/KMBMBGBhh8nhxk+FUY+pMlvMXkv40/X1DzCOo/08zjYRd28a2r3BwV3hfznRE23YCfFVKOlchv2FduTj0FOQY/DXSDPKN5LeCbYutF8j9G5H38j4IcUm17pYSNenfrjTpptKdeyKpf80JMx9qesZYOeP98em/P3jfekxF7axuDvxin+vUs07TObth1s9GEo8adrdPJN53zlWnBlS3LmG2FBohuiCsNnGZKQj00T5k8QUXoX+c3NlbuU1jx7DVMsaE1v9NTUKtTE5Oq593hjOm0XIuX0T46MLLVuDoTGZKRnM0lEQV5f7UoKj9/GJyzYbiWagpLfVLLeKDee6MzhhTKLWsUxbhdjH1gVrvGM5iZ1QlNQS6Gm2NuTpDHTmcpGZn4SbPJSthgSdZ7PZXwYPud9QsoYJU90daRXW4yMVbX1syFtTJ8JTpJdq9gxwKl4emmlvNhkWlMzTEYSavP8Sc1FxPMgXzsC4UPvc6O6AzE8xNSYRk3BxpR6dNztKbns8NBgtDJGlx6DNOk/plsSUiWaWkWZemnzL5RD6V1aDRxeUsNzRwzzvHgAHBxbKxAO7vAAMFqqz52BtQceaOhwOG2T6O/lxVPOnTElH9fo0zcpgbBzrdwPVFj3TM211DEJB7+R3f82vy/cG7MuaZyOGfRmaPusKLDzS/vLZGtfj2WT/7dd7zS1rpks1PhsNReyL/IfDhLiPjxTGcQknutZI4/g/fiCcJA==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminCustomersByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/customers/{customerId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminCustomersByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      