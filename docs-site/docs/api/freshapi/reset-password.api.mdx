---
id: reset-password
title: "Reset password"
description: "Reset user password using token from email"
sidebar_label: "Reset password"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Reset password"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/auth/reset-password"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Reset user password using token from email

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["token","email","password","password_confirmation"],"properties":{"token":{"type":"string","description":"Password reset token from email"},"email":{"type":"string","format":"email","description":"User email address"},"password":{"type":"string","format":"password","minLength":8,"description":"New password"},"password_confirmation":{"type":"string","format":"password","description":"Password confirmation (must match password)"}},"title":"ResetPasswordRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Password reset successful","content":{"application/json":{"schema":{"type":"object","properties":{"status":{"type":"string","description":"Password reset status message"}},"title":"PasswordResetResponse"}}}},"422":{"description":"Invalid token or validation error","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Validation error message"},"errors":{"type":"object","additionalProperties":{"type":"array","items":{"type":"string"}},"description":"Field-specific validation errors"}},"title":"ValidationErrorResponse"}}}}}}
>
  
</StatusCodes>


      