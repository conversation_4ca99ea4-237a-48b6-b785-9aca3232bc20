---
id: get-admin-allbookings
title: "getAdminAllbookings"
description: "getAdminAllbookings"
sidebar_label: "getAdminAllbookings"
hide_title: true
hide_table_of_contents: true
api: eJylVU1v5DYM/SsCz95MmqIX39Jut0hRLBaYtD0Ec+DYHFuILKkiPems4f9eULbnIx+7AXoZj6jHR4qkngYQbBjKB7h17ucQHq1v+LburIdNARETdiSUFDGAx46ghF0ibteC0jMxFGA9lPBPT+kABXDVUodQDiCHqGhMCXXDCnV8ZmdJ1jcwjmNxZI7Y0Oe+21L6Lq31Qk3G7ULqUCbTjzfwnG9tv9L/YdsUkIhj8HrYcoCb62v91MRVslFsUOJ1X1XErMGBqeqTlUMu2ZYwUYLyYaNEIVJCdbmroYSGJFf61rntXHkonhG/julI2jBTgLZJWihhhdGuUNErvIAzpf3Swz45KKEViVyuVi5U6NrAUg4xJBlfxP9DAWYigAL2mCxu3VQJ9ZhKscPeac1+ur7+Ye7oZZw8Mh9q2n/AaK/wa5/oibZshfjK5zNchv1Ie3IhduRlCf4WaY/yTtK1YGN98z1CZ/f0TsYvKdR9pYsj6eY0AGudsqlUyxgcB05DLoOo6xlQzH8+LVP4+9/3eais34XsbsUp/pOman7BZG6/3GljKPGU0/7mRZr3rWXFmZjC3tbEhnwdg/XCZheSkZZMHeQzicm8Cv3r5srcyVseHfqplhUmLvTXVCjUhGRpWbt+a7jfHhM5ty/DWRj0temZksFeWvJiq3xBcnB0Ljxx3mYjwURKej3N8Rqx4b5qDU4Yk6ixLNNWJnahsb4wzrKckp1QpOpjPD3N1pocnYHOXC5O5qbETR/zVk2C1rHZHi6D+yxihWHCVLULrcI6fKScWxdqcoWpEuEp0ms1ewE4Fa+P9bQ3mwzPqqyl9hU59y3mnMXSyFd6YKyvXF/rbkTmp5Bqk4hJOLtSh9aZPSW7W5oGYwExsHTodVhnFW5ITBYx8w2lO94MoX9lFR1ar2z5gg6zwj0ARgsFZI3T7xndpgDVMQUNwxaZ/kxuHNU8ib5qX21ZxauGcoeOqYBHOrzynu3R9ZpJVog3fC5eqvc6zE/RCb45KWp+Is6U/bdf7/WEvZ77WJtnKpEPtTyz/nBGPQwT4j48kh9V1qcsRNcwbsZx/A+mrcxv
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminAllbookings"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/allbookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminAllbookings

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"freshStatuses","in":"query","schema":{"type":"array","items":{"type":"string"}}},{"name":"pageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"pageSize","in":"query","schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      