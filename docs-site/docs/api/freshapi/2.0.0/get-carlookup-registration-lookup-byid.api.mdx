---
id: get-carlookup-registration-lookup-byid
title: "getCarlookupRegistration-lookupByid"
description: "getCarlookupRegistration-lookupByid"
sidebar_label: "getCarlookupRegistration-lookupByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVU1vIzcM/SsCTy2gxGmAXubWpt0ixWKx2KTtIfCBnqFnhGgkLUk5dY3574Vmxh+JvW0u9kgk33ukKGoHiq1A9QR3yB9jfM4JlhYSMvakxMW0g4A9QQVMrRNlVBfDp9yviMGCC1BBQu3AAtPX7JgaqJQzWZC6ox6h2oFuU0EQZRdaGIZlcZYUg5AU++3NTflrSGp2qRBABQ+5rkkEhsGCUJ3Z6XbUsyJkYqielgUoJpo03TdQQUt6h+zHVL6cCL6atn7eugbsG6L3xfSkXZwpwE45V7DA5Bb1vngLPg9f7M4LN0BJiTf7Cmf2UEGnmqRaLHys0XdRtNqlyDqcCf5YHMwEABY2yA5XfipliZhqucbsFSr48ebmBxiGwb7lWTNJd9XQ5gqTu8Z/MtMLrcQpyXUYk3xN+wttyMfUU9A9+bdAM+o7QR8UWxfa/wP0bkPvRPzMscl1WRxAl8cOeihNOZVq30eH/iyUMPdtWc8Odv74ELnHUtHf/3ocu9KFdRzDnfri/6FINXfI5qfP9+VgiGXStLk9k/nYOSl+JnHcuIbEUGhSdEHFrCMb7cg0UT+RmhG3uP55e23u9VsRPYapljWy2PJralRqIzvar31eGcmrg5DT/VWMzy60Yg2GxmQhNpi1o6CuHpt3JEfv44uMZjEaTSJeR+7N4R6KkVx3Bicfc9r9I7CPrQvWeCd6FDt5UZkPJtDLvNuQpxOnk5BXmflJuMlpNDWk6LyY1fY1eRjvnjVCyHW3hy1uPT7TqK2PDXlraiY8Ml2q2ZnDsXg5NZNt3jKiqFmmIwg1ef9fyKOK/UFeOAPjQu1zU6wJRV4iN4ZJSGUMpR6dNxtit94fGgwWUhTtMZRmnad5S2oOU89cGHvm0qw8XBWlv3WRPLpQ4Mcbu5tn4hNgcmDhMBXHp+EMHyxUF56UpYUy+grMbrdCoT/YD0PZ/pqJywOwPE68cXg2Tsp3A9UavdCZ4joGpVDu7Xdf5hfqewP2cibzJobtOFh9Liuw8Ezby0/gsBxO3obffn0EC+XETmr1ZoyMoi/y7HaTx2N8pjAMB1ot68I0DP8Cl5y+sA==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getCarlookupRegistration-lookupByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/carLookup/registration-lookup/{registrationNumber}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getCarlookupRegistration-lookupByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"registrationNumber","in":"path","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      