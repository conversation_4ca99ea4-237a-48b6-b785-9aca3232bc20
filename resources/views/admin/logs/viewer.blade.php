<x-app-layout>
    <x-slot name="header">
        {{ __('Log Viewer') }}
    </x-slot>

    <div class="py-4">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Log Files</h1>
                    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">View and analyze application log files.</p>
                </div>
            </div>
            
            <div class="mt-4 bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <form method="GET" action="{{ route('admin.logs.viewer') }}" class="flex flex-col sm:flex-row gap-4">
                        <div class="w-full sm:w-1/3">
                            <label for="file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Log File</label>
                            <select id="file" name="file" onchange="this.form.submit()" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                @foreach($logFiles as $logFile)
                                    <option value="{{ $logFile['name'] }}" {{ $selectedLog === $logFile['name'] ? 'selected' : '' }}>
                                        {{ $logFile['name'] }} ({{ $logFile['size'] }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="w-full sm:w-1/4">
                            <label for="lines" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Lines to Display</label>
                            <select id="lines" name="lines" onchange="this.form.submit()" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                @foreach([50, 100, 250, 500, 1000] as $lineOption)
                                    <option value="{{ $lineOption }}" {{ $lines == $lineOption ? 'selected' : '' }}>
                                        {{ $lineOption }} lines
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                View Log
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="p-4">
                    <div class="flex justify-between mb-2">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ $selectedLog }}</h3>
                        <div class="flex space-x-2">
                            <div class="relative inline-block text-left mr-2">
                                <button type="button" id="auto-refresh-btn" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200 dark:text-purple-100 dark:bg-purple-900 dark:hover:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                    <span id="auto-refresh-text">Auto-refresh</span>
                                </button>
                                <div id="auto-refresh-dropdown" class="hidden origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                                    <div class="py-1" role="menu" aria-orientation="vertical">
                                        <button type="button" class="refresh-option block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-seconds="5">Every 5 seconds</button>
                                        <button type="button" class="refresh-option block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-seconds="10">Every 10 seconds</button>
                                        <button type="button" class="refresh-option block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-seconds="30">Every 30 seconds</button>
                                        <button type="button" class="refresh-option block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-seconds="60">Every minute</button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" id="stop-refresh-btn" class="hidden inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-100 dark:bg-red-900 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                Stop
                            </button>
                            <a href="{{ route('admin.logs.viewer', ['file' => $selectedLog, 'lines' => $lines]) }}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 dark:text-indigo-100 dark:bg-indigo-900 dark:hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Refresh
                            </a>
                            <a href="{{ route('admin.logs.download', ['file' => $selectedLog]) }}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 dark:text-green-100 dark:bg-green-900 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                Download
                            </a>
                        </div>
                    </div>
                    
                    <div id="log-container" class="bg-gray-100 dark:bg-gray-900 p-4 rounded-md" style="height: 70vh; overflow: auto;">
                        <pre id="log-content" class="text-xs text-gray-800 dark:text-gray-300 whitespace-pre" style="overflow-wrap: normal; word-break: keep-all;">{{ $content }}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const autoRefreshBtn = document.getElementById('auto-refresh-btn');
            const stopRefreshBtn = document.getElementById('stop-refresh-btn');
            const autoRefreshDropdown = document.getElementById('auto-refresh-dropdown');
            const autoRefreshText = document.getElementById('auto-refresh-text');
            const refreshOptions = document.querySelectorAll('.refresh-option');
            const logContainer = document.getElementById('log-container');
            
            let refreshInterval = null;
            let refreshSeconds = 0;
            
            // Toggle dropdown
            autoRefreshBtn.addEventListener('click', function() {
                autoRefreshDropdown.classList.toggle('hidden');
            });
            
            // Stop auto-refresh
            stopRefreshBtn.addEventListener('click', function() {
                setAutoRefresh(0);
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!autoRefreshBtn.contains(event.target) && !autoRefreshDropdown.contains(event.target)) {
                    autoRefreshDropdown.classList.add('hidden');
                }
            });
            
            // Handle refresh option selection
            refreshOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const seconds = parseInt(this.getAttribute('data-seconds'));
                    setAutoRefresh(seconds);
                    autoRefreshDropdown.classList.add('hidden');
                });
            });
            
            function setAutoRefresh(seconds) {
                // Clear existing interval
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = null;
                }
                
                refreshSeconds = seconds;
                
                // Update UI based on auto-refresh state
                if (seconds > 0) {
                    // Enable auto-refresh
                    autoRefreshText.textContent = `Auto (${seconds}s)`;
                    autoRefreshBtn.classList.add('bg-purple-500', 'text-white', 'dark:bg-purple-600');
                    autoRefreshBtn.classList.remove('bg-purple-100', 'text-purple-700', 'dark:bg-purple-900', 'dark:text-purple-100');
                    
                    // Show stop button
                    stopRefreshBtn.classList.remove('hidden');
                    
                    // Set new interval
                    refreshInterval = setInterval(function() {
                        refreshLogContent();
                    }, seconds * 1000);
                    
                    // Do an immediate refresh
                    refreshLogContent();
                } else {
                    // Disable auto-refresh
                    autoRefreshText.textContent = 'Auto-refresh';
                    autoRefreshBtn.classList.remove('bg-purple-500', 'text-white', 'dark:bg-purple-600');
                    autoRefreshBtn.classList.add('bg-purple-100', 'text-purple-700', 'dark:bg-purple-900', 'dark:text-purple-100');
                    
                    // Hide stop button
                    stopRefreshBtn.classList.add('hidden');
                }
            }
            
            function refreshLogContent() {
                const file = '{{ $selectedLog }}';
                const lines = '{{ $lines }}';
                
                fetch(`{{ route('admin.logs.content') }}?file=${file}&lines=${lines}`)
                    .then(response => response.text())
                    .then(content => {
                        const logContent = document.getElementById('log-content');
                        logContent.textContent = content;
                        
                        // Scroll to bottom
                        logContainer.scrollTop = logContainer.scrollHeight;
                    })
                    .catch(error => {
                        console.error('Error fetching log content:', error);
                    });
            }
        });
    </script>
</x-app-layout>
