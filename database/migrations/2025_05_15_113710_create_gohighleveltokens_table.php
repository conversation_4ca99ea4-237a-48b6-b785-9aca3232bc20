<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('gohighlevel_tokens', function (Blueprint $table) {
            $table->id();
            $table->jsonb('data');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('gohighlevel_tokens');
    }
};
