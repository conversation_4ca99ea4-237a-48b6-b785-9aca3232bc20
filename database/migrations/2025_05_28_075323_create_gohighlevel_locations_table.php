<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('gohighlevel_locations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('location_id');
            $table->string('client_id');
            $table->string('client_secret');
            $table->string('base_url_authorization');
            $table->string('base_url_api');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('gohighlevel_locations');
    }
};
