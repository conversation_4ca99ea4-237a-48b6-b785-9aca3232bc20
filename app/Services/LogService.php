<?php

namespace App\Services;

use App\Services\Contracts\LogServiceInterface;
use App\Services\MongoDBAtlasService;
use Illuminate\Support\Facades\Log;
use Exception;

class LogService implements LogServiceInterface
{
    public function __construct(
        private readonly MongoDBAtlasService $mongoDBAtlasService
    ) {
    }

    /**
     * Store a log entry
     *
     * @param string $identifier
     * @param array $payload
     * @param array $metadata
     * @return array|null
     */
    public function storeLog(string $identifier, array $payload, array $metadata = []): ?array
    {
        try {
            // Add additional metadata
            $enrichedMetadata = array_merge($metadata, [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'request_id' => request()->header('X-Request-ID'),
            ]);

            return $this->mongoDBAtlasService->storeLog($identifier, $payload, $enrichedMetadata);
        } catch (Exception $e) {
            Log::error('Failed to store log', [
                'identifier' => $identifier,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Retrieve logs by identifier
     *
     * @param string $identifier
     * @param array $options
     * @return array|null
     */
    public function getLogsByIdentifier(string $identifier, array $options = []): ?array
    {
        try {
            $filter = ['identifier' => $identifier];
            return $this->mongoDBAtlasService->find('api_logs', $filter, $options);
        } catch (Exception $e) {
            Log::error('Failed to retrieve logs by identifier', [
                'identifier' => $identifier,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Retrieve logs with filters
     *
     * @param array $filters
     * @param array $options
     * @return array|null
     */
    public function getLogs(array $filters = [], array $options = []): ?array
    {
        try {
            return $this->mongoDBAtlasService->find('api_logs', $filters, $options);
        } catch (Exception $e) {
            Log::error('Failed to retrieve logs', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
