---
id: update-bookings-byid
title: "updateBookingsByid"
description: "updateBookingsByid"
sidebar_label: "updateBookingsByid"
hide_title: true
hide_table_of_contents: true
api: eJzNVk1zGzcM/SscnPqxtlx3etlbbCdTdzqtJ1Hbg0cHaBfSMuaSDAnKUXf2v2dAriQrUtocc5GWIPAAgo+PHIBxHaF+hBvnnrRdw6ICjwF7YgoyMYDFnqCGZXG4b6ECbaEGj9xBBYE+JB2ohZpDogpi01GPUA/AWy+B2jKtKUAFKxd65GL6+RrGcVHCKfKNa7cS0zjLZFk+0XujG2Tt7Ox9dFZsJ+Bu+Z4ahgp8cJ4Ca4oyq+MdGWKpau+6dM4QWhjHCrBttQCjeXgRt0ITqQLWbCTgnVtxgXltWfP2bv5nDmb6yN9WRS9b9cOP31BpEh4oemdjyXV9dSV/LcUmaC+oEpuahmLMySI1KWjeZuotCQMFqB8XQhXJndd430INybfINNE23my1EPMY96xLT9y5tvC3EQJnHtcwQ69nE8njbNjTfQQpKmx2xyEFAzV0zD7Ws5lxDZrORa4H7wKPJzX8Lg6qAEAFGwwal6Y0QyJKN1aYjByMX66ufpK2VZ/nWQWK3UVLmwv0+hL/TYGeaRk1U7y0xCdp72hDxvmeLO+Sfwk0IX8l6DvGtbbr/wM0ekNfifgQXJsaGexBFwcOvBMCl1btmLAnqKSESW0yYYtDNX282UnNb//MM6/kULw9aM3rj9h7Ie4R/UXBRpG3lcupJm6/kWWpWwzq1cO9bCKFWOrfXJ8sad7pKH7KB7fRLUVFtvVOW45q5YLijlTr+A9ilXHF9e/rS3XPX4ro0Za+NxhiJb+qQaa1C5p2Y5OWKqblvpCX9h2pK4W2VSlSUJi4I8uTZuTkaIx7jnk6KnbKUxC9VvtTF1VMTaew+KhAax25TGVg49baVsroyIdiixcFsVh6nqytNPzg9CLkaGWmFK6Sz1MtMWoT1XJ7nNymfkmhUpEwNN0OVtx6fKJcW+9aMpVqAuEh07menTgcmpfVROYmk4qMnGLZAtuQMf+FnKvYbeSZPVDaNia1MusxxmcXWhUoEsccSj1qozYU9Gq3aTBW4F3kHrPaT9d00Ty1Ez11Thj3hyjfZN6gtgKWz/Iw6eEjoNdynCYgqKA+PAEWFYjoidswLDHSX8GMo5g/JAoi3ouD1mXZbHWU73Z/cXxW0f7ih+/eTi+K7xVU5yudjGi3WVJNkhFU8ETbo5fKuBgr6AjbfIkM0/xtSXUxF5RD/Ml7Q6Rof1s8vJrf/pr7UR4qwiioIeCzvIHwuRTgynaLQ7YNYNCuE67Ft4DmGzVJiw+X7bF25VLPLnEYisfcPZEdx/2KWcay2nH8BGX5dyY=
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateBookingsByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/bookings/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateBookingsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"text/json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"isDeleted":{"type":"boolean"}},"additionalProperties":false,"title":"SoftDeleteEntityDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      