---
id: list-all-cars-for-authenticated-user
title: "List user cars"
description: "This endpoint allows you to retrieve a list of all cars associated with the authenticated user. The cars are returned in an array format."
sidebar_label: "List user cars"
hide_title: true
hide_table_of_contents: true
api: eJzNVktz2zYQ/is7yCHtDEXJzqMtb65jZ5RxE0/kNAfLk0DkisQYBJAFIFvV8L93FqRsyUqbHquDJHKf2P3w7W5EkLUXxbU4lSRuMmEdkgzKmmklCqGVDydan0ry55ZOYmjQBFXKgNUnjyQyUaEvSTm2EIW4apQHNJWzygSQWts7D2sbIVggDKRwhSCB3YJdsgKUkjxI722p2C3cqdBAaBDkbjSIHimHqwYHA0J2GMlgBcqANCCJ5BqWlloZcpEJQu+s8ehFsRHHkwn/7Gd7MaSRPG7Tq8DHskTvl1Hrtegy8XJydGj7yXB+ltRfWMEIdkqjrAHCb1ERVqLrMuGxjKTCWhTXG7FASUiiuL7pbjLRYmgsF7rGIDLhZGhEIcarF2Pp1JjzEmxPKySfzCNpUYgmBOeL8VjbUurG+lBsnKXQHfTjghWgdyAysZKk5EL3JWGL/lxLGXUQhTiaHE1E13XZ0zgVrlBb16IJ+ZLQN6WkvLR5vD2I+OZRdRv30J90asRCVeLoaPLbr69evz5++fKXF5McI1mHozv04TinaHLp3PggyCzIWpn6XwP8KNFLslUsU7e2bm4eezUrG2z7Om07thFh7XAIwm1JKqLYKmTDn/OEQFGId5+vDqK++3yVsMLA6aHSoKyQIHo+EOP+9+QFevc5nN3L1mks4PmeXbHV2wR7i6Z7nqCmzNKmTFXQnNo51wBOJcHJ5ZQBgOT7RF7kk3wiMnE/0rZONn0Fx6qtx9vSJVnuTM2Hk+VtTTaa6tRqS6IQz87TR2RC6nCF9+EgYPf0+F8SQZxcTqGRHhaIBgZsYdVzhNOyxFSHyob3GCB5ZIs/jzOQpgLlwdgAawx88aPHChbrZFFqxbCTzvn8y9zMzbNnMIttK2k9Nw+RHdmVqvCRpzyTBrTS9KDiW5fxNzDz1JYUbp91XMyNj4uHI+0IYGHtrTK177NkvtqlMGVNDtMHTmSxn5tgwSExZ8ED8XomoAZkrwSEtfKhFyXP2tbKZIlDH9Kdm14NiV8ZvBtOUaHGR61dm73D6T51iI5Fc1NhkEp7ruteeBPbBVIGHiWVzdYvq7XyFlN2ra1QZ1ASSg41Nw/1eVq3QeN7BYyu6mXDq6GXp2S9H30gVSsDH9HbSCXCrJHp1D+dfvg4+3mn0T465jj/n+wSBsqkWNlWqp7D0QefD9H3KX5uTrTegdDA+E9aPlzrC0lyhRpm0pQhtjlMTalj1eO8Zw1I15gRze++RxHF3Hz9+nVu/oEG1jbSqOeCQVFkorQmyDLRvJGJrHiCXqi6CYYTe0s2Mpfts+fd3V0eGtRbNb72bqDRLhNalWg87jj9Y3p14MQ6NH2lc0v1eDDyY9btMhGQWv9hOeunwI5drUITF3lp2/GS2QZJmuohlR1qWrhRaQnHC20XY+7Y+GJ6evZ+dpZYjWTAkVatCoezm8HBckhyrkPaWpaWyp6G0PhICMOIAh/kQmkV1gnhS6mYrmWNeaL3YXpuBLsUxetJJhzPC9EqEwMyda5FIaaXIKuK0Huw1F/t6Ru4a9DsbzqpwnzFH12++qFHzgtbqTSUtl0ok7CRRrlw1odWmp1upbUnJTAsGHvV2STQoAn/s3VuGL4B78PYaakMFypBbjPsTddi9YKHkVOMfD7aTSZ4O2LRZrOQHj+R7jp+/S0i8UJ287gUpZ1sZyV7e8ag5rR3Zv+TWZ92skEkzTqtWDry02bTa1ylG8mb2S1y09INFd1N13V/A69SC0Q=
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"List user cars"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/v3/api/cars"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint allows you to retrieve a list of all cars associated with the authenticated user. The cars are returned in an array format.

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"List of cars retrieved successfully"},"401":{"description":"Unauthorized - Authentication required"}}}
>
  
</StatusCodes>


      