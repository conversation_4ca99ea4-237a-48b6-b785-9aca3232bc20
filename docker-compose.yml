services:
    api:
        build:
            context: .
            dockerfile: dockerfiles/Api.Dockerfile
        container_name: api
        depends_on:
            - pg
        volumes:
            - .:/var/www/html
        restart: always
        ports:
            - "80:80"
        networks:
            freshcar-network:
                ipv4_address: *********
        extra_hosts:
            - host.docker.internal:host-gateway

    pg:
        image: postgres:15
        container_name: pg
        volumes:
            - pg-disk:/var/lib/postgres
        restart: always
        environment:
            POSTGRES_PASSWORD: password
            POSTGRES_DB: testing
        ports:
            - "5444:5432"
        networks:
            freshcar-network:
                ipv4_address: *********

    mailpit:
        image: "axllent/mailpit:latest"
        container_name: mailpit
        restart: always
        ports:
            - "1011:8025"
        networks:
            freshcar-network:
                ipv4_address: *********

    ql:
        build:
            context: .
            dockerfile: dockerfiles/QueueListener.Dockerfile
        container_name: ql
        depends_on:
            - api
        volumes:
            - .:/var/www/html
        restart: unless-stopped
        networks:
            freshcar-network:
                ipv4_address: *********

# On-disk storage of DB data, when containers are stopped
volumes:
    pg-disk: { name: pg-disk }
    elasticsearch-disk: { name: elasticsearch-disk }

# Local network for services running using this docker-compose config
networks:
    freshcar-network:
        ipam:
            driver: default
            config:
                - subnet: *********/16
