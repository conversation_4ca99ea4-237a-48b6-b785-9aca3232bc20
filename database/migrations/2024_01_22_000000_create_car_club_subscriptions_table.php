<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('car_club_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('car_id')->constrained()->onDelete('cascade');
            $table->string('stripe_subscription_id')->unique()->nullable();
            $table->string('stripe_customer_id')->nullable();
            $table->string('stripe_price_id')->nullable();
            $table->string('status')->default('active'); // active, paused, cancelled, expired
            $table->integer('frequency'); // frequency in weeks
            $table->integer('cleans'); // number of cleans per frequency period
            $table->integer('resource_id');
            $table->string('resource_name')->nullable();
            $table->string('category')->nullable();
            $table->integer('package_group_id');
            $table->string('customer_name')->nullable();
            $table->decimal('amount', 10, 2)->nullable(); // subscription amount
            $table->string('currency', 3)->default('GBP');
            $table->integer('cancellation_reason_id')->nullable();
            $table->text('cancellation_reason_notes')->nullable();
            $table->text('pause_reason')->nullable();
            $table->timestamp('pause_until')->nullable();
            $table->timestamp('paused_at')->nullable();
            $table->text('resume_reason')->nullable();
            $table->timestamp('resume_date')->nullable();
            $table->timestamp('resumed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index(['user_id', 'status']);
            $table->index('stripe_subscription_id');
            $table->index('car_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('car_club_subscriptions');
    }
};
