---
id: logout
title: "Logout user"
description: "Logout the authenticated user and invalidate the token"
sidebar_label: "Logout user"
hide_title: true
hide_table_of_contents: true
api: eJytVltv2zYU/isE+9ANkCUnbbNNb1maDi6yJqjT9SEOWoo6lthQJMtLHM/Qfx8OKd/idC2G5cGRdO6Hh9/5VtSzxtHyhp4G34LygjMvtKK3Ga3BcStMfC3phW508MS3QNhWFWoSHFjCVE2EumdS1MxD1PL6DhTNqDZgo89JTUsqoxuaUQvOaOXA0XJFj8dj/PdkRBc4B+fmQdKMcq08KI/KzBg5ZFt8cWixoo630DF88ksDtKS6+gIcwxmLeXiR4tXMP6m1n8D7IUUS1fuMduAca2DH0nkrVHNgOU05k7VB32fUCy9hK1s7p32P0pfjo8MOfFB7rf7/6v/hQs6t1XZbRva4c98wO61rgY9MEogeollGVZCSVdgGbwPsdiUG2utJn1EHPFjhl7S8WdEKmAVLy5vb/haPwrcaB8poF+tjvqUlLbBhxWbIHNh7sC7aBytpSVvvjSuLQmrOZKudL1dGW98flHCBCiQ5oBm9Z1Zg5rF9aJFOa86C9LSkR+OjMWadPY5Twz1IbTpQPp9bcC1nNuc6D3cHEV9vVddxD/0xI0YoFBxGR+Pffn11cnL88uUvL8Y5BDzi0QKcP85tUDkzpjicTM8aoZp/DfC9RK+srgPHl42b2+1hTXEGU5/WR7YZFwyCxxJVaLlWyIaHN9p2DNv59uP1QdS3H68JgpS24u849aQFVoMlwWFBCDm/Ry8kuc/J+QPrjISSPN+zK9d6qwhR/fN4PYWa65jpMJBvsAfkjFlyejXBAQDrUiIv8nE+phl9GOGcoU3qYCG6pli3LspyE+9GxfhdY3VQ9ZmW2tKSPnsT/2hGmfTX8OAPAvaPy/903QqHItIyRyoARYbZgpp4TSwYyXiC3lr7d+BJ9IgWfx1nCaEdUdqTJXgiFAJ3TapltOBS4NgxY1z+aaZm6tkzMg1dx+xypjaRjdX3ogZHQNVGC+UdmSM8MJWGijPrMvwliFeNtgLW7zJUM+VCtSlpR0Aqre+EalzKMi2UvXWUk4knTEq9cFHsZsprYsDOte3IZsM4XBYtYUmJWGiE80kUPUvdCJURKZzfpDtTSQ0QyIiCxVBFDRK2Wrs2e8XJlDoJBkUzVYNnQjrs6154FboKbEYcMMvbtV9U69gdxOw6XYPMCLfAMNRMbfrzuG+DxlMNDKZOsuHTcJZnVjs3urSiEYq8B6eD5UCmLYtV/3R2+X76885Bu2AQ49wP2cUZ4FGx1h0Tilj4GsB5lw/R97nFTJ1KuTNCqC0sPDry4VpfMMvuQZIpU9yHLicTxWWo05wn1EhMAycavz0FEeVMff78eaa+AQNLHewoYcGgOGxaxiPMKxbB6roFciGa1itM7A+rA2LZPnouFovctyDXanjtzQCjfUal4ID7bev0z8n1gRNtQKVO59o2xWDkCtTFlQm2c5fzadoCO3aN8G2ocq67Yo5oA5apepPKDjRVZsS1haKSuirwxIqLydn5u+l5RDXLPIyk6IQ/ZCQ4HCgnUY59EIgGc215giFQLlggw4oizrNKSOGXccLnTCBcswbyCO/D9lxRdEnLk3FGDe4L2gkVPCB0LmlJJ1eE1bVFQqVtutqT12TRgtqnorHDeMW3Ll991yPmBR0TknDdVUIl+hvpB1KLjqmd0xooKabweD+tttzsv3PlYU16ePCFkUwoLCkOx2pgODcUPdJsTaRvM4osBgWrVcUcfLCy7/Hz1wAWmdPtlrxE8pTRdCkiKboDbMcp52B8ZDkyYAIH3HKPcl1dTnFoYyLb3f5ol0f3g4ip5Y7z1SppXMcbh8wrZZGa0N/2ff8PeZhutQ==
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Logout user"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/auth/logout"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Logout the authenticated user and invalidate the token

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Logout successful","content":{"application/json":{"schema":{"type":"object","properties":{"data":{"type":"object","description":"Response data"},"message":{"type":"string","description":"Success message"}},"title":"SuccessResponse"}}}},"401":{"description":"Unauthenticated","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}}}}
>
  
</StatusCodes>


      