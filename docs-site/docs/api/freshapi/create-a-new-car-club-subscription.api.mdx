---
id: create-a-new-car-club-subscription
title: "Create a new car club subscription"
description: "Create a new car club subscription"
sidebar_label: "Create a new car club subscription"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create a new car club subscription"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/v3/api/car-club/subscriptions"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create a new car club subscription

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"customerCarID":{"type":"integer","format":"int32"},"frequency":{"type":"integer","format":"int32"},"cleans":{"type":"integer","format":"int32"},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"customerName":{"type":"string","nullable":true},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CarClubRegisterDTO"}},"text/json":{"schema":{"type":"object","properties":{"customerCarID":{"type":"integer","format":"int32"},"frequency":{"type":"integer","format":"int32"},"cleans":{"type":"integer","format":"int32"},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"customerName":{"type":"string","nullable":true},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CarClubRegisterDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"customerCarID":{"type":"integer","format":"int32"},"frequency":{"type":"integer","format":"int32"},"cleans":{"type":"integer","format":"int32"},"resourceId":{"type":"integer","format":"int32"},"resourceName":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"packageGroupId":{"type":"integer","format":"int32"},"customerName":{"type":"string","nullable":true},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CarClubRegisterDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"201":{"description":"Subscription created successfully"}}}
>
  
</StatusCodes>


      