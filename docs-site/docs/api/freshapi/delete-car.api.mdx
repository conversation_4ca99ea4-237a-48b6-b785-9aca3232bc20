---
id: delete-car
title: "Delete a car"
description: "This endpoint allows you to delete a specific car associated with the authenticated user. The car is identified by its unique ID. This is a soft delete"
sidebar_label: "Delete a car"
hide_title: true
hide_table_of_contents: true
api: eJzNVktz2zYQ/isY5JBkhiLlR9KWN9d2Oso4iSdSmkPkSUBgRWIMAggellUN/3tnQephy0l6rC8Wse/Fh293TQOrPS2/0HPm6E1GjQXHgjR6ImhJBSgIgKKMCvDcSYsyWtJZIz0BLayROhCmlFl6sjKRBEN6K8KIt8DlQnLCmSPMe8MlCyDIUoaGhAYIi6EBHSRPx9GDy8msgaQvPZEChQsJglQrIoMnUcvvEcjkAvVQw2MYswhDUJpRyxxrIYDDstZUsxZoSaWgGZWYuWWhoRl18D1KB4KWwUXIqOcNtIyWaxpWNlnoADVg5QvjWhb6o5Nj2nU3aO6t0R48WhyPx/jvYYfOmRuSEsRHzsH7RVRqRbuMno6PDg0+aWyHcfIfEGREzna9kUaTbb7J/PTpeNoEsjBRC9p1GfXAo5NhlfpQAXPgaPnlBtNvITRmd8OpbaGhJS3uTgpmZcGZ88Vaio6iH3e3aWd0ipa0CcH6siiU4Uw1xodybY0L3QFMrlCB9A5oRu+Yk6xSfdfQoi9jwaLC/h6Nj8a067rscRwBd6CMbUGHfOHAN5y5nJs83h5EvNipbuIe+mNWjlAoOYyOxn/8/ur16+PT099OxjlEZyyMluDDce6izpm1xUGQaWC11PVPA/wq0WtnROTpbjdubnZ3NkU49n3a3NwWmRiEDojF70EhG3682cD17efZQdS3n2cJWQizHlgNMAGORI8F4aP8M3khvfucXN6z1iooyfMHduVGbx3MLejueYKc1AuTMpVBYWpvsAcEoXl2PUEAgPN9Iif5OB/TjN6PlKmTTd/BQrZ1sWldkuVW11gc47e1Q3CfG2UcLemzN+mPZpSpMIP7cBCwe1z+18QaZ9cT0jBPKgBNBmyBQOpyYBXjkPogTHgPgSSPaPH3cUaYFsg5+M5WEIjUSFqJndCCK4mwY9b6/Otcz/WzZ2Qa25a51VxvI1tn7qSAHX16sjCOtEz3oMKnlyUKRFqsjZOw+Vaxmmsfq21JewJSGXMrde37LJFM9/lVGp2TyZaqUeznOhhiwSHBkS3ze6SrhrBeiTiopQ+9KHlWppY6I0r6sE13rns1cHikYTlUkehlV9SezYPiVJ86iRZFcy0gMKk89vVBeB3bClxGPDDHm41fVGvZLaTsWiNAZYQ7YBhqrrf9edy3QeOpBkYretlwNNzluTPejz44WUtNPoI30XEg04alql+cf/g4fbl30T5a5Dj/n+wSBnhSFKZlsmd88MHnQ/SHA2Guz5Tag9AwHx5d+fCsr5hjd6DIlGkeYpuTieYqih7nPWuQ9IwR0Xj2FEWUc/3t27e5/gENrEx0o54LBkWaUW50YDzR/DCIcbxfyboJGhP7y5mIXPaQPZfLZR4aUBs1fPZ2oNEuo0py0B72nL6bzA6cGAu673RuXF0MRr5A3S6jAVzrPyym/RTYs6tlaGKVc9MWaZSCY1psU9mjpsqOuHFQVMpUBd5YcTU5v3w/vUys5liAkZKtDIejGsGBcpLk2Ie0TC2M4z0NgfbRARlGFPGBVVLJsEoIXzCJdM1qyBO9D9NzTdElLV+PM2pxXtBW6phme7WiJZ1cEyaEA++Jcf3TnlyQZQP64RqWOoxPfOfy1S89Yl7QMqkIN20ldcJGGuXUGh9apvdu62KzH/LDxXKdIAM6/K93zGEQB7gPhVVMamxagt96WKS+0LsTHExW4itgztOMllLgio37Eiqs1xXz8MmprsPj7xEcrmo3uzUpbVxCevwtaLlgysNP+vXi47AjviQ/ynE4ZHqVtjEV8Ytm9BZW/ZLc3XR72+HF5dXlDPGMTdzbQB5tHCnPJ12v173GLPFCt42UeAKDdd2/05debQ==
sidebar_class_name: "delete api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete a car"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/v3/api/cars/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint allows you to delete a specific car associated with the authenticated user. The car is identified by its unique ID. This is a soft delete

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Car deleted successfully"},"401":{"description":"Unauthorized - Authentication required"},"404":{"description":"Car not found"}}}
>
  
</StatusCodes>


      