---
id: get-admin-customers
title: "getAdminCustomers"
description: "getAdminCustomers"
sidebar_label: "getAdminCustomers"
hide_title: true
hide_table_of_contents: true
api: eJylVU2P4zYM/SsCz97JdIpefNtuu8UUxWKBSdvDIAfGZmxhZEkVqUyzgf97QTnOx3x0B7uXOBIfHymSetqDYMdQ38OHzBIGSvy+HayHVQUREw4klNS+B48DQQ1MmJp+SWmACqyHGv7JlHZQATc9DQj1HmQXC1SS9R2MY3XyDkl+3n2LZ8SOPuVhTemr3tYLdQW3CWlAmbZ+vHnGd2e/0PewrSpIxDF4JlaPm+tr/bTETbJRbFDiu9w0xKzBganJycquFHRNmChBfb9Soh<PERSON><PERSON>brctlBDR1L6cOwKVE9oX0IMJH04uIM2UHqoYYHRLlCxi+YMzJS2c29zclBDLxK5XixcaND1gaXex5BkfBb7DwWYiQAq2GKyuHZTDdRjKsIGs9Nq/XR9/QOMpfaXcTaJuH/X0vYdRnuFX3KiR1qzFeIrX05wGfYX2pILcSAvc/DXSDPKG0nvBDvru68ROrulNzJ+TqHNjS6OpKtT6+90vqZSzQNwHDUNOY+grg+A6vDn4zx/v/+9LONk/SYUdytO8R81VfMBk3n/+VYbQ4mnnLY3z9Jc9pYVZ2IKW9sSG/JtDNYLm01IRnoybZBPJKbwKvSvmytzK695DOinWjaYuNJf06BQF5Klee3y2nBeHxM531+H8GB9x5VB35rMlAxm6cmLbcrVKMHRufDIxcxGgomU9GKa4wViw7npDU4Yk6izLJOpELvQWV8ZZ1lOyU4oUtkxnh4Puy05OgOduVyczE2JmxyLqSVB69isd5fBfZGvykwSOtMqbMAHKrkNoSVXmSYRniK9VLNngFPxcmwn22HLsKBknlrgG3Lu/5hLFnMjX+iBsb5xuVVrRObHkFqTiEm4uNKA1pktJbuZmwZjBTGwDOh1WA/625GYImDmVY073guhf2URHVqvXOV67g/qdg8YLVRQ9A0qOCncqgLVMIXs92tk+jO5cdTtSepV91rLKlwt1Bt0TBU80O7pG7dFlzWJIg2vOczP2lvAFy/ZWx0OT9UJvjrpbnlCztT/t1+XWpGs9TnW8ImWlOMfTOjPU9/vJ8QyPJAfVfynLETXMK7GcfwP+XjfGQ==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminCustomers"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/customers"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminCustomers

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"searchTerm","in":"query","schema":{"type":"string"}},{"name":"sortBy","in":"query","schema":{"type":"string"}},{"name":"pageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"pageSize","in":"query","schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      