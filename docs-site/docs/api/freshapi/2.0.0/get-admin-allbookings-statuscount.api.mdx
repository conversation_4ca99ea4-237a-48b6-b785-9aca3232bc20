---
id: get-admin-allbookings-statuscount
title: "getAdminAllbookingsStatuscount"
description: "getAdminAllbookingsStatuscount"
sidebar_label: "getAdminAllbookingsStatuscount"
hide_title: true
hide_table_of_contents: true
api: eJyNVEtv5DYM/isCz95MGqAX39Jtt0hRLBZItj0Ec6Btji1ElrQkNdmp4f9eSPY8NtnXxbbEjx9pPr4JFHuB+hFunfsthCfre7ntRuthWwGTxOCFBOoJbq6v86sjadlGtcFDDfepbUkE5rkCoTax1QPUjxM0hEwM9eN23lYQIjFml7sOauhJS4hb55o15L2iJmlD8grVixg/hI+kQ1iJoYKIOkANG4x2g9lxg2fPjRTXt6urEO+JpaSc2EENg2qUerNxoUU3BNF6ioF1fpXW3xlgFgKoYI9ssXFLrbLHUqwdJqdQw6/X17/APM/Vyzg7JhnedLR/g9Fe4X+J6ZkasUpy5el1NX6nPbkQR/J6DP4t0oT6k6T3ir31/Y8Ind3TTzJ+4NClNh9OpNvziNy3A41LqY6DMoEeIq0hc2cKBOojoFo/3gUeMVf0r38fythZvwvF3arL+Hc5VfMW2dx+uMuNIZYlp/3NqzQfBisZZyKHve1IDPkuButVzC6w0YFMF/Q9qSm8GfrPzZW50295jOiXWrbIUuWnaVGpD2zpeHapMZKaUyKX98dBrQz6ziQhNph0IK+2LStUgqNz4VmKWYwGE4l3gUdzWjQxktrB4IIxTL0VXUyF2IXe+so4K3pOdkER5xtPz+ttR44uQBcuX/yZWxI3KRZTR4rWiWkOXwb3aWyIKyOE3A5H2gwb8YlKbmPoyFWmZcJzpK/V7BXgXLwUu8W2Xpll75cW+Jac+x5zyeLYyK/0wFjfutRla0SR58CdYRJSKa40onVmT2x3x6bBXEEMoiP6PKwey2z3pKZom7kQN/MdMTxtidJn3USH1mfmsqzTqnyPgNFCBUX78vtMnffqQv+2FWSFyy7T1KDQR3bznK8/JeIs5NuzsBUtvxDbP/94yOQphzyl9WJZi66uJvSHIpMu5dM0LYiH8ER+zur6RIf8X/kM83ae5/8BeENcBQ==
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getAdminAllbookingsStatuscount"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/admin/allbookings/statusCount"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getAdminAllbookingsStatuscount

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      