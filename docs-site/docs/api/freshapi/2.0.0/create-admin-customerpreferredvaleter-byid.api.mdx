---
id: create-admin-customerpreferredvaleter-byid
title: "createAdminCustomerpreferredvaleterByid"
description: "createAdminCustomerpreferredvaleterByid"
sidebar_label: "createAdminCustomerpreferredvaleterByid"
hide_title: true
hide_table_of_contents: true
api: eJzlVktvGzcQ/ivEnPpYW46LXvbmOA3gIkiMWE0Phg6j5WiXMZdk+JCiLva/F8N9SJbt1vdcpOVw3sP5ZjqIWAco7+E6hWhb8reeNuQ9yS+oKZK/kq0ysCrAoceWKczegcGWoIRqFLuRUIAyUILD2EABnr4l5UlCGX2iAkLVUItQdhD3jiWViVSThwI21rcYB9Jvl9D3q0GcQnxr5Z5lKmsimcif6JxWFUZlzeJrsIZpB+UHs/fgKdjkK7p5l/0ZDh/Z71UxeWHXX6mKUIDz1pGPisKgZhZ9jccn6ssOWmU+kKljA+Wb2ViIXpka+r4AlFJxCKhvjwxvUAcqIKqomf9KytNyvFt+yvKRvscfOPzjR/DLrz9qHvrBorMmDP5eXlzwn6RQeeVYMZRwl6qKQsj2AlXJq7jPLbwm9OShvF9xx7H5nNAbyX3tCSPl5p+QwU0+bAcf3u4VN/1jY6+Xayk2lk05G3LmGTdKWKBTC2T5RfWCgkV3QJ0eOCi/nWApeQ0lNDG6UC4W2laoGxti2TnrY//E3Q/MIAYFUMAWvcK1HpLJEkM2N5g0l/n3i4s3nPbi1M7GU2jOJG3P0Klz/Cd52tE6qEjh3FB8YvYdbUlb15KJk/GXlCaMr1R6F7FWpv4/hVpt6ZUab72VqeLDrHR1eEN33G1DqqaXNPcIm4QR9Pk8MhTjx/upcf78e5nfJXfw5wPk//EdW6fptAUvThtsbiWePRubHRib5j0HK67Ri6vbGy4t+TBEtb18EuiyUYH5hPN2qyQFQUY6q0wMYmO9iA0JaeNHiiLrZdYvl+fiJr4k0aIZqlGhDwX/igoj1dYrms46rUVI69mRY/ra2gdl6lAINFKkQF5gig2ZOMJeNo5a213I10FEKxx5xiQx93IQIVWNwIFHeKpViMNVVqxtrUwhtArx4OzARZxZYWg3UiVpOmI6EnkUmR4cF8nlK0kRlQ5ivX9s3KR2Tb4QgdBXzaSW2Vp8oOxbayXpQmREmS09l7MnDIfkJSeHu5EkQsSYwlACU5HW/6U5ezEV8pkaCGUqnSTfOgxhZ70UngLFkEWpRaXFlrzaTEXjl8p412IeWNMSlUFTZNQUL8GmeA5v54bL+4DTqAybyH3fjZB6D+gU8KzhTa6Al2AVCiiP1rlVAYycLN91awz0l9d9z+RviTxPkNUBMDP2ShX4W84D7MTVeYmDnz6P8/lnAcXzIYxENPuMyzrxCQp4oP3jtbNf9QU0hDKPsm5kuB5snS1ZzUHBk+WRAW2eRLef7paMUePSyQ8QSvC448UBd4MDdngdGZuY1oFGUyesmXfQmSd74tzP9TkBwOzpsyF23cCxtA9k+n6OOPKZg+37fwGdAiS2
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAdminCustomerpreferredvaleterByid"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/admin/customerpreferredvaleter/{customerId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAdminCustomerpreferredvaleterByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"customerId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["resourceID","resourceName"],"type":"object","properties":{"resourceID":{"type":"integer","format":"int32"},"resourceName":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"AddPreferredValeterDTO"}},"text/json":{"schema":{"required":["resourceID","resourceName"],"type":"object","properties":{"resourceID":{"type":"integer","format":"int32"},"resourceName":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"AddPreferredValeterDTO"}},"application/*+json":{"schema":{"required":["resourceID","resourceName"],"type":"object","properties":{"resourceID":{"type":"integer","format":"int32"},"resourceName":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"AddPreferredValeterDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      