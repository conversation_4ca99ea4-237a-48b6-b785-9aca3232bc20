<?php

use App\Http\Controllers\v3\CarCategoryController;
use App\Http\Controllers\v3\CarClubSubscriptionController;
use App\Http\Controllers\v3\CarController;
use App\Http\Controllers\v3\CarLookupController;
use App\Http\Controllers\v3\PaymentIntentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| V3 API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for version 3 of your application.
| These routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group.
|
*/

// Car lookup routes
Route::prefix('car-lookup')->group(function () {
    Route::get('registration/{registrationNumber}', [CarLookupController::class, 'lookupCarByRegistration'])
        ->name('v3.car-lookup.registration');

    Route::get('make-model', [CarLookupController::class, 'searchCarsByMakeModel'])
        ->name('v3.car-lookup.make-model');
});

// Car categories routes
Route::prefix('car-categories')->group(function () {
    Route::get('/', [CarCategoryController::class, 'index'])
        ->name('v3.car-categories.index');
});

// Car CRUD routes (authenticated)
Route::middleware(['auth:sanctum'])->prefix('cars')->group(function () {
    Route::get('/', [CarController::class, 'index'])
        ->name('v3.cars.index');
    Route::get('/{id}', [CarController::class, 'show'])
        ->name('v3.cars.show');
    Route::post('/', [CarController::class, 'store'])
        ->name('v3.cars.store');
    Route::patch('/{id}', [CarController::class, 'update'])
        ->name('v3.cars.update');
    Route::delete('/{id}', [CarController::class, 'destroy'])
        ->name('v3.cars.destroy');
});

// Car Club Subscription routes (authenticated)
Route::middleware(['auth:sanctum'])->prefix('car-club/subscriptions')->group(function () {
    Route::get('/', [CarClubSubscriptionController::class, 'index'])
        ->name('v3.car-club.subscriptions.index');
    Route::post('/', [CarClubSubscriptionController::class, 'store'])
        ->name('v3.car-club.subscriptions.store');
    Route::delete('/{id}', [CarClubSubscriptionController::class, 'destroy'])
        ->name('v3.car-club.subscriptions.destroy');
    Route::post('/{id}/pause', [CarClubSubscriptionController::class, 'pause'])
        ->name('v3.car-club.subscriptions.pause');
    Route::post('/{id}/resume', [CarClubSubscriptionController::class, 'resume'])
        ->name('v3.car-club.subscriptions.resume');
});

// Payment routes
Route::prefix('payments')->group(function () {
    Route::post('/intent', [PaymentIntentController::class, 'create'])
        ->name('v3.payments.intent.create');
});

// Add other v3 API routes here
