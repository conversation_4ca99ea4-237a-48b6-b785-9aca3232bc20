---
id: create-addresses
title: "createAddresses"
description: "createAddresses"
sidebar_label: "createAddresses"
hide_title: true
hide_table_of_contents: true
api: eJztV0tv20YQ/iuLObZ0LMvIhbc0aQAXRmPUanMQdBiRI3Hj5S6zOytZJfTfi1lS1Mt2jAK5+SJx5/HN7DzJFhiXAfIpfChLTyHALANP3yMF/s2VG8hbKJxlsiyP2DRGF8ja2ctvwVmhhaKiGuVJ9LSnUuCwg7vVlq4gg8YFLlxJkAG7tRUrvGkIcnDzb1SwiHjXkGdNIVnq9G9KOfSi2jItyUMGC+dr5I50PYYMbDQG54YgZx9pmx3bz1uo8fGW7JIryK/ejzKotR3Ogy+BvbZLOFYfP6V+rPCy/ev/BZDCdKz4Cr+HOJ+YHP9Qs3DRsk8JJxtryKej7Gqfphdiz5rFb/jYI2y762upEjR3B2ldoAl0oBADu5p8X3qfJl+SLtMjv1XXW3X9nOo6HGG//PpWZm9l9hPKTLQ9hcbZ0FXCeDSSv5JC4XUjoJDDfSwKWbkiHaiIXvMG8mkLc0JPHvLpbDvLQEynepU6gsITMvXWKEB2AnrOr4krJ6oSV6lRlPDBJTb6Eg/kAvkV+ZA8iN5ADhVzE/LLS+MKNJULnLeN87w9M3orAqoDgAxW6LXUQbq6aHR3X2A0EvP3o9GVBCk7tbPwFKqLklYX2Oh3+G/0tKZ50EzhnSU+M/uJVmRcU5PlnfHnQCPyK0HvGZfaLn8EaPSKXol4510ZCzkMoLN9xu9l9HSh2uV9GBZiUjKTRCDfCWT9w+ddFf/xdZKqSMbZX/u3t98fsW4MnYyi0elg2Tfw8cR4kn59SO/6e3/ed+6eNvTkaJuBtguX7td3z2eJpfqIXn24u5HKIR+6oK3GZ3GcVDqInGq8W+mSgiJbNk5bDmrhvOKKVOn4T2KVcEX0n/E7dcPPadRou2QX6EMmv6pApqXzmnZnE+cqxPngyCF97tyDtsuQKbSlioG8wsgVWe5XTDKOxrh1SOyg2KmGvMwfNTR2UCEWlcJORnla6sAdKwEbt9Q2U0YH3jvbSZFEWVla99SSDB0IHagc3cx0jqvYJFZJjNoENd8cG7exnpPPVCD0RbWDFbEaHyj5VruSTKbS2BksPRWzM4F98GJTdryepAIjx9ClwBZkzEvIyYtdIp/IgdK2MLEUboMhrJ0vladAHJIq1aiNWpHXi13SdkuoxrS9LKbm6yarenb0Dl2bXmAbg9oKUBoebT91p4CN3jcUpe8tmazCats5Bvrbm+1WyN8jSdtMZ/uBmlZCBhVhmfZDCw+0SVsrfaJdTMQBETdRHDn7XJO5M6yDuy/3Exkl/Wde3fWtxzVk6TcHkOWTopzejITWgkG7jLgU2Q4zrcootxsicDKnkqc9C+3mwMO27SQm7oHsVhZLdyGWM2xn2+32P2ObEKg=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAddresses"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/addresses"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAddresses

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"}},"text/json":{"schema":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"}},"application/*+json":{"schema":{"required":["addressLine1","postcode","town"],"type":"object","properties":{"addressId":{"type":"integer","format":"int32","nullable":true},"addressLine1":{"maxLength":150,"minLength":1,"type":"string"},"addressLine2":{"maxLength":150,"type":"string","nullable":true},"addressLine3":{"maxLength":150,"type":"string","nullable":true},"town":{"maxLength":50,"minLength":1,"type":"string"},"postcode":{"maxLength":12,"minLength":1,"type":"string"},"country":{"enum":[0,1],"type":"integer","format":"int32","title":"Country"}},"additionalProperties":false,"title":"CustomerAddressDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      