---
id: get-cars-byid
title: "getCarsByid"
description: "getCarsByid"
sidebar_label: "getCarsByid"
hide_title: true
hide_table_of_contents: true
api: eJyNVUuP5DQQ/itWnUDyTg+DuOQGA4sGIbRiBjiM+lAdVyfWOHbWVe6hifLfUSXp104v7CWJ6/HV53plAMGGoXqGe8ywttBjxo6EsgoHiNgRVOAdWPARKuhRWrCQ6WPxmRxUkgtZ4LqlDqEaQPb95BGFGspgYZtyhzKLvr2DcVyrO/cpMrF63N3e6ssR19n34pPGeSx1TcwwjhaY6pK97CdGG8JMGarntQKlnjKqy4ODChqSe8z8w37iewl4qetI2rS4gJ1vVcEKe7+qMfNq8G4EjZx3h1SUHKCCVqTnarUKqcbQJpZq6FOW8U28X9XAzABgYYfZ4ybMN1aP+cpbLEFz893t7TcwjqP9NM42E7fvHO3eYe9v8J+S6ZU27IX4Jk7cL8P+SDsKqe8oyiH450ALyheCPgo2Pjb/Bxj8jr4Q8UNOrtR6OIKuT4V+1G6aU3Uo97GxNCQsDafnxcAuH+8P3fbLX09T8/i4TZO7l6D275Wqucdsvv/woIWhzDOn3d0bmk+tZ7UzfU4774gNRdcnH4XNNmUjLRmX5DcSM+Gq6Z93N+ZBPufRYZxzqW1m9WlqFGpS9nQ4h7IxXDZHIufyTUovPjZsDUZnClM2WKSlKL6eBmEKjiGkV57UbCSZnrKOoTmOCxsudWtwtjGZGs8yqybgkBofrQme5UR2tqKskkivi9RRoDOjM5eLm4WZuCn9pHIk6AObzf4yeCzdhrI1TJjr9gCrZh2+0MStS46CNXUmPEW6lrM3Bqfkld7NukVkWFAKzyWINYXwX8gTi0Mhr9TA+FiH4lTbI/Nrys5kYhKeXKlDH8yOst8eigajhT6xdBi1WZe125Bop7K5ttGOEyH0t6z6gD4qyjSYw7LRngF7DxY0h2Ch8k53vK4t1Q3DBpn+yGEcVfyxUNYduz5tq2nxOc/67aDaYmB6Q6NOUSjqzH31+/Jb+NqAvU5vEWLcT0sxFD2BhRfaz/+ZcT2ereeff3oCC5rdswt/MvITyau4wzBbPKUXiuN4DCN61kjj+C9bHHSM
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getCarsByid"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/cars/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getCarsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      