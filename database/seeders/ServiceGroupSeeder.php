<?php

namespace Database\Seeders;

use App\Models\ServiceGroup;
use Illuminate\Database\Seeder;

class ServiceGroupSeeder extends Seeder
{
    public function run(): void
    {
        $serviceGroups = [
            [
                'name' => 'Valet',
                'description' => 'Premium valet car cleaning services with pickup and delivery.',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'SMART',
                'description' => 'Small to Medium Area Repair Technology - minor cosmetic repairs.',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Detailing',
                'description' => 'Professional car detailing services for comprehensive vehicle care.',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Maintenance',
                'description' => 'Regular vehicle maintenance and servicing packages.',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Protection',
                'description' => 'Paint protection, ceramic coating, and vehicle protection services.',
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($serviceGroups as $serviceGroup) {
            ServiceGroup::updateOrCreate(
                ['name' => $serviceGroup['name']],
                $serviceGroup
            );
        }
    }
}
