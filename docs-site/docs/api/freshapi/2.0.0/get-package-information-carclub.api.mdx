---
id: get-package-information-carclub
title: "getPackage-informationCarclub"
description: "getPackage-informationCarclub"
sidebar_label: "getPackage-informationCarclub"
hide_title: true
hide_table_of_contents: true
api: eJyNVE1v3DgM/SsCz04mDdCLb0V3U6QoigBJu4dgDrTMsYWRJVWkJp0a/u8LyfOVTNL2Mh6Rj48USb0RBDuG+hHuUK+xI1hWEImDd0wM9QjXV1f50xLraIIY76CG+6Q1McM0VcCkUzSyhfpxhIYwUoT6cTktK/CBIuaQ2xZq6Eh2SS6MW/k4FNdHjNqmBqoXKf6EHkh6v6OFCgJKDzUsMJhFOI9b6EMgU9xQ5FJuihZq6EUC14uF9Rpt71nqMfgo01lNXzJAzQRQwQajwcbOfcoRc6NWmKxADe+vrt7BNE3VyzyrSNxftLS5wGAu8VeK9EQNGyG+dOU2z9P+QxuyPgzkZJ/8LdKE8pek94Kdcd2fCK3Z0F8y3kXfJp0PB9LlcT3udU/D3Kr9kowg20C7lHkyBQL1HlDt/tyUKUINn/97KCuXB1vCjdiMv8mlqo8Y1Ye72zwYijzXtLk+K/OhN5xxKkS/MS2xItcGb5ywWvmopCfVevlKogpvhn6/vlS38lbEgG7upcbIVf5VGoU6Hw3tzzY1ilNzKOTU3ni/Nq7jSqFrVWKKCpP05MTosr0lOVrrn7i4WYlXgWJeb3V4ZKw46V7hjFGROsMyuwqx9Z1xlbKG5VjsjKKYLY6edtaWLJ2ATkKe3czOhasUiqslQWNZNdvnyV0aGoqVYsKo+z1thg24plLb4FuyldKR8JjptZ6dAY7NS6GdfTuTYkFJPI/AabL2d8yliv0gX5mBMk7b1GZvQOYnH1sViUm4hNKAxqoNRbPaDw2mCoJnGdDlZXVYdrsjUa8Im3pDBw9vROinLIJF4zJvearjTvUeAYMpGnhGCxXslW9ZQda2DB/HBpm+RTtN2fwjUczyvTxKWlHwE5H99O8DVJA7clLSi2daFHXnQrctAmlTPo3jjHjwa3JT1tU1bfOd8hmm5TRN/wNst1Ok
sidebar_class_name: "get api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"getPackage-informationCarclub"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/package-information/carclub"}
  context={"endpoint"}
>
  
</MethodEndpoint>



getPackage-informationCarclub

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      