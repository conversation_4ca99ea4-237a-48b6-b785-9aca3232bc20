---
id: create-payment-intent
title: "Create payment intent"
description: "Creates a Stripe payment intent and returns the client secret"
sidebar_label: "Create payment intent"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create payment intent"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/v3/api/payments/intent"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Creates a Stripe payment intent and returns the client secret

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["amount"],"properties":{"amount":{"type":"integer","format":"int32","description":"Amount in the smallest currency unit (e.g., pence for GBP)","example":1000,"minimum":1}},"title":"PaymentIntentRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Payment intent created successfully","content":{"application/json":{"schema":{"type":"object","properties":{"clientSecret":{"type":"string","description":"The client secret of the payment intent","example":"pi_3NqLOILkdIwHu7ix0PG7qE1K_secret_vgMExQV4Wb4baMjGMVrYTGZ6W"}},"title":"PaymentIntentResponse"}}}},"422":{"description":"Validation error","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Validation error message"},"errors":{"type":"object","additionalProperties":{"type":"array","items":{"type":"string"}},"description":"Field-specific validation errors"}},"title":"ValidationErrorResponse"}}}},"500":{"description":"Server error","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","description":"Error message"},"data":{"type":"string","description":"Additional error data","nullable":true}},"title":"ErrorResponse"}}}}}}
>
  
</StatusCodes>


      