---
id: create-cars
title: "createCars"
description: "createCars"
sidebar_label: "createCars"
hide_title: true
hide_table_of_contents: true
api: eJztV81y2zYQfhXMHlvalt3JhbdUaWbcSRNPrbYHjw4rckUiBgEGC0hWOXz3zoLUv9L4AXyxxcX+77cfyA4CVgz5E0zRwzwDT98icfjVlRvIOyicDWSD/MS2NbrAoJ29+crOioyLmhqUX2KnPZXiqsFnem/LP1xJBsRlpTn4ZPk5NgtKgcKmJcjBLb5SESCD1ruWfNDEKW7k4BryU/T3pQhGdW0DVeQhg6XzDYZB9MsdZGCjMbgwBHnwkfqLcfMOGnz5RLYKNeTvJhk02m4fb3dJcfDaVtBnx6UcW9++wrzAQJXzm1PTyanuhfQLZ1y8lPIPLTeEp3aviNhngGWppVtoHg6msUTDlEHQQXRhuh/Nh9kXELtAL+ENE2+YOMTEIV389PMbON7AsQNHn3rNrbM8DO9uMpF/JXHhdSsOIYfHWBTEnMDEVESvwwbypw4WhF5G8zTv5xlI2DQxGTsUnjDQFD1DduLv6KihUDsxaB0nMKEUDTfY6ptiUGHyK/KcQkZvIIc6hJbzmxvjCjS145B3rfOhPwv1SRTU4AAyWKHX0rdUq1gMxS4xGsHju8nkVrqSncZZeuL6qqTVFbb6Gv+Nnta0YB2Iry2Fs7AfaEXGtQ3ZsA3+PacRwyudPgastK1+5NDoFb3S44N3ZSzkYed0vh/xozDE0KrtoHfLLCFlMkkF8q1CNv74uN3w3/+ZJdgI6/y5f6H57QWbViB5RheTy8u/h/7xWu/l+309kI17uJcM+3Ww5touXaprXJGP0kM1Ra/eP9wLYsjz0KzV3Vn/ZrVm0VOtdytdEiuyZeu0DayWzqtQkypd+ExBJb+i+vfdtboP37No0A5DFuhn8leNhWnaPpu4UBwXu0QO5QvnnrWtOFNoSxWZvMIYarJhvAFScDTGrTkdswpOteSFk9Vug1lxLGqFg446nEhybFylbaaM5rBPdtAi6ayytB6lJRk6UDowOarMDImr2KajkgJqw2qxOQ5uEx4yxYS+qLduRU1wkXJrBBmZSiSzi3SpZ2cK++bFthzORpHigCHyMAJbkDH/5zllsR3khRkobQsTSzltkXntfKk8MQVOptSgNmpFXi+3QxOkCj82mO5ui2npBh5Vlzh2t6jphbA1qK34SHzRjRz7BNjqtDme5VoXHhVp1y2Q6S9v+l7E3yLJVj3N9/SZGD+DmrBM9N/BM8niTYdvlKuZxBZ1EyWHs+8VYZkd7z98eZwJcYzfOTI+yMHjWt4/cA05gNwtqbfpdUVkHRi0VcRKdAef6RaMUtiu+BNWSpmOR2g3Bxl23aAxc89ke7lGhoKCPEM/7/v+Pz6Fwl8=
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createCars"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/cars"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createCars

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}},"text/json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}},"application/*+json":{"schema":{"required":["makeAndModel","registrationNumber"],"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32","nullable":true},"registrationNumber":{"maxLength":50,"minLength":1,"type":"string"},"makeAndModel":{"maxLength":150,"minLength":1,"type":"string"},"category":{"maxLength":10,"type":"string","nullable":true},"colour":{"maxLength":50,"type":"string","nullable":true},"year":{"maxLength":10,"type":"string","nullable":true}},"additionalProperties":false,"title":"CustomerCarDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      