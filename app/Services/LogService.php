<?php

namespace App\Services;

use App\Models\ApiLog;
use App\Services\Contracts\LogServiceInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;

class LogService implements LogServiceInterface
{

    /**
     * Store a log entry
     *
     * @param string $identifier
     * @param array $payload
     * @param array $metadata
     * @return array|null
     */
    public function storeLog(string $identifier, array $payload, array $metadata = []): ?array
    {
        try {
            $apiLog = ApiLog::createLog($identifier, $payload, $metadata);

            return [
                'id' => $apiLog->id,
                'identifier' => $apiLog->identifier,
                'created_at' => $apiLog->created_at->toISOString(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to store log', [
                'identifier' => $identifier,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Retrieve logs by identifier
     *
     * @param string $identifier
     * @param array $options
     * @return array|null
     */
    public function getLogsByIdentifier(string $identifier, array $options = []): ?array
    {
        try {
            $limit = $options['limit'] ?? 50;
            $logs = ApiLog::byIdentifier($identifier)
                ->recent($limit)
                ->get();

            return [
                'documents' => $logs->toArray(),
                'count' => $logs->count(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to retrieve logs by identifier', [
                'identifier' => $identifier,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Retrieve logs with filters
     *
     * @param array $filters
     * @param array $options
     * @return array|null
     */
    public function getLogs(array $filters = [], array $options = []): ?array
    {
        try {
            $query = ApiLog::query();

            // Apply filters
            if (isset($filters['identifier'])) {
                $query->byIdentifier($filters['identifier']);
            }

            if (isset($filters['created_at'])) {
                // Assuming created_at filter is a date range
                if (is_array($filters['created_at']) && count($filters['created_at']) === 2) {
                    $query->byDateRange(
                        Carbon::parse($filters['created_at'][0]),
                        Carbon::parse($filters['created_at'][1])
                    );
                }
            }

            // Apply options
            $limit = $options['limit'] ?? 50;
            $logs = $query->recent($limit)->get();

            return [
                'documents' => $logs->toArray(),
                'count' => $logs->count(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to retrieve logs', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
