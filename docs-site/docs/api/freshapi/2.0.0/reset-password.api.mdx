---
id: reset-password
title: "resetPassword"
description: "resetPassword"
sidebar_label: "resetPassword"
hide_title: true
hide_table_of_contents: true
api: eJztVktvGzcQ/ivEHFvacgz0sqemSQO4CBrBVtuDocNoOdIy5pIbcihHXex/L4arx1pOGt9yyWUfnJlvXpyP7CF0FJFt8DcGKoiUiOeY0mOIBjQwbhJU9/C6rkP2DEsNkT5lSvxbMDuoeqiDZ/Isn9h1ztYFbPYxBS9rqW6oRfkSOxvJCFwdDIEGatE60NAdHC418K4jqCCsPlLNIosSIVtKAjJaVD201r8nv+EGqldHo8TR+g1oWIfYIkO11x8mLl5uezQZtGS5trGdT1C+bafBZ+dw5QgqjpkKjqFvhTAMGtAYK3VEN5/kv0aXSANbFki4nTbr7eJDsWT6zD+q/72qPx2Bn37+0Ybv0gaxjZS64NNYsOurK3kZSnW0nUBCBXe5riml4ilRnaPlHVT3PawII0Wo7pfDUp8ZnfNjS9wEIc4upNIolGxghp2d7TlzdntmkyhuKabiK0cHFTTMXapmMxdqdE1IXPVdiDzAufv3oqBGANCwxWilsiVJsRizXGN20oNfrq5eSTn0uZ91pNRcGNpeYGcv8d8c6ZFWyTKlS0/8zO1b2pILXUueD86/BpqRXwh6x7ixfvMtQGe39ELEeQwm1/JzBF2eensnQziW6tDh4/4Vl9KZogLVQUHvP94ddvUf/yzKfpHBvj2dg79/xrZzNJlNyInirzSuX9ahnQ74ZF6ejdRUZKbzoMH6dSgx73f+O6mPeoNRvZ7fyG6gmMZCbK+f1WbR2CR6qothaw0lRd50wXpOah2i4oaUCfwnsSq4ovr39aW64a9ZtOjHBtYYk5anqpFpE6Klw7/LK5Xy6hjIdH0VwoP1m6QVeqOkXgozN+R5T6DFOToXHlMRJ8VBdRSFY9Tx2pJUynWjcNRRkTY28SgqwC5srNfK2cSnYEctksoqT4/7VUOOJkoTkyeZuTFwlbsiMsRoXVKr3VPnPrcrilolwlg3B1hRa/GBSmxtMOS0qiPhydOXavZM4VS83JlRtl9SiZFzGlvga3Lu/5BLFIdGfqEHyvraZSPSw/ZVhQNTMS27XW0p2vWhaeWcCYlbLEefxzJQxUZNSPDJ7jzOYbm8dA6tF5hCB/2eU+8BOwv6eBPVT5lfTk1hTlHs+xUm+iu6YZDlT5miMPvyRJiF3DU0hKYwfQ8PtIMK3oyX2YuFhCPqLktYzy62witH6p9/uFsIVewvxO04txEfQZdnBaAhjBUvdwBZ68Gh32TciO6IWY68LLke63HGQyXSvQj9bhJh348ai/BAfpCDY0yI5R+G5TAM/wG8RS+j
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"resetPassword"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/Account/ResetPassword"}
  context={"endpoint"}
>
  
</MethodEndpoint>



resetPassword

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["code","email","password"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true},"code":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"ResetPasswordDTO"}},"text/json":{"schema":{"required":["code","email","password"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true},"code":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"ResetPasswordDTO"}},"application/*+json":{"schema":{"required":["code","email","password"],"type":"object","properties":{"email":{"minLength":1,"type":"string","format":"email"},"password":{"minLength":1,"type":"string","format":"password"},"confirmPassword":{"type":"string","format":"password","nullable":true},"code":{"minLength":1,"type":"string"}},"additionalProperties":false,"title":"ResetPasswordDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      