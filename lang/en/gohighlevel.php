<?php

return [
    'locations' => 'Locations',
    'create_location' => 'Create Location',
    'edit_location' => 'Edit Location',
    'location_id' => 'Location Id',
    'client_id' => 'App Client Id',
    'client_secret' => 'App Client Secret',
    'private_integration_key' => 'Private Integration Key',
    'base_url_authorization' => 'Api Base URL Authorization',
    'base_url_api' => 'Api Base URL',
    'import_locations' => 'Import Locations',
    'import_locations_description' => 'Import all locations from GoHighLevel API using the token configured in your environment.',
    'using_env_token' => 'This will use the API token configured in your .env file (GOHIGHLEVEL_API_TOKEN).',
    'import' => 'Import Locations',
];
