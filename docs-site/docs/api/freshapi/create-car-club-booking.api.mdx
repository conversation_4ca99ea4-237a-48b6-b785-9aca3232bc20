---
id: create-car-club-booking
title: "Create Car club booking"
description: "This endpoint allows you to create a new car club booking. The booking details are provided in the request body."
sidebar_label: "Create Car club booking"
hide_title: true
hide_table_of_contents: true
api: 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
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create Car club booking"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/v3/api/car-club/bookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



This endpoint allows you to create a new car club booking. The booking details are provided in the request body.

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"carClubBooking":{"type":"object","properties":{"addressId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"confirmedDate":{"type":"string","format":"date-time"},"preferredTime":{"type":"string","format":"date-time","nullable":true},"additionalComments":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"isPrepaid":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"bookingHubDurationMinutes":{"type":"string","nullable":true},"carClubBookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"CarClubBookingCustomerCarPackageDTO"},"nullable":true}},"additionalProperties":false,"title":"CreateCarClubBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateCarClubBookingRequestDTO"}},"text/json":{"schema":{"type":"object","properties":{"carClubBooking":{"type":"object","properties":{"addressId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"confirmedDate":{"type":"string","format":"date-time"},"preferredTime":{"type":"string","format":"date-time","nullable":true},"additionalComments":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"isPrepaid":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"bookingHubDurationMinutes":{"type":"string","nullable":true},"carClubBookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"CarClubBookingCustomerCarPackageDTO"},"nullable":true}},"additionalProperties":false,"title":"CreateCarClubBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateCarClubBookingRequestDTO"}},"application/*+json":{"schema":{"type":"object","properties":{"carClubBooking":{"type":"object","properties":{"addressId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"confirmedDate":{"type":"string","format":"date-time"},"preferredTime":{"type":"string","format":"date-time","nullable":true},"additionalComments":{"type":"string","nullable":true},"resourceId":{"type":"integer","format":"int32"},"isPrepaid":{"type":"boolean"},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"bookingHubDurationMinutes":{"type":"string","nullable":true},"carClubBookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"customerCarId":{"type":"integer","format":"int32"},"customerCarCategory":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true},"customerCarClubPackageId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"CarClubBookingCustomerCarPackageDTO"},"nullable":true}},"additionalProperties":false,"title":"CreateCarClubBookingDTO"},"paymentDetails":{"type":"object","properties":{"isPayNow":{"type":"boolean"},"paymentMethodId":{"type":"string","nullable":true},"paymentIntentId":{"type":"string","nullable":true},"paymentIntentClientSecret":{"type":"string","nullable":true},"requiresAction":{"type":"boolean"}},"additionalProperties":false,"title":"PaymentDetailsDTO"}},"additionalProperties":false,"title":"CreateCarClubBookingRequestDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"201":{"description":"Booking created successfully"}}}
>
  
</StatusCodes>


      