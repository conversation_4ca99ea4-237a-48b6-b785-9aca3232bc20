---
id: create-admin-allbookings
title: "createAdminAllbookings"
description: "createAdminAllbookings"
sidebar_label: "createAdminAllbookings"
hide_title: true
hide_table_of_contents: true
api: eJztV01v4zYQ/SvEHFs5dtwG2+qWTXaBFP0INm4LNPBhLI0tbihSSw7tdQX992Io+SPxJs11gb0kEvnmzXCG80ZugXEVIL+HS2PeOveg7SpclrW2MM/A06dIgd+6cgt5C4WzTJblEZvG6AJZOzv+GJyVtVBUVKM8iZ32VArvoie9KSEDsuU1Ms10TSDswUVfUNoKjJ73m/MMeNsQ5OAWH6lgyKDxriHPmoJ4OLDm7Q6qLdOKPGSwdL5G7pd+mEL3lP5gE9hruzo2KZFpxILqHgf8aqOjc70uuDoyXTm71LKhnX1XozZHtgvnDKGFrssAy1ILBs3tUUKWaAJlwJqNGKQC/oY24q6m1+ySOdNn/lawr6hgx4323fffKve1VK7ro2ucDX0ap5OJ/CspFF43wgs53MWioBCSu0BF9Jq3kN+3sCD05CG/n3fzDMR7ClfOCYUnZEqOL40ZyhMge8L9LKwmrpwQNS6kciNXkMMYGz1GgY/xET6QX5MPKa7oDeRQMTchH4+NK9BULnDeNs5zdxLDrwJQPQFksEavcWH6hIhFn5ElRiNVuphMziV12VM/S0+hGpW0HmGjz/Df6GlDi6CZwpklPnF7TWsyrqnJ8s75c6QR+ZWkd4wrbVf/R2j0ml7JeOtdGQt52ZPOD/fgTnp8aMHhNuyvqbiUyiSIXNsekA0P73f3/pe/Z+luiW58OAzzd5+xbgw96e7JSe/CdDL9cTR5M5r+PDu/yC/O8+lPZ5M35/88EZgXgcc9Onm2A9lH6jLQdunSOYf2ei85VVfo1eXtjdwg8qFP3np6ks9ZpYPgVOPdWpcUFNmycdpyUEvnFVekSse/E6vEK9C/pmfqhp+zqNH2RS/Qh0z+qgKZVs5r2r2buFAhLvaBHK/vuihTaEsVA3mFkSuyPGh6co7GuE1I20GxUw15US61b/ugQiwqhT1GeVrpwP1WIjZupW2mjA58CLZHkeinsrQZVksydAQ6Mnl0MtMHrmKTtkpi1CaoxfaxcxvrBflMBUJfVDtagdX4QCm22pVkMpXUaO/pSzk7ARySF5uy3xuWVGDkGPoS2IKMeYk5RbEr5BdqoLQtTCxlt8EQNs6XylMgDsmU5IKqNXm93BVNJomIZ41pGltMPdALrkqKq15Q5n0Xp4+xxqC2QpjEpB3U+B6w0SAzSD7FMzhW5HkGorkCatsFBvrTm66T5U+RvMyP+UFq0wjJoCIs0zxp4YG2kMNV/y0/mkkoAjdRQjr5rhdF2g+M2z/uZiIyw+8BKS3k4HEjbY4byAFkWKW8p48TWWvBoF1FXAm250zTNco5D4P3sYKlSIcttNujCNu2R8zcA9lORk5/IJZ36OZd1/0H2Gh7fA==
sidebar_class_name: "post api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"createAdminAllbookings"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/admin/allbookings"}
  context={"endpoint"}
>
  
</MethodEndpoint>



createAdminAllbookings

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["bookingId","endDateTime","resourceId","startDateTime"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32"},"startDateTime":{"type":"string","format":"date-time"},"endDateTime":{"type":"string","format":"date-time"},"resourceId":{"type":"integer","format":"int32"},"muteConfirmationEmail":{"type":"boolean"}},"additionalProperties":false,"title":"AdminManualBookingDto"}},"text/json":{"schema":{"required":["bookingId","endDateTime","resourceId","startDateTime"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32"},"startDateTime":{"type":"string","format":"date-time"},"endDateTime":{"type":"string","format":"date-time"},"resourceId":{"type":"integer","format":"int32"},"muteConfirmationEmail":{"type":"boolean"}},"additionalProperties":false,"title":"AdminManualBookingDto"}},"application/*+json":{"schema":{"required":["bookingId","endDateTime","resourceId","startDateTime"],"type":"object","properties":{"bookingId":{"type":"integer","format":"int32"},"startDateTime":{"type":"string","format":"date-time"},"endDateTime":{"type":"string","format":"date-time"},"resourceId":{"type":"integer","format":"int32"},"muteConfirmationEmail":{"type":"boolean"}},"additionalProperties":false,"title":"AdminManualBookingDto"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      