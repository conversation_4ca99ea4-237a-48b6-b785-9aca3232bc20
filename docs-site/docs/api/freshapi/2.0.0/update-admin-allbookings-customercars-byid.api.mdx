---
id: update-admin-allbookings-customercars-byid
title: "updateAdminAllbookingsCustomercarsByid"
description: "updateAdminAllbookingsCustomercarsByid"
sidebar_label: "updateAdminAllbookingsCustomercarsByid"
hide_title: true
hide_table_of_contents: true
api: eJztWEtz2zYQ/iuYPfVBW6o7vegmO0nrTpt4Yqc9aHRYESsJMQgweMhRNPzvnQUokbLkVk2P1UUiwX3v4lssNhBw4WE0gbHW19Y+KrPwY1kpA9MCanRYUSDHFBswWBGMYJbJbiUUoAyMoMawhAIcfYrKkYRRcJEK8OWSKoTRBsK6ZkZlAi3IQQFz6yoMeenHK2iaaWYnH66tXDNPaU0gE/gR61qrEoOyZvDRW8NrnfBO7WRr2k30wVbkbtB59qPVb2cfqQxQQO1sTS4o8izgGFNnNDqHa/Y0UNVfP1nYrTwpBEUvrqfRl1+ho8byERf0s7OxPpnJ0UL54FIC3sZqRq7H6INTZgEFmKg1zjTl9DcFVPhIYyN/t5L0SQwlBlpYtz6N2GobTzNkTXgaoa3ZRdSvPweH/6EI2ijfBqpODHJTAEqpsvq7nrA5ak8FBBXYTmi36F2n4F3f5lcP7zhhzxz7l8J7tdvqSWI5PityTkm6c6qknl8mF0XPLWkj6/9qUz7UEgMdGuS3pgT6HM5gcAaDMxicwWDvgPDd92dUOKPCGRX+76jQpKr0tTU+R/xqOOQ/Sb50KkUERnAfy5K8T6Z7KqNTYZ1mnRmh4yKeTHk2YSNSbXOuICbNaUoaa93uSb81oUTnr9eKp6N9XSezVRSWVubBquTJKg1YIxhgrQbI/APsBAzKnoTBZgcRDbBLbrWd3qLTMIJlCLUfDQbalqiX1ofRprYuNAfW/sYEIguAAlboFCcpF691IcdyjlFzMn8aDn/goBfP9cwd+eWFpNUF1uoSv0RHTzTzKpC/NBQO1L6iFWlbV2TCVvlLQiOGE4XeB1wos/gngVqt6ESJd87KWPLLTui0q6B77jwt2Ld1tNsIrBLa4ThN05mgaB/ebLfHr38+pKrkbva+G41ff8aq1vRiU5q81F+Ge91jeNAbhofIPzyO6x067sN3t96hdG+tBeNuJWNu9/4cWicHKDls8m7cx5hhw/cQc5uC3ILDG06ouEEnxne3XL7kfM7c6uogmQ9L5ZlO1M6ulCQvyMjaKhO8mFsnwpKEtOEtBZHkMukfV5fiNrzEUaHJFcd7suBf0cZE0fZdx5nwcbYzpL++3duFQCNF9OQExrAkE9pjTlKOWtsnnz57EayoyTG6ih1aeeFjuRSYaUQ/mUmwtgtlCqGVD52xmYo4KcLQU7sqSVOPqMey55nOhotYp0+SAirtxWy9rzx3g0J4Qlcut2KZjEsq2VZxURWidISdpmMxOyDogpcQl7+1S8IHDNHnFJiStP47ycmKbSKP5EAoU+oo+WuN3j9ZJ4UjT8EnVqpQabEip+bbpKXjlfWhwnRAbe/Tcl8QqTGIXmcQ/dYgjrWUHaikabjWqAyrSNi2abvGBLBWwD2VL/UK6HUO6ECAVUABow4ipgVwe2ABm80MPX1wuml4+VMk3tqTadcV0maVyvOz3HXqZ7bubvTgm/ftgfxbAcVxH7anKrNOzUdHfoMCHmm9dwXZTJsCloQyNetN+/0mq7p4YCkd/8FFIqPJrtvejR9ufmEgbm8guQJhBA6f+HITn7IBGaUStqe1DWg0i4gLps1C0xEmcvB3CXqG8snUoy5uNpniwT6SaZqdx4Hf2dum+Qs2AKeg
sidebar_class_name: "patch api-method"
info_path: docs/api/freshapi/2.0.0/fresh-car-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"updateAdminAllbookingsCustomercarsByid"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/admin/allbookings/customercars/{bookingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



updateAdminAllbookingsCustomercarsByid

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"bookingId","in":"path","required":true,"schema":{"type":"integer","format":"int32"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["bookingCustomerCars"],"type":"object","properties":{"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"}},"overridePrice":{"type":"number","format":"double","nullable":true}},"additionalProperties":false,"title":"UpdateBookingCustomerCarsDTO"}},"text/json":{"schema":{"required":["bookingCustomerCars"],"type":"object","properties":{"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"}},"overridePrice":{"type":"number","format":"double","nullable":true}},"additionalProperties":false,"title":"UpdateBookingCustomerCarsDTO"}},"application/*+json":{"schema":{"required":["bookingCustomerCars"],"type":"object","properties":{"bookingCustomerCars":{"type":"array","items":{"type":"object","properties":{"bookingCustomerCarId":{"type":"integer","format":"int32"},"bookingId":{"type":"integer","format":"int32"},"customerCarId":{"type":"integer","format":"int32"},"packageGroupId":{"type":"integer","format":"int32"},"registrationNumber":{"type":"string","nullable":true},"makeAndModel":{"type":"string","nullable":true},"category":{"type":"string","nullable":true},"colour":{"type":"string","nullable":true},"year":{"type":"string","nullable":true},"optionalExtras":{"type":"array","items":{"type":"object","properties":{"packageItemId":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"BookingPackageItemOptionalExtraDTO"},"nullable":true}},"additionalProperties":false,"title":"BookingCustomerCarPackageDTO"}},"overridePrice":{"type":"number","format":"double","nullable":true}},"additionalProperties":false,"title":"UpdateBookingCustomerCarsDTO"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      